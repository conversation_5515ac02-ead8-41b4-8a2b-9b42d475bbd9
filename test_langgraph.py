"""
LangGraph 워크플로우 테스트 스크립트

이 스크립트는 LangGraph 기반 Text-to-SQL 워크플로우를 테스트합니다.
"""

import os
import json
import logging
from dotenv import load_dotenv
from modules.langraph_workflow import process_query_with_langgraph

# 로깅 설정
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 환경 변수 로드
load_dotenv()

# 기본 스키마 정보
DEFAULT_SCHEMA = """
CREATE TABLE users (
    id INT PRIMARY KEY,
    name VARCHAR(100),
    age INT,
    email VARCHAR(100),
    created_at DATETIME
);

CREATE TABLE orders (
    id INT PRIMARY KEY,
    user_id INT,
    product_name VARCHAR(100),
    amount DECIMAL(10, 2),
    order_date DATETIME,
    FOREIGN KEY (user_id) REFERENCES users(id)
);
"""

def test_langgraph_workflow():
    """
    LangGraph 워크플로우 테스트 함수
    """
    # API 키 및 DB 설정 가져오기
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        print("오류: OPENAI_API_KEY 환경 변수가 설정되지 않았습니다.")
        return

    # 데이터베이스 연결 설정
    db_config = {
        "host": os.getenv("DB_HOST", "localhost"),
        "port": int(os.getenv("DB_PORT", "3306")),
        "user": os.getenv("DB_USER", "root"),
        "password": os.getenv("DB_PASSWORD", ""),
        "database": os.getenv("DB_NAME", "test")
    }

    # 스키마 정보
    schema_info = DEFAULT_SCHEMA

    # 테스트 쿼리
    test_queries = [
        "users 테이블에 있는 모든 사용자를 조회해줘",
        "나이가 30세 이상인 사용자의 수를 알려줘",
        "각 사용자별 주문 금액의 합계를 보여줘"
    ]

    # 테스트에서 사용할 모델 정의
    sql_model = "gpt-3.5-turbo"  # 정확한 모델명 지정
    analysis_model = "gpt-3.5-turbo"  # 정확한 모델명 지정
    
    # 모델 선택 로깅
    logger.info(f"SQL 생성에 사용할 모델: {sql_model}")
    logger.info(f"데이터 분석에 사용할 모델: {analysis_model}")

    # 각 쿼리에 대해 LangGraph 워크플로우 실행
    for i, query in enumerate(test_queries):
        print(f"\n테스트 {i+1}: '{query}'")
        print("-" * 50)

        try:
            # 모델 선택 로깅
            logger.info(f"테스트 {i+1} 실행 - SQL 모델: {sql_model}, 분석 모델: {analysis_model}")
            
            # LangGraph 워크플로우 실행
            result = process_query_with_langgraph(
                query=query,
                schema_info=schema_info,
                api_key=api_key,
                db_config=db_config,
                sql_model=sql_model,  # 테스트에서는 저비용 모델 사용
                analysis_model=analysis_model
            )

            # 결과 출력
            print("생성된 SQL:", result.get("sql", "없음"))
            print("테이블:", result.get("tables_used", []))
            
            # 데이터 존재 여부
            if result.get("data") is not None:
                print(f"데이터: {len(result['data'])}개 행 반환됨")
            
            # 오류 확인
            if result.get("error"):
                print("오류:", result.get("error"))
            
            print("-" * 50)
        except Exception as e:
            logger.error(f"워크플로우 실행 중 오류 발생: {str(e)}")
            print(f"워크플로우 실행 중 오류 발생: {str(e)}")

if __name__ == "__main__":
    print("LangGraph 워크플로우 테스트 시작...")
    test_langgraph_workflow()
    print("테스트 완료!") 