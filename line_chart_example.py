import streamlit as st
import pandas as pd
import numpy as np
import sys
import os

# 모듈 경로 추가
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from modules.visualization import st_generate_line_chart

st.title("Streamlit 라인 차트 예제")
st.caption("st_generate_line_chart 함수 활용")

# 예제 1: 기본 라인 차트
st.subheader("1. 기본 라인 차트")

# 랜덤 데이터 생성
chart_data = pd.DataFrame(
    np.random.randn(20, 3), 
    columns=["서울", "부산", "대구"]
)

# 기본 라인 차트 그리기
st_generate_line_chart(chart_data)

# 예제 2: x축과 y축 지정하기
st.subheader("2. x축과 y축 지정하기")

# 임의의 날짜 데이터 생성
dates = pd.date_range("2023-01-01", periods=20, freq="D")
sales_data = pd.DataFrame({
    "날짜": dates,
    "서울 매출": np.random.randint(100, 500, 20),
    "부산 매출": np.random.randint(80, 400, 20),
    "대구 매출": np.random.randint(50, 300, 20)
})

# x축과 y축을 지정한 라인 차트
st_generate_line_chart(
    sales_data,
    x="날짜",
    y=["서울 매출", "부산 매출", "대구 매출"], 
    height=400
)

# 예제 3: 축 레이블 지정하기
st.subheader("3. 축 레이블 지정하기")

st_generate_line_chart(
    sales_data,
    x="날짜",
    y=["서울 매출", "부산 매출"],
    x_label="판매 날짜",
    y_label="일일 매출 (만원)",
    height=400
)

# 예제 4: 색상 지정하기
st.subheader("4. 색상 지정하기")

# 색상을 명시적으로 지정한 라인 차트
st_generate_line_chart(
    sales_data,
    x="날짜",
    y=["서울 매출", "부산 매출", "대구 매출"],
    color=["#FF0000", "#00FF00", "#0000FF"], # 빨강, 초록, 파랑
    height=400
)

# 예제 5: 인터랙티브 차트 (add_rows 기능)
st.subheader("5. 인터랙티브 차트 (add_rows 기능)")

# 초기 데이터
initial_data = pd.DataFrame(
    np.random.randn(5, 3),
    columns=["A", "B", "C"]
)

# 차트 생성
chart = st_generate_line_chart(initial_data, height=300)

# 버튼 클릭시 새 데이터 추가
if st.button("새 데이터 추가"):
    # 새 데이터 생성
    new_data = pd.DataFrame(
        np.random.randn(5, 3),
        columns=["A", "B", "C"]
    )
    # 차트에 새 데이터 추가
    chart.add_rows(new_data)
    st.write("새 데이터가 차트에 추가되었습니다!") 