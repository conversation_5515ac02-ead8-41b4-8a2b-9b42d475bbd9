# 시스템 패턴

## 아키텍처 개요

이 애플리케이션은 **모듈화된 마이크로서비스 아키텍처**와 **상태 기반 워크플로우 패턴**을 결합한 구조를 가지고 있습니다. LangGraph를 중심으로 한 워크플로우 관리와 DynamoDB 기반 데이터 지속성을 통해 확장 가능하고 안정적인 시스템을 구축했습니다.

## 핵심 아키텍처 패턴

### 1. 상태 기반 워크플로우 패턴 (LangGraph)

```
GraphState → Node Functions → State Updates → Next Node
     ↑                                           ↓
     ←←←←←←←←← Conditional Routing ←←←←←←←←←←←←←←←
```

- **상태 중심 설계**: 모든 워크플로우 상태가 `GraphState` 객체에 중앙화
- **노드 기반 처리**: 각 기능을 독립적인 노드로 분리
- **조건부 라우팅**: 실행 결과에 따른 동적 흐름 제어
- **상태 지속성**: DynamoDB를 통한 워크플로우 상태 영구 저장

### 2. 계층화된 모듈 구조

```
UI Layer (Streamlit)
    ↓
Service Layer (Session/Message Managers)
    ↓
Data Access Layer (CRUD Operations)
    ↓
Infrastructure Layer (DynamoDB/MySQL)
```

#### UI Layer

- `ui/app.py`: 메인 애플리케이션 진입점
- `ui/sidebar.py`: 세션 관리 및 히스토리 UI
- `ui/message.py`: 메시지 렌더링 컴포넌트

#### Service Layer

- `session_manager.py`: 세션 생명주기 관리
- `message_history_manager.py`: 메시지 히스토리 서비스
- `langgraph_state_manager.py`: 워크플로우 상태 관리

#### Data Access Layer

- `dynamodb_crud.py`: DynamoDB CRUD 작업
- `db_connector.py`: MySQL 연결 및 쿼리

#### Infrastructure Layer

- `dynamodb_connector.py`: DynamoDB 연결 관리
- `server_id_manager.py`: 서버 식별 및 격리

### 3. 데이터 지속성 패턴

#### 이중 데이터베이스 전략

```
MySQL (분석 대상 데이터)
    ↓ 쿼리 실행
LangGraph 워크플로우
    ↓ 상태 저장
DynamoDB (세션/히스토리 데이터)
```

- **MySQL**: 비즈니스 데이터 (읽기 전용)
- **DynamoDB**: 애플리케이션 상태 및 히스토리 (읽기/쓰기)

#### 데이터 모델 패턴

```
Sessions Table:
PK: server_id | SK: session_id
- 서버별 세션 격리
- 세션 메타데이터 관리

Messages Table:
PK: server_id#session_id | SK: timestamp
- 세션별 메시지 그룹화
- 시간순 정렬 보장
```

### 4. 캐싱 및 성능 패턴

#### 다층 캐싱 전략

```
Memory Cache (Python Dict)
    ↓ Cache Miss
Session Cache (Instance Level)
    ↓ Cache Miss
DynamoDB (Persistent Storage)
```

- **L1 캐시**: 메모리 기반 빠른 액세스
- **L2 캐시**: 세션 수준 중간 캐시
- **L3 저장소**: DynamoDB 영구 저장

#### 압축 및 최적화

- **자동 압축**: 1KB 이상 데이터 zlib 압축
- **배치 처리**: DynamoDB 배치 작업 활용
- **지연 로딩**: 필요 시점에 데이터 로드

## 주요 디자인 패턴

### 1. Factory Pattern (연결 관리)

```python
class DynamoDBConnector:
    @classmethod
    def get_instance(cls):
        # 싱글톤 패턴으로 연결 인스턴스 관리

class SessionManager:
    def __init__(self, crud_instance):
        # 의존성 주입을 통한 느슨한 결합
```

### 2. Strategy Pattern (데이터 직렬화)

```python
def _serialize_data(self, data):
    try:
        return json.dumps(data)  # JSON 우선
    except:
        return pickle.dumps(data)  # Pickle 대안
```

### 3. Observer Pattern (상태 변경 알림)

```python
# Streamlit 세션 상태와 DynamoDB 동기화
if st.session_state.current_session_id != previous_session:
    self._load_session_history()
    self._update_ui_state()
```

### 4. Command Pattern (CRUD 작업)

```python
class DynamoDBCRUD:
    def create_session(self, **kwargs):
        # 세션 생성 명령

    def update_session(self, **kwargs):
        # 세션 업데이트 명령
```

## 상태 관리 패턴

### 1. 상태 분리 원칙

```
UI State (Streamlit Session)
    - 현재 세션 ID
    - UI 컴포넌트 상태
    - 임시 사용자 입력

Application State (DynamoDB)
    - 세션 메타데이터
    - 메시지 히스토리
    - 워크플로우 체크포인트

Workflow State (LangGraph)
    - 현재 노드 상태
    - 처리 중인 데이터
    - 다음 실행 노드
```

### 2. 상태 동기화 패턴

```python
# 상태 변경 시 자동 동기화
def _sync_states(self):
    # UI → Application State
    self._save_to_dynamodb()

    # Application State → UI
    self._update_streamlit_session()

    # Workflow State → Application State
    self._save_workflow_checkpoint()
```

## 오류 처리 패턴

### 1. 계층별 오류 처리

```
UI Layer: 사용자 친화적 메시지
    ↓
Service Layer: 비즈니스 로직 검증
    ↓
Data Layer: 데이터 무결성 확인
    ↓
Infrastructure: 연결 및 리소스 관리
```

### 2. 재시도 패턴 (Exponential Backoff)

```python
@retry_with_exponential_backoff
def _execute_with_retry(self, operation):
    # DynamoDB 스로틀링 대응
    # 네트워크 오류 복구
    # 일시적 장애 처리
```

### 3. Circuit Breaker Pattern

```python
class ConnectionManager:
    def __init__(self):
        self.failure_count = 0
        self.circuit_open = False

    def execute(self, operation):
        if self.circuit_open:
            return self._fallback_response()
        # 정상 실행 로직
```

## 보안 패턴

### 1. 데이터 격리 패턴

```
Server ID → 서버별 완전 격리
    ↓
Session ID → 세션별 데이터 분리
    ↓
Message Level → 메시지별 접근 제어
```

### 2. 암호화 패턴

- **전송 중 암호화**: HTTPS/TLS
- **저장 시 암호화**: DynamoDB 관리형 암호화
- **애플리케이션 레벨**: 민감 데이터 추가 암호화

### 3. 접근 제어 패턴

```python
def _validate_access(self, server_id, session_id):
    # 서버 ID 검증
    # 세션 소유권 확인
    # 권한 레벨 검사
```

## 확장성 패턴

### 1. 수평 확장 패턴

```
Load Balancer
    ↓
Multiple App Instances (Stateless)
    ↓
Shared DynamoDB (Stateful)
```

- **무상태 애플리케이션**: 서버 간 상태 공유 없음
- **상태 외부화**: DynamoDB에 모든 상태 저장
- **서버별 격리**: 서버 ID 기반 데이터 분할

### 2. 데이터 파티셔닝 패턴

```
DynamoDB Partition Key: server_id
    - 서버별 자동 분산
    - 핫 파티션 방지
    - 균등한 부하 분산
```

### 3. 비동기 처리 패턴

```python
# 스트리밍 응답과 상태 저장 분리
async def process_query():
    # 실시간 응답 스트리밍
    yield response_chunk

    # 백그라운드 상태 저장
    asyncio.create_task(save_state())
```

## 모니터링 및 관찰성 패턴

### 1. 구조화된 로깅

```python
logger.info("Session created", extra={
    "server_id": server_id,
    "session_id": session_id,
    "timestamp": datetime.utcnow(),
    "action": "session_create"
})
```

### 2. 메트릭 수집 패턴

- **성능 메트릭**: 응답 시간, 처리량
- **비즈니스 메트릭**: 세션 수, 메시지 수
- **시스템 메트릭**: 메모리, CPU, 네트워크

### 3. 분산 추적 (LangSmith)

```python
# LangGraph 워크플로우 추적
with trace_context("text_to_sql_workflow"):
    result = workflow.invoke(state)
```

## 테스트 패턴

### 1. 계층별 테스트 전략

```
Unit Tests: 개별 함수/클래스
    ↓
Integration Tests: 모듈 간 상호작용
    ↓
System Tests: 전체 워크플로우
    ↓
Performance Tests: 부하 및 성능
```

### 2. 모킹 패턴

```python
@pytest.fixture
def mock_dynamodb():
    with mock_dynamodb():
        yield create_test_tables()
```

이러한 패턴들은 시스템의 **유지보수성**, **확장성**, **안정성**을 보장하며, 복잡한 Text-to-SQL 워크플로우와 채팅 히스토리 관리를 효율적으로 처리할 수 있게 합니다.
