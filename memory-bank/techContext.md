# 기술 컨텍스트

## 기술 스택

### 프론트엔드
- **Streamlit**: 웹 애플리케이션 프레임워크
- **CSS/HTML**: 커스텀 스타일링 및 레이아웃
- **JavaScript**: 클라이언트 사이드 상호작용 (Streamlit 내장)

### 백엔드
- **Python 3.13**: 메인 프로그래밍 언어
- **LangGraph**: 워크플로우 관리 및 상태 기반 그래프 처리
- **OpenAI API**: GPT-4o 모델을 통한 자연어 처리 및 SQL 생성
- **LangSmith**: AI 모델 추적 및 모니터링

### 데이터베이스
- **MySQL**: 주 데이터베이스 (쿼리 대상)
- **AWS DynamoDB**: 채팅 히스토리 및 세션 관리 (신규 추가)
  - 로컬 개발: DynamoDB Local (Docker)
  - 프로덕션: AWS DynamoDB 클라우드

### 데이터 처리 및 시각화
- **pandas**: 데이터 조작 및 분석
- **numpy**: 수치 계산
- **matplotlib**: 기본 차트 생성
- **seaborn**: 통계적 시각화
- **plotly**: 인터랙티브 차트

### 패키지 관리 및 개발 도구
- **uv**: 빠른 Python 패키지 관리자
- **Docker**: 컨테이너화 및 개발 환경 관리
- **Docker Compose**: 다중 컨테이너 애플리케이션 관리

### 새로 추가된 기술 스택 (채팅 히스토리 시스템)
- **boto3**: AWS SDK for Python (DynamoDB 연결)
- **botocore**: AWS 서비스 핵심 라이브러리
- **zlib**: 데이터 압축/해제 (메시지 최적화)
- **hashlib**: 서버 ID 생성 및 데이터 해싱
- **uuid**: 고유 식별자 생성
- **socket**: 네트워크 정보 수집 (서버 ID 생성용)

## 개발 환경

### 로컬 개발
- **Python 가상환경**: `.venv` 디렉토리
- **환경 변수**: `.env` 파일 또는 `.streamlit/secrets.toml`
- **DynamoDB Local**: Docker 컨테이너 (포트 8000)
- **DynamoDB Admin**: 웹 UI (포트 8001)

### 의존성 관리
- **pyproject.toml**: 프로젝트 메타데이터 및 의존성 정의
- **uv.lock**: 정확한 의존성 버전 고정
- **requirements.txt**: pip 호환성을 위한 의존성 목록

### 새로 추가된 환경 설정
```env
# DynamoDB 설정
DYNAMODB_ENDPOINT_URL=http://localhost:8000  # 로컬 개발용
AWS_REGION=ap-northeast-2
AWS_ACCESS_KEY_ID=test  # 로컬 개발용
AWS_SECRET_ACCESS_KEY=test  # 로컬 개발용

# 서버 식별 설정 (선택적)
SERVER_ID=custom_server_id
SERVER_DOMAIN=example.com
```

## 아키텍처 패턴

### 모듈화된 구조
- **states/**: LangGraph 노드 함수들
- **utils/**: 유틸리티 및 헬퍼 함수들
- **ui/**: Streamlit UI 컴포넌트들

### 새로 추가된 모듈 구조 (채팅 히스토리)
```
modules/utils/
├── server_id_manager.py      # 서버 고유 ID 관리
├── dynamodb_connector.py     # DynamoDB 연결 관리
├── dynamodb_crud.py          # 데이터 CRUD 작업
├── session_manager.py        # 세션 관리 서비스
├── message_history_manager.py # 메시지 히스토리 관리
└── langgraph_state_manager.py # 워크플로우 상태 관리
```

### 상태 관리
- **LangGraph GraphState**: 워크플로우 상태 관리
- **Streamlit Session State**: UI 상태 관리
- **DynamoDB**: 영구 데이터 저장 및 세션 관리

### 데이터 흐름
```
사용자 입력 → LangGraph 워크플로우 → MySQL 쿼리 → 결과 분석 → 시각화
     ↓                    ↓                              ↓
DynamoDB 세션 저장 → 워크플로우 상태 저장 → 메시지 히스토리 저장
```

## 성능 고려사항

### 캐싱 전략
- **스키마 캐싱**: 데이터베이스 스키마 정보 캐시 (TTL: 1시간)
- **세션 캐싱**: 활성 세션 정보 메모리 캐시
- **메시지 캐싱**: 최근 메시지 및 검색 결과 캐시

### 데이터 최적화
- **자동 압축**: 1KB 이상 메시지 자동 압축 (zlib)
- **배치 처리**: DynamoDB 배치 작업 지원
- **페이지네이션**: 대용량 데이터 조회 시 페이지네이션

### 재시도 및 오류 처리
- **지수 백오프**: DynamoDB 스로틀링 대응
- **자동 재시도**: 네트워크 오류 시 자동 재시도
- **우아한 실패**: 오류 발생 시 사용자 친화적 메시지

## 보안 고려사항

### API 키 관리
- 환경 변수를 통한 안전한 API 키 저장
- `.env` 파일은 git에서 제외

### 데이터 보안
- **DynamoDB 암호화**: 저장 시 암호화 (AWS 관리형 키)
- **전송 암호화**: HTTPS/TLS를 통한 안전한 데이터 전송
- **접근 제어**: AWS IAM을 통한 세밀한 권한 관리

### 데이터 프라이버시
- **서버별 데이터 분리**: 서버 ID 기반 데이터 격리
- **세션별 데이터 분리**: 세션 간 완전한 데이터 격리
- **자동 정리**: 만료된 세션 및 메시지 자동 정리

## 확장성 고려사항

### 수평 확장
- **서버별 격리**: 서버 ID 기반 데이터 분할
- **DynamoDB 확장성**: 자동 스케일링 지원
- **무상태 설계**: 서버 간 상태 공유 최소화

### 성능 모니터링
- **LangSmith 추적**: AI 모델 성능 모니터링
- **DynamoDB 메트릭**: 읽기/쓰기 용량 모니터링
- **응답 시간 추적**: 사용자 경험 메트릭

## 배포 고려사항

### 컨테이너화
- **Docker**: 애플리케이션 컨테이너화
- **Docker Compose**: 로컬 개발 환경 (DynamoDB Local 포함)

### 환경별 설정
- **개발**: DynamoDB Local + 테스트 데이터
- **스테이징**: AWS DynamoDB + 제한된 데이터
- **프로덕션**: AWS DynamoDB + 완전한 보안 설정

### 마이그레이션 전략
- **스키마 버전 관리**: DynamoDB 테이블 스키마 버전 추적
- **데이터 마이그레이션**: 기존 데이터의 안전한 마이그레이션
- **롤백 계획**: 배포 실패 시 롤백 전략
