# Text-to-SQL 데이터 분석 애플리케이션

## 프로젝트 개요

이 애플리케이션은 사용자가 자연어로 데이터베이스 질의를 할 수 있게 해주는 Text-to-SQL 변환 시스템입니다. MySQL 데이터베이스에서 데이터를 조회하고, 그 결과를 분석하거나 그래프로 시각화하여 보여주는 채팅 인터페이스를 제공합니다.

## 핵심 기능

- 자연어를 SQL 쿼리로 변환
- SQL 쿼리 자동 생성 및 실행
- 쿼리 결과의 시각적 표현 (다양한 차트 유형 지원)
- 데이터 분석 및 인사이트 제공
- 사용자 친화적인 채팅 인터페이스
- 실시간 스트리밍 응답

## 주요 목표

1. 비 기술적 사용자도 SQL 지식 없이 데이터베이스를 쉽게 분석할 수 있게 함
2. LangGraph 기반의 모듈화된 워크플로우 구현으로 복잡한 처리 과정을 효율적으로 관리
3. 실시간 피드백과 직관적인 시각화를 통해 사용자 경험 개선
4. 데이터 기반 의사결정을 위한 심층적인 분석 제공

## 대상 사용자

- 데이터 분석가
- 비즈니스 사용자
- SQL에 익숙하지 않은 사람들
- 신속한 데이터 인사이트가 필요한 의사결정자
