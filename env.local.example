# ===========================================
# 로컬 개발 환경 설정 예시 (env.local.example)
# ===========================================
# 이 파일을 .env로 복사하고 실제 값들을 설정하세요.
# cp env.local.example .env

# OpenAI 설정
OPENAI_API_KEY=your_openai_api_key_here

# MySQL 데이터베이스 설정
MYSQL_HOST=db-logis.logisteq.com
MYSQL_PORT=9311
MYSQL_USER=root
MYSQL_PASSWORD=your_mysql_password_here
MYSQL_DATABASE=tms_dev

# LangSmith 설정 (선택적 - AI 모델 추적용)
LANGSMITH_API_KEY=your_langsmith_api_key_here
LANGSMITH_PROJECT=text_to_sql_app
LANGSMITH_TRACING=true

# ===========================================
# DynamoDB 로컬 개발 설정
# ===========================================
# 로컬 DynamoDB 사용 (Docker로 실행)
DYNAMODB_ENDPOINT_URL=http://localhost:8000
AWS_REGION=ap-northeast-2
AWS_ACCESS_KEY_ID=test
AWS_SECRET_ACCESS_KEY=test

# DynamoDB 테이블명 (기본값 사용 가능)
DYNAMODB_SESSIONS_TABLE=text_to_sql_sessions
DYNAMODB_MESSAGES_TABLE=text_to_sql_messages

# ===========================================
# 서버 및 세션 관리 설정
# ===========================================
SERVER_ID=local-dev
SERVER_DOMAIN=localhost
SESSION_TTL_DAYS=30
MAX_SESSIONS_PER_SERVER=100
MESSAGE_RETENTION_DAYS=90

# ===========================================
# 개발 환경 설정
# ===========================================
ENVIRONMENT=development
DEBUG=true

# ===========================================
# 성능 및 캐시 설정
# ===========================================
# 스키마 캐시 설정
SCHEMA_CACHE_ENABLED=true
SCHEMA_CACHE_TTL=3600

# ===========================================
# AI 모델 설정
# ===========================================
# 개발용으로 빠르고 저렴한 모델 사용
SQL_QUERY_MODEL=gpt-4o-mini
DATA_ANALYSIS_MODEL=gpt-4o-mini

# ===========================================
# 사용 방법:
# ===========================================
# 1. 이 파일을 .env로 복사: cp env.local.example .env
# 2. 실제 API 키들을 설정
# 3. DynamoDB Local 시작: docker-compose -f docker-compose.dynamodb.yml up -d
# 4. 애플리케이션 실행: uv run streamlit run ui/app.py

# ===========================================
# 프로덕션 환경 설정 참고:
# ===========================================
# 프로덕션에서는 다음과 같이 설정하세요:
# DYNAMODB_ENDPOINT_URL=  # 비워두면 AWS DynamoDB 사용
# AWS_REGION=ap-northeast-2
# AWS_ACCESS_KEY_ID=your_real_aws_access_key
# AWS_SECRET_ACCESS_KEY=your_real_aws_secret_key
# ENVIRONMENT=production
# DEBUG=false 