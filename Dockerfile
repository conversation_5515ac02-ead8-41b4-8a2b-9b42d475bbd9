# 베이스 이미지: Python 3.11 슬림 버전 사용
FROM python:3.13-slim

# 작업 디렉토리 설정
WORKDIR /app

# C++ 컴파일러 및 빌드 도구 설치
RUN apt-get update && apt-get install -y \
    build-essential \
    g++ \
    cmake \
    && rm -rf /var/lib/apt/lists/*

# uv 설치
RUN pip install uv

# 프로젝트 의존성 파일 복사
COPY pyproject.toml uv.lock .python-version ./
# 또는 requirements.txt 사용 시: COPY requirements.txt ./

# 의존성 설치
RUN uv sync --frozen --no-install-project

# 프로젝트 소스 코드 복사
COPY . .

# 프로젝트 설치
RUN uv sync

EXPOSE 8501

# 애플리케이션 실행 명령
CMD ["uv", "run", "streamlit", "run", "ui/app.py", "--server.address", "0.0.0.0"]