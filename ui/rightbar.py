"""
Streamlit 애플리케이션의 오른쪽 사이드바 구성 요소

이 모듈은 데이터 테이블과 시각화 그래프를 표시하는 오른쪽 사이드바를 제공합니다.
"""

import streamlit as st
import pandas as pd
import plotly.express as px
from typing import Optional, Any, Dict, List
import logging

# 로깅 설정
logger = logging.getLogger(__name__)


def render_rightbar_toggle_button():
    """오른쪽 사이드바 토글 버튼 렌더링"""
    # 현재 상태 확인
    is_visible = st.session_state.get("rightbar_visible", True)

    # 토글 버튼 스타일
    st.markdown(
        """
    <style>
    .rightbar-toggle {
        position: fixed;
        top: 80px;
        right: 16px;
        z-index: 1000;
        background: white;
        border: 1px solid #e2e8f0;
        border-radius: 6px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        padding: 8px;
        cursor: pointer;
        transition: all 0.2s ease;
    }
    
    .rightbar-toggle:hover {
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        transform: translateY(-1px);
    }
    
    .close-icon {
        width: 24px;
        height: 24px;
        position: relative;
        overflow: hidden;
        display: inline-block;
    }
    
    .close-icon-inner {
        width: 18px;
        height: 18px;
        position: absolute;
        left: 3px;
        top: 3px;
        border: 1.5px solid #000000;
        border-radius: 2px;
    }
    
    .close-icon-inner::before {
        content: '×';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 12px;
        font-weight: bold;
        line-height: 1;
    }
    </style>
    """,
        unsafe_allow_html=True,
    )

    # 토글 버튼 (오른쪽 사이드바가 보일 때만 표시)
    if is_visible:
        if st.button("", key="rightbar_toggle", help="오른쪽 패널 닫기"):
            st.session_state.rightbar_visible = False
            st.rerun()


def render_rightbar(data=None, graph_data=None):
    """오른쪽 사이드바를 렌더링하는 메인 함수"""

    # 사이드바 표시 상태 확인
    if not st.session_state.get("rightbar_visible", True):
        return

    # 사이드바 컨테이너 스타일
    st.markdown(
        """
    <style>

    

    
    .rightbar-section {
        background: white;
        border: 1px solid #e2e8f0;
        border-radius: 8px;
        margin-bottom: 16px;
        overflow: hidden;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }
    
    .section-header {
        background: #f1f3f4;
        padding: 8px 12px;
        font-weight: 600;
        font-size: 14px;
        color: #2d3748;
        border-bottom: 1px solid #e2e8f0;
        display: flex;
        align-items: center;
        gap: 8px;
    }
    
    .rightbar-empty {
        text-align: center;
        padding: 40px 20px;
        color: #718096;
        background: white;
        border-radius: 8px;
        border: 1px solid #e2e8f0;
    }
    
    .data-metrics {
        display: flex;
        justify-content: space-around;
        padding: 8px;
        background: #f8f9fa;
        margin-top: 8px;
    }
    
    .metric-item {
        text-align: center;
        font-size: 12px;
    }
    
    .metric-value {
        font-weight: 600;
        font-size: 16px;
        color: #2d3748;
    }
    
    .metric-label {
        color: #718096;
    }
    
    .rightbar-show-btn {
        position: fixed;
        top: 80px;
        right: 16px;
        z-index: 1000;
        background: white;
        border: 1px solid #e2e8f0;
        border-radius: 6px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        padding: 8px 12px;
        cursor: pointer;
        transition: all 0.2s ease;
        font-size: 12px;
        color: #4F8BC9;
        font-weight: 600;
    }
    
    .rightbar-show-btn:hover {
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        transform: translateY(-1px);
        background: #f8f9ff;
    }
    </style>
    """,
        unsafe_allow_html=True,
    )

    # 메인 컨테이너
    st.markdown('<div class="rightbar-content">', unsafe_allow_html=True)

    # 헤더 스타일 정의
    st.markdown(
        """
    <style>
    .rightbar-header-container {
        background: linear-gradient(90deg, #4F8BC9, #6FA8DC);
        color: white;
        padding: 12px 16px;
        border-radius: 8px 8px 0 0;
        font-weight: 600;
        font-size: 16px;
        margin: -16px -16px 16px -16px;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .rightbar-title {
        flex: 1;
        text-align: center;
    }
    </style>
    """,
        unsafe_allow_html=True,
    )

    # 헤더 컨테이너
    st.markdown('<div class="rightbar-header-container">', unsafe_allow_html=True)

    # 제목과 버튼을 나란히 배치
    col1, col2 = st.columns([6, 1])

    with col1:
        st.markdown(
            '<div class="rightbar-title">✨ Graph & Table</div>', unsafe_allow_html=True
        )

    with col2:
        # SVG와 버튼 스타일을 포함한 커스텀 HTML/CSS
        html_code = """
        <div style="display: flex; justify-content: center;">
            <button style="
                border: none;
                border-radius: 4px;
                padding: 4px;
                background-color: rgba(255, 255, 255, 0.2);
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
                width: 28px;
                height: 28px;
                transition: all 0.2s ease;"
                onmouseover="this.style.backgroundColor='rgba(255, 255, 255, 0.3)'; this.style.transform='scale(1.05)';"
                onmouseout="this.style.backgroundColor='rgba(255, 255, 255, 0.2)'; this.style.transform='scale(1)';"
                onclick="fetch('/?rightbar_close=true').then(() => window.parent.location.reload());"
                title="사이드바 접기">
                <svg width="20" height="20" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                    <!-- 정사각형 테두리 -->
                    <rect x="5" y="5" width="90" height="90" fill="none" stroke="white" stroke-width="2"/>
                    <!-- > 모양 화살표 -->
                    <polyline points="30,30 50,50 30,70" fill="none" stroke="white" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/>
                    <!-- 세로 직선 -->
                    <line x1="75" y1="5" x2="75" y2="95" stroke="white" stroke-width="4"/>
                </svg>
            </button>
        </div>
        """

        # HTML 렌더링
        st.components.v1.html(html_code, height=32)

        # URL 파라미터를 통해 클릭 감지
        query_params = st.query_params
        if query_params.get("rightbar_close") == "true":
            st.session_state.rightbar_visible = False
            # URL 파라미터 제거
            st.query_params.clear()
            st.rerun()

    st.markdown("</div>", unsafe_allow_html=True)

    # 데이터 유무 확인
    has_content = False
    if data is not None and isinstance(data, pd.DataFrame) and not data.empty:
        has_content = True
    if graph_data is not None:
        has_content = True

    if not has_content:
        # 빈 상태 표시
        st.markdown(
            """
        <div class="rightbar-empty">
            <div style="font-size: 48px; margin-bottom: 16px; opacity: 0.5;">📈</div>
            <div style="font-weight: 600; margin-bottom: 8px;">데이터 대기 중</div>
            <div style="font-size: 14px;">질문을 하시면 그래프와<br>테이블이 여기에 표시됩니다.</div>
        </div>
        """,
            unsafe_allow_html=True,
        )
    else:
        # 데이터 테이블 섹션
        if data is not None and isinstance(data, pd.DataFrame) and not data.empty:
            st.markdown('<div class="rightbar-section">', unsafe_allow_html=True)
            st.markdown(
                f'<div class="section-header">📋 데이터 테이블 ({len(data)}행)</div>',
                unsafe_allow_html=True,
            )

            # 데이터프레임 표시
            st.dataframe(
                data,
                use_container_width=True,
                height=min(300, (len(data) + 1) * 35 + 38),
            )

            # 데이터 요약 메트릭
            st.markdown(
                f"""
            <div class="data-metrics">
                <div class="metric-item">
                    <div class="metric-value">{len(data):,}</div>
                    <div class="metric-label">행</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value">{len(data.columns):,}</div>
                    <div class="metric-label">열</div>
                </div>
            </div>
            """,
                unsafe_allow_html=True,
            )

            st.markdown("</div>", unsafe_allow_html=True)

        # 그래프 섹션
        if graph_data is not None:
            st.markdown('<div class="rightbar-section">', unsafe_allow_html=True)
            st.markdown(
                '<div class="section-header">📈 시각화</div>', unsafe_allow_html=True
            )

            try:
                # graph_data에서 실제 그래프 객체 추출
                if isinstance(graph_data, dict):
                    graph_obj = graph_data.get("graph_object")
                else:
                    graph_obj = graph_data

                if graph_obj is not None:
                    if hasattr(graph_obj, "show"):
                        # Plotly 그래프
                        st.plotly_chart(graph_obj, use_container_width=True, height=350)
                    elif hasattr(graph_obj, "savefig"):
                        # Matplotlib 그래프
                        st.pyplot(graph_obj, use_container_width=True)
                    else:
                        st.info("지원되지 않는 그래프 형식입니다.")
                else:
                    st.warning("그래프 데이터가 없습니다.")

            except Exception as e:
                logger.error(f"그래프 표시 중 오류: {e}")
                st.error(f"그래프를 표시할 수 없습니다: {str(e)}")

            st.markdown("</div>", unsafe_allow_html=True)

        # 데이터는 있지만 그래프가 없는 경우 기본 시각화 생성
        elif data is not None and isinstance(data, pd.DataFrame) and not data.empty:
            st.markdown('<div class="rightbar-section">', unsafe_allow_html=True)
            st.markdown(
                '<div class="section-header">📈 자동 시각화</div>',
                unsafe_allow_html=True,
            )

            try:
                # 간단한 기본 그래프 생성
                if len(data.columns) >= 2:
                    # 숫자형 컬럼 찾기
                    numeric_cols = data.select_dtypes(include=["number"]).columns
                    if len(numeric_cols) >= 1:
                        # 첫 번째 열을 x축, 첫 번째 숫자 열을 y축으로 사용
                        x_col = data.columns[0]
                        y_col = numeric_cols[0]

                        # 상위 10개 행만 표시
                        plot_data = data.head(10)
                        fig = px.bar(
                            plot_data, x=x_col, y=y_col, title=f"{y_col} by {x_col}"
                        )
                        st.plotly_chart(fig, use_container_width=True, height=350)
                    else:
                        st.info("시각화할 수 있는 숫자 데이터가 없습니다.")
                else:
                    st.info("시각화하기에 충분한 열이 없습니다.")
            except Exception as e:
                logger.error(f"자동 시각화 생성 중 오류: {e}")
                st.info("자동 시각화를 생성할 수 없습니다.")

            st.markdown("</div>", unsafe_allow_html=True)

    # 컨테이너 닫기
    st.markdown("</div>", unsafe_allow_html=True)


def render_rightbar_show_button():
    """오른쪽 사이드바 표시 버튼 렌더링 (사이드바가 숨겨져 있을 때)"""
    if not st.session_state.get("rightbar_visible", True):
        # 표시 버튼을 위한 컨테이너
        st.markdown(
            """
        <div class="rightbar-show-btn" onclick="document.querySelector('[data-testid=\\"rightbar_show\\"]').click()">
            📊 데이터 보기
        </div>
        """,
            unsafe_allow_html=True,
        )

        # 숨겨진 표시 버튼
        if st.button("", key="rightbar_show", help="오른쪽 패널 열기"):
            st.session_state.rightbar_visible = True
            st.rerun()


def extract_latest_data_from_messages(messages: List[Dict[str, Any]]) -> tuple:
    """메시지에서 최신 데이터와 그래프 추출"""
    latest_data = None
    latest_graph_data = None

    if messages:
        # 최신 assistant 메시지부터 검색
        for message in reversed(messages):
            if message.get("role") == "assistant":
                # 데이터 추출
                if message.get("data") is not None and isinstance(
                    message.get("data"), pd.DataFrame
                ):
                    latest_data = message["data"]

                # 그래프 데이터 추출
                if message.get("graph") is not None:
                    latest_graph_data = {
                        "type": message.get("graph_type", "bar"),
                        "title": "데이터 시각화",
                        "graph_object": message["graph"],
                    }

                # 첫 번째 assistant 메시지에서 찾으면 중단
                break

    return latest_data, latest_graph_data


def get_rightbar_visibility():
    """오른쪽 사이드바 표시 상태 반환"""
    return st.session_state.get("rightbar_visible", True)


def toggle_rightbar():
    """오른쪽 사이드바 토글"""
    current_state = st.session_state.get("rightbar_visible", True)
    st.session_state.rightbar_visible = not current_state
