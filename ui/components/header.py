import streamlit as st
import base64
import os

def load_image_base64(image_path):
    """이미지를 base64로 로드하는 헬퍼 함수"""
    try:
        with open(image_path, "rb") as f:
            return base64.b64encode(f.read()).decode()
    except FileNotFoundError:
        st.warning(f"이미지를 찾을 수 없습니다: {image_path}")
        return None

def render_header():
    """피그마 디자인을 기반으로 한 헤더 컴포넌트"""
    
    # 이미지 경로 설정
    image_dir = os.path.join(os.path.dirname(__file__), "images")
    
    # 이미지 로드
    carta_logo = load_image_base64(os.path.join(image_dir, "carta_logo.png"))
    ic_chatbot = load_image_base64(os.path.join(image_dir, "ic_chatbot.svg"))
    chevron_down = load_image_base64(os.path.join(image_dir, "chevron_down.svg"))
    layout_grid = load_image_base64(os.path.join(image_dir, "layout_grid.svg"))
    square_pen = load_image_base64(os.path.join(image_dir, "square_pen.svg"))
    
    # 헤더 CSS 스타일
    header_css = """
    <style>
    /* Streamlit 시스템 영역 숨기기 */
    .stDeployButton {
        display: none !important;
    }
    
    #MainMenu {
        visibility: hidden !important;
    }
    
    header[data-testid="stHeader"] {
        display: none !important;
    }
    
    section[data-testid="stHeader"] {
        display: none !important;
    }
    
    .carta-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 20px;
        background: white;
        border-bottom: 1px solid #EFEFEF;
        min-height: 60px;
        box-sizing: border-box;
        position: relative;
        z-index: 1000;
        margin-top: 0;
        top: -80px;
    }
    
    .header-left {
        display: flex;
        align-items: center;
        gap: 12px;
    }
    
    .logo-section {
        display: flex;
        align-items: center;
        gap: 8px;
    }
    
    .chatbot-icon {
        width: 32px;
        height: 32px;
        background: linear-gradient(180deg, #EB642B 0%, #FDB33C 100%);
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .chatbot-icon img {
        width: 24px;
        height: 14px;
    }
    
    .carta-logo {
        width: 291px;
        height: 30px;
        object-fit: contain;
    }
    
    .ai-version-selector {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 10px;
        border: 1px solid #EFEFEF;
        border-radius: 12px;
        background: white;
        cursor: pointer;
        transition: all 0.2s ease;
    }
    
    .ai-version-selector:hover {
        border-color: #58B4B6;
        box-shadow: 0 0 0 1px rgba(88, 180, 182, 0.1);
    }
    
    .ai-version-text {
        font-family: Pretendard, sans-serif;
        font-size: 16px;
        font-weight: 400;
        color: #111111;
    }
    
    .chevron-icon {
        width: 20px;
        height: 20px;
    }
    
    .header-right {
        display: flex;
        align-items: center;
        gap: 8px;
    }
    
    .dashboard-btn {
        display: flex;
        align-items: center;
        gap: 10px;
        padding: 10px;
        border: 1px solid;
        border-image: linear-gradient(135deg, #58B4B6 0%, #FF5900 100%);
        border-image-slice: 1;
        border-radius: 12px;
        background: white;
        cursor: pointer;
        transition: all 0.2s ease;
    }
    
    .dashboard-btn:hover {
        background: linear-gradient(135deg, rgba(88, 180, 182, 0.05) 0%, rgba(255, 89, 0, 0.05) 100%);
    }
    
    .dashboard-icon {
        width: 20px;
        height: 20px;
    }
    
    .dashboard-text {
        font-family: Pretendard, sans-serif;
        font-size: 16px;
        font-weight: 500;
        background: linear-gradient(135deg, #58B4B6 0%, #FF5900 100%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }
    
    .new-chat-btn {
        width: 40px;
        height: 40px;
        border: 1px solid #EFEFEF;
        border-radius: 12px;
        background: white;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s ease;
    }
    
    .new-chat-btn:hover {
        border-color: #58B4B6;
        background: rgba(88, 180, 182, 0.05);
    }
    
    .new-chat-icon {
        width: 20px;
        height: 20px;
    }
    
    @media (max-width: 768px) {
        .carta-header {
            padding: 8px 16px;
            gap: 8px;
        }
        
        .header-left {
            gap: 8px;
        }
        
        .carta-logo {
            width: 200px;
            height: 20px;
        }
        
        .dashboard-text {
            display: none;
        }
        
        .ai-version-text {
            font-size: 14px;
        }
    }
    </style>
    """
    
    # HTML 구조
    header_html = f"""
    {header_css}
    <div class="carta-header">
        <div class="header-left">
            <div class="logo-section">
                <div class="chatbot-icon">
                    {f'<img src="data:image/svg+xml;base64,{ic_chatbot}" alt="Chatbot Icon">' if ic_chatbot else '🤖'}
                </div>
                {f'<img src="data:image/png;base64,{carta_logo}" alt="Carta Assistant" class="carta-logo">' if carta_logo else '<span style="font-size: 24px; font-weight: bold; color: #111;">CARTA ASSISTANT</span>'}
            </div>
            <div class="ai-version-selector" onclick="toggleAIVersionDropdown()">
                <span class="ai-version-text">AI 1.2</span>
                {f'<img src="data:image/svg+xml;base64,{chevron_down}" alt="Dropdown" class="chevron-icon">' if chevron_down else '▼'}
            </div>
        </div>
        <div class="header-right">
            <div class="dashboard-btn" onclick="openDashboard()">
                {f'<img src="data:image/svg+xml;base64,{layout_grid}" alt="Dashboard" class="dashboard-icon">' if layout_grid else '📊'}
                <span class="dashboard-text">Ai Dashboard</span>
            </div>
            <div class="new-chat-btn" onclick="startNewChat()">
                {f'<img src="data:image/svg+xml;base64,{square_pen}" alt="New Chat" class="new-chat-icon">' if square_pen else '✏️'}
            </div>
        </div>
    </div>
    
    <script>
    function toggleAIVersionDropdown() {{
        console.log('AI 버전 드롭다운 클릭됨');
        // AI 버전 선택 드롭다운 로직 구현 예정
        alert('AI 버전 선택 기능은 곧 추가될 예정입니다.');
    }}
    
    function openDashboard() {{
        console.log('대시보드 버튼 클릭됨');
        // 대시보드 페이지로 이동 로직 구현 예정
        alert('AI 대시보드 기능은 곧 추가될 예정입니다.');
    }}
    
    function startNewChat() {{
        console.log('새 채팅 버튼 클릭됨');
        // 새 채팅 시작 로직
        if (window.parent) {{
            window.parent.postMessage({{
                type: 'new_chat_requested'
            }}, '*');
        }}
    }}
    </script>
    """
    
    return header_html 