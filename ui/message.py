"""
Message 타입 정의

이 모듈은 채팅 메시지의 구조를 정의하는 타입을 제공합니다.
"""

import pandas as pd
import matplotlib.pyplot as plt
from typing import Dict, List, Any, Optional, Union, Literal
from typing_extensions import TypedDict

class Message(TypedDict, total=False):
    """채팅 메시지 구조를 정의하는 클래스"""
    # 필수 필드
    role: Literal["user", "assistant"]  # 메시지 발신자 역할
    content: str                        # 메시지 텍스트 내용
    
    # 선택적 필드
    sql: Optional[str]                  # 생성된 SQL 쿼리
    data: Optional[pd.DataFrame]        # 쿼리 결과 데이터프레임
    graph: Optional[plt.Figure]         # 생성된 그래프 객체
    graph_type: Optional[Literal["bar", "line", "pie", "scatter", "area", "histogram"]]  # 그래프 유형
    analysis_display: Optional[str]     # 분석 결과 텍스트
    timestamp: Optional[str]            # 메시지 생성 시간
    error: Optional[str]                # 오류 메시지 (있는 경우)
    id: Optional[str]                   # 메시지 고유 식별자
