"""
Text-to-SQL Streamlit 애플리케이션

이 애플리케이션은 사용자의 자연어 질문을 SQL 쿼리로 변환하여 MySQL 데이터베이스에서
데이터를 조회하고, 그 결과를 분석하거나 그래프로 시각화하여 보여주는 채팅 인터페이스를 제공합니다.
"""

import streamlit as st
import pandas as pd
import os
import json
import re
from typing import Dict, List, Any, Optional, Tuple
import sys
import logging
from dotenv import load_dotenv
import uuid
from datetime import datetime, timezone
import time

# 상위 디렉토리 경로 추가 (상대 임포트를 위해)
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 설정 모듈 임포트
from config import Config

# 엔진 모듈 임포트
from modules.utils.db_connector import create_db_connection, execute_query_with_langchain, get_schema_info
from modules.visualization import generate_graph
from modules.utils.streaming_callback import StreamingCallbackHandler
from modules.utils.logging_utils import log_config_info

# LangGraph 워크플로우 모듈 임포트
from modules.langraph_workflow import process_query_with_langgraph

# 세션 및 메시지 관리 모듈 임포트
from modules.utils import get_message_history_manager, MessageType

from ui.message import Message
from ui.sidebar import render_sidebar  # sidebar 모듈 import
from ui.rightbar import render_rightbar, extract_latest_data_from_messages, get_rightbar_visibility, render_rightbar_show_button  # rightbar 모듈 import
from ui.components.header import render_header  # header 컴포넌트 import 추가

# 로깅 설정
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 환경 변수 로드 (가장 먼저 실행)
try:
    # 명시적으로 환경 변수 파일 로드 (프로젝트 루트의 .env 파일)
    dotenv_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), '.env')
    if os.path.exists(dotenv_path):
        # 실제 존재하는 .env 파일 로드
        load_dotenv(dotenv_path=dotenv_path)
        logger.info(f"환경 변수 파일 로드 성공: {dotenv_path}")
        
        # 주요 환경변수 상태 로깅 (값 노출 없이)
        sql_model_env = "설정됨" if os.getenv("SQL_QUERY_MODEL") else "미설정"
        data_model_env = "설정됨" if os.getenv("DATA_ANALYSIS_MODEL") else "미설정"
        openai_key_env = "설정됨" if os.getenv("OPENAI_API_KEY") else "미설정"
        mysql_host_env = "설정됨" if os.getenv("MYSQL_HOST") else "미설정"
        
        logger.info(f"주요 환경변수 상태: SQL_QUERY_MODEL({sql_model_env}), DATA_ANALYSIS_MODEL({data_model_env}), "
                   f"OPENAI_API_KEY({openai_key_env}), MYSQL_HOST({mysql_host_env})")
        #모델 설정
        models = Config.get_model_config()
        logger.info(f"모델 설정 ====>   sql_model : {models['sql_query']}   , data_model : {models['data_analysis']}")
        logger.info(f"환경변수 로딩 ====>   sql_model_env : {sql_model_env}   , data_model_env : {data_model_env}")

    else:
        logger.warning(f"환경 변수 파일(.env)이 존재하지 않습니다: {dotenv_path}")
        logger.warning("시스템 환경 변수나 세션 상태를 사용합니다.")
except Exception as e:
    logger.error(f"환경 변수 로드 중 오류 발생: {str(e)}")

# 페이지 설정
st.set_page_config(
    page_title="Text-to-SQL 데이터 분석 애플리케이션",
    page_icon="📊",
    layout="wide",
)

# 헤더 렌더링
header_html = render_header()
st.markdown(header_html, unsafe_allow_html=True)

# CSS 추가
st.markdown("""
<style>
    /* Streamlit 기본 헤더 숨기기 */
    .stDeployButton {
        display: none !important;
    }
    
    #MainMenu {
        visibility: hidden !important;
    }
    
    header[data-testid="stHeader"] {
        display: none !important;
    }
    
    section[data-testid="stHeader"] {
        display: none !important;
    }
    
    /* 메인 콘텐츠 영역 상단 여백 조정 */
    .main .block-container {
        padding-top: 0rem !important;
        margin-top: 0rem !important;
    }
    
    .reportview-container {
        margin-top: 0em !important;
    }
    
    /* 메인 콘텐츠와 헤더 사이 간격 조정 */
    .stApp > div:first-child {
        margin-top: 0 !important;
    }
    
    .stAlert {
        font-size: 1rem;
    }
    
    /* 사이드바 배경색을 HTML 컨텐츠와 동일하게 설정 */
    .stSidebar {
        background-color: #F7F7F7 !important;
    }
    
    .stSidebar > div {
        background-color: #F7F7F7 !important;
    }
    
    .stSidebar .sidebar-content {
        padding-top: 0rem;
        background-color: #F7F7F7 !important;
    }
    
    /* 사이드바 내부 모든 요소들도 배경색 통일 */
    .stSidebar * {
        background-color: transparent !important;
    }
    
    /* 사이드바 컨테이너 배경색 강제 적용 */
    section[data-testid="stSidebar"] {
        background-color: #F7F7F7 !important;
    }
    
    section[data-testid="stSidebar"] > div {
        background-color: #F7F7F7 !important;
    }
    
    /* 사이드바 스크롤바 스타일링 */
    .stSidebar .sidebar-content::-webkit-scrollbar {
        width: 6px;
    }
    
    .stSidebar .sidebar-content::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
    }
    
    .stSidebar .sidebar-content::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;
    }
    
    .stSidebar .sidebar-content::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
    }
    
    /* 고정 위치 오른쪽 사이드바 토글 버튼 */
    .fixed-rightbar-toggle {
        position: fixed;
        right: 20px;
        top: 50%;
        transform: translateY(-50%);
        z-index: 9999;
        background: linear-gradient(45deg, #4F8BC9, #6FA8DC);
        color: white;
        border: none;
        border-radius: 50%;
        width: 56px;
        height: 56px;
        cursor: pointer;
        box-shadow: 0 4px 12px rgba(79, 139, 201, 0.4);
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        font-weight: 600;
    }
    
    .fixed-rightbar-toggle:hover {
        background: linear-gradient(45deg, #3a6fa5, #5a94c7);
        transform: translateY(-50%) scale(1.1);
        box-shadow: 0 6px 16px rgba(79, 139, 201, 0.5);
    }
    
    .fixed-rightbar-toggle:active {
        transform: translateY(-50%) scale(0.95);
    }
    
    /* 토글 버튼 스타일 */
    .stButton button[key="rightbar_toggle"] {
        background: linear-gradient(45deg, #4F8BC9, #6FA8DC) !important;
        color: white !important;
        border: none !important;
        border-radius: 50% !important;
        width: 50px !important;
        height: 50px !important;
        font-size: 20px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        box-shadow: 0 4px 12px rgba(79, 139, 201, 0.4) !important;
        transition: all 0.3s ease !important;
    }
    
    .stButton button[key="rightbar_toggle"]:hover {
        background: linear-gradient(45deg, #3a6fa5, #5a94c7) !important;
        transform: scale(1.1) !important;
        box-shadow: 0 6px 16px rgba(79, 139, 201, 0.5) !important;
    }
</style>
""", unsafe_allow_html=True)

# 세션 상태 초기화 - 사이드바 렌더링 전에 먼저 실행
# messages: Message 클래스 타입의 메시지 목록
# Message 클래스 구조는 modules/states/message.py 참조
if "messages" not in st.session_state:
    st.session_state.messages = []

if "current_session_id" not in st.session_state:
    st.session_state.current_session_id = None

if "current_query" not in st.session_state:
    st.session_state.current_query = None

if "current_result" not in st.session_state:
    st.session_state.current_result = None

if "current_graph" not in st.session_state:
    st.session_state.current_graph = None

if "error" not in st.session_state:
    st.session_state.error = None

if "schema_info" not in st.session_state:
    st.session_state.schema_info = None
    
# 입력 중 상태 추적 변수 추가
if "is_processing" not in st.session_state:
    st.session_state.is_processing = False

# 오른쪽 사이드바 토글 상태 초기화
if "rightbar_visible" not in st.session_state:
    st.session_state.rightbar_visible = True

# 화면 상단에 토글 버튼 배치
toggle_col1, toggle_col2 = st.columns([10, 1])
with toggle_col2:
    toggle_icon = "✖️" if st.session_state.rightbar_visible else "📊"
    if st.button(toggle_icon, key="rightbar_toggle", help="데이터 패널 열기/닫기"):
        st.session_state.rightbar_visible = not st.session_state.rightbar_visible
        st.rerun()

# 사이드바 렌더링 (세션 관리 포함) - 세션 상태 초기화 후에 실행
with st.sidebar:
    session_ui = render_sidebar()

# 현재 세션 정보 로깅 (사이드바 렌더링 전)
current_session_id = st.session_state.get('current_session_id')
message_count = len(st.session_state.get('messages', []))
logger.info(f"앱 시작 - 현재 세션 ID: {current_session_id}, 메시지 수: {message_count}")

# 사이드바 렌더링 후 세션 ID 다시 확인
current_session_id = st.session_state.get('current_session_id')
logger.info(f"사이드바 렌더링 후 - 현재 세션 ID: {current_session_id}, 메시지 수: {len(st.session_state.get('messages', []))}")

# URL 파라미터를 통한 새 세션 시작 처리
query_params = st.query_params

# 오른쪽 사이드바 토글 처리
if query_params.get('toggle_rightbar'):
    toggle_value = query_params.get('toggle_rightbar')
    if toggle_value == 'true':
        st.session_state.rightbar_visible = True
    elif toggle_value == 'false':
        st.session_state.rightbar_visible = False
    
    # URL 파라미터 제거
    st.query_params.clear()
    st.rerun()

if query_params.get('new_session') == 'true':
    logger.info("새 세션 시작 요청 감지")
    
    # 현재 세션 상태 초기화
    if session_ui:
        try:
            # 새 세션 생성
            new_session_id = session_ui.create_new_session()
            if new_session_id:
                logger.info(f"새 세션 생성 성공: {new_session_id}")
                
                # 세션 상태 초기화
                st.session_state.messages = []
                st.session_state.current_query = None
                st.session_state.current_result = None
                st.session_state.current_graph = None
                st.session_state.error = None
                st.session_state.schema_info = None
                st.session_state.is_processing = False
                st.session_state.current_session_id = new_session_id
                
                # URL 파라미터 제거하고 리다이렉트
                st.query_params.clear()
                st.success("✅ 새 채팅 세션이 시작되었습니다!")
                st.rerun()
            else:
                logger.error("새 세션 생성 실패")
                st.error("새 세션을 생성할 수 없습니다.")
        except Exception as e:
            logger.error(f"새 세션 생성 중 오류: {e}")
            st.error(f"새 세션 생성 중 오류가 발생했습니다: {str(e)}")
    
    # URL 파라미터만 제거 (세션 생성 실패 시에도)
    st.query_params.clear()
    st.rerun()

# 메시지 로깅 함수 정의
def log_message_details(message: Message, prefix="메시지 정보"):
    """메시지 구조와 내용을 자세히 로깅하는 함수"""
    try:
        # 기본 메시지 정보 로깅
        role = message.get("role", "unknown")
        logger.info(f"{prefix} - 역할: {role}")
        
        # 각 필드별 로깅
        for key, value in message.items():
            if key == "data" and isinstance(value, pd.DataFrame):
                logger.info(f"{prefix} - {key}: DataFrame (shape: {value.shape})")
            elif key == "graph":
                logger.info(f"{prefix} - {key}: {'있음' if value is not None else '없음'}")
            elif key == "content":
                # content 필드는 일반적으로 텍스트이므로 그대로 출력
                logger.info(f"{prefix} - {key}: {value}")
            else:
                # 다른 필드들은 타입과 간략한 정보만 출력
                type_name = type(value).__name__
                value_preview = str(value)[:100] + '...' if isinstance(value, str) and len(str(value)) > 100 else value
                logger.info(f"{prefix} - {key} ({type_name}): {value_preview}")
    except Exception as e:
        logger.error(f"메시지 로깅 중 오류 발생: {str(e)}")

# 세션 복원 및 히스토리 로드 - session_ui 초기화 후에 실행
if session_ui:
    # 현재 세션 상태 로깅
    logger.info(f"세션 복원 검사 - 세션 ID: {current_session_id}, 메시지 수: {len(st.session_state.messages) if st.session_state.messages else 0}")
    
    # 세션이 없거나 메시지가 없으면 복원 시도
    if not current_session_id or not st.session_state.messages:
        logger.info("세션 또는 메시지가 없어 복원 시도...")
        
        # 복원 전 상태 기록
        old_session_id = current_session_id
        old_message_count = len(st.session_state.messages) if st.session_state.messages else 0
        
        # 세션 복원 시도
        restored_session_id = session_ui.ensure_session_exists()
        
        if restored_session_id:
            # 복원된 세션 ID를 current_session_id에 반영
            current_session_id = restored_session_id
            
            # 복원 후 상태 기록
            new_message_count = len(st.session_state.messages) if st.session_state.messages else 0
            
            logger.info(f"세션 복원/생성 성공:")
            logger.info(f"  - 세션 ID: {old_session_id} -> {current_session_id}")
            logger.info(f"  - 메시지 수: {old_message_count} -> {new_message_count}")
            
            # 복원이 성공했는지 다시 한 번 확인
            if new_message_count > 0:
                logger.info("✅ 히스토리가 성공적으로 복원되었습니다")
            elif current_session_id != old_session_id:
                logger.info("✅ 새 세션이 생성되었습니다")
            else:
                logger.warning("⚠️ 세션은 있지만 히스토리 로드가 실패했을 수 있습니다")
        else:
            logger.warning("❌ 세션 복원/생성 실패")
    else:
        logger.info(f"✅ 세션이 이미 존재하고 메시지도 있습니다 (ID: {current_session_id}, 메시지: {len(st.session_state.messages)}개)")

# 복원 후 최종 세션 ID 확인 및 업데이트
if session_ui and not current_session_id:
    # session_ui에서 관리하는 세션 ID를 직접 가져오기
    current_session_id = st.session_state.get('current_session_id')
    if current_session_id:
        logger.info(f"session_state에서 세션 ID 복원: {current_session_id}")
    else:
        logger.warning("세션 ID가 여전히 None입니다")

# 최종적으로 세션 ID가 없으면 session_state에서 다시 가져오기
if not current_session_id:
    current_session_id = st.session_state.get('current_session_id')
    if current_session_id:
        logger.info(f"최종 세션 ID 업데이트: {current_session_id}")
    else:
        logger.warning("최종 세션 ID가 여전히 None입니다")

# 현재 세션 상태를 세션 스테이트에 저장 (이후 사용을 위해)
if current_session_id:
    st.session_state.current_session_id = current_session_id

# 세션이 변경되었는지 확인
if hasattr(st.session_state, '_last_session_id'):
    if st.session_state._last_session_id != current_session_id:
        logger.info(f"세션 변경 감지: {st.session_state._last_session_id} -> {current_session_id}")
        st.session_state._last_session_id = current_session_id
else:
    st.session_state._last_session_id = current_session_id

# 오류 메시지 표시
if st.session_state.error:
    st.error(st.session_state.error)
    if st.button("오류 지우기"):
        st.session_state.error = None
    st.rerun()

# 최신 메시지에서 데이터 추출
latest_data, latest_graph_data = extract_latest_data_from_messages(st.session_state.messages)

# 오른쪽 사이드바 표시 상태 확인
rightbar_visible = get_rightbar_visibility()

# 메인 레이아웃: 좌측 메인 콘텐츠 + 우측 사이드바 (조건부)
if rightbar_visible:
    main_col, right_col = st.columns([3, 2])  # 3:2 비율
else:
    main_col = st.columns(1)[0]  # 전체 너비 사용

# 왼쪽: 메인 채팅 영역
with main_col:
    # 메시지 기록 표시
    for i, message in enumerate(st.session_state.messages):
        try:
            # 메시지 타입에 따른 아바타 설정
            avatar = "🤖" if message["role"] == "assistant" else "👤"
            
            # 메시지 내용 표시
            with st.chat_message(message["role"], avatar=avatar):
                # 메시지 내용 표시 (텍스트만)
                content = message.get("content", "")
                if content:
                    # SQL 코드 블록은 제거하고 텍스트만 표시
                    import re
                    # ```sql...``` 블록 제거
                    content_without_sql = re.sub(r'```sql.*?```', '', content, flags=re.DOTALL)
                    if content_without_sql.strip():
                        st.markdown(content_without_sql.strip())
                
                # 분석 결과가 있는 경우 표시 (텍스트 기반 분석만)
                if message.get("analysis_display"):
                    st.markdown("**📊 분석 결과:**")
                    st.markdown(message["analysis_display"])
                
                # 타임스탬프 표시 (작게)
                if message.get("timestamp"):
                    st.caption(f"⏰ {message['timestamp']}")
                    
        except Exception as e:
            logger.error(f"메시지 표시 중 오류 (인덱스 {i}): {e}")
            st.error(f"메시지를 표시할 수 없습니다: {str(e)}")
            continue

# 오른쪽: 데이터 & 그래프 사이드바 (조건부 표시)
if rightbar_visible:
    with right_col:
        render_rightbar(latest_data, latest_graph_data)
else:
    # 오른쪽 사이드바가 숨겨져 있을 때 표시 버튼 렌더링
    render_rightbar_show_button()

# 실제 워크플로우 처리 (기존 시스템 유지)
# 사용자 입력 처리
user_query = st.chat_input("데이터베이스에 질문하세요...", disabled=st.session_state.is_processing)

# 사이드바에서 전달된 쿼리가 있는지 확인
if st.session_state.get("pending_query"):
    user_query = st.session_state.pop("pending_query")
    logger.info(f"사이드바에서 전달된 쿼리 처리: {user_query}")

if user_query:
    # 처리 상태로 설정
    st.session_state.is_processing = True
    
    # 세션 확인 및 생성
    if session_ui:
        current_session_id = session_ui.ensure_session_exists()
    else:
        current_session_id = None
    
    # 사용자 메시지 추가
    user_message: Message = {
        "role": "user", 
        "content": user_query,
        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "id": str(uuid.uuid4())
    }
    st.session_state.messages.append(user_message)
    logger.info(f"사용자 입력 - '{user_query}'")
    
    # 사용자 메시지 표시
    with st.chat_message("user"):
        st.markdown(user_query)
    
    # 사용자 메시지를 DynamoDB에 저장
    if current_session_id:
        max_retries = 3
        retry_count = 0
        save_success = False
        
        while retry_count < max_retries and not save_success:
            try:
                message_manager = get_message_history_manager()
                
                # 중복 저장 방지를 위해 기존 메시지 확인
                existing_messages, _ = message_manager.get_session_messages(
                    session_id=current_session_id, 
                    limit=10
                )
                
                # 동일한 내용의 메시지가 최근에 저장되었는지 확인
                duplicate_found = False
                for existing_msg in existing_messages:
                    if (existing_msg.content == user_query and 
                        existing_msg.message_type == MessageType.USER and
                        (datetime.now(timezone.utc) - existing_msg.timestamp).total_seconds() < 60):  # 1분 이내
                        duplicate_found = True
                        logger.info(f"중복 메시지 감지, 저장 건너뜀: {user_query[:50]}...")
                        save_success = True
                        break
                
                if not duplicate_found:
                    saved_message = message_manager.save_message(
                        session_id=current_session_id,
                        message_type=MessageType.USER,
                        content=user_query,
                        sender="user",
                        metadata={
                            "message_id": user_message["id"], 
                            "retry_count": retry_count,
                            "timestamp": user_message["timestamp"]
                        }
                    )
                    if saved_message:
                        logger.info(f"사용자 메시지 저장 성공: {saved_message.message_id} (시도 {retry_count + 1})")
                        save_success = True
                    else:
                        retry_count += 1
                        logger.warning(f"사용자 메시지 저장 실패, 재시도 {retry_count}/{max_retries}")
                        
            except Exception as e:
                retry_count += 1
                logger.error(f"사용자 메시지 저장 중 오류 (시도 {retry_count}/{max_retries}): {e}")
                if retry_count >= max_retries:
                    logger.error("사용자 메시지 저장 최종 실패")
                time.sleep(0.5)  # 재시도 전 잠시 대기

    # 여기서 st.rerun()을 호출하지 않고 처리 과정을 진행하도록 수정

    # 처리 중 표시 (스트리밍 방식으로 변경)
    with st.chat_message("assistant"):
        message_placeholder = st.empty()
        sql_placeholder = st.empty()  # 사용하지는 않지만 코드 호환성을 위해 유지
        data_placeholder = st.empty()
        # graph_placeholder 제거 (메시지 표시 부분에서 그래프 그림)
        analysis_placeholder = st.empty()

        # 초기 메시지 표시
        message_placeholder.markdown("🤔 생각 중...")

        try:
            # 응답을 저장할 Message 객체 초기화
            response: Message = {
                "role": "assistant",
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "id": str(uuid.uuid4()),  # 고유 식별자 추가
                "content": "",
                "sql": None,
                "data": None,
                "graph": None
            }

            # 처리 흐름 제어를 위한 변수
            continue_processing = True

            # 1. SQL 쿼리 생성 단계
            message_placeholder.markdown("🔍 SQL 쿼리를 생성하는 중...")

            # 스키마 정보 가져오기
            schema_info = st.session_state.get("schema_info", "")

            # API 키 가져오기
            api_key = Config.get_model_config().get("openai_api_key") or os.getenv("OPENAI_API_KEY")

            # 데이터베이스 연결 정보
            db_config = Config.get_db_config()

            # 스키마 정보가 없거나 DB 연결이 가능한 경우 실시간으로 스키마 정보 가져오기 시도
            if (not schema_info or db_config) and api_key:
                try:
                    # 캐시 설정 가져오기
                    cache_config = Config.get_schema_cache_config()
                    use_cache = cache_config["enabled"]

                    # 질문에 관련된 테이블 스키마 정보 가져오기 (캐시 사용 여부 전달)
                    updated_schema_info = get_schema_info(db_config, user_query, api_key, use_cache=use_cache)
                    if updated_schema_info:
                        schema_info = updated_schema_info
                        st.session_state["schema_info"] = schema_info
                        logger.info(f"질문 '{user_query}'에 관련된 스키마 정보 가져오기 성공 - 길이: {len(schema_info)} 문자, 캐시 사용={use_cache}")
                except Exception as e:
                    logger.warning(f"실시간 스키마 정보 가져오기 실패: {str(e)}")

            # 스키마 정보가 없는 경우 기본 예시 스키마 사용
            if not schema_info:
                logger.error("스키마 정보가 없습니다...")

            # API 키가 없는 경우 처리
            if not api_key:
                message_placeholder.markdown("OpenAI API 키가 설정되지 않았습니다. 사이드바에서 API 키를 입력해주세요.")
                st.session_state.messages.append({"role": "assistant", "content": "OpenAI API 키가 설정되지 않았습니다. 사이드바에서 API 키를 입력해주세요."})
                continue_processing = False

            # SQL 스트리밍을 위한 콜백 함수 정의
            def process_sql_chunk(text):
                # SQL 코드 형식을 찾아서 로그에만 기록
                sql_match = re.search(r'"sql"\s*:\s*"(.*?)"', text, re.DOTALL)
                if sql_match:
                    # 로그에만 SQL 기록
                    # logger.info(f"생성 중인 SQL 쿼리: {sql_match.group(1)}")
                    message_placeholder.markdown("🔍 사용자의 질문을 분석중...")
                # else:
                #     # SQL 코드가 아닌 일반 텍스트는 메시지 플레이스홀더에 표시
                #     # message_placeholder.markdown(text)
                #     logger.info(f"생성 중인 SQL 쿼리: {text}")

            # 분석 결과 스트리밍을 위한 콜백 함수 정의
            def process_analysis_chunk(text):
                analysis_placeholder.markdown(text)

            # SQL 생성을 위한 StreamingCallbackHandler 인스턴스 생성
            sql_streaming_handler = StreamingCallbackHandler(
                placeholder=message_placeholder,  # 플레이스홀더 전달
                initial_text="🔍 SQL 쿼리를 생성하는 중...",
                process_func=process_sql_chunk
            )

            # 분석을 위한 StreamingCallbackHandler 인스턴스 생성
            analysis_streaming_handler = StreamingCallbackHandler(
                placeholder=analysis_placeholder,  # 분석 플레이스홀더 전달
                initial_text="📈 데이터 분석 중...",
                process_func=process_analysis_chunk
            )

            # 모델 설정 가져오기
            models = Config.get_model_config()

            # LangGraph 워크플로우를 사용하여 처리 (스트리밍 콜백 사용)
            if continue_processing:
                # 데이터베이스 연결 정보
                db_config = Config.get_db_config()

                # LangGraph 워크플로우 실행
                workflow_result = process_query_with_langgraph(
                    query=user_query,
                    schema_info=schema_info,
                    api_key=api_key,
                    db_config=db_config,
                    sql_model=models["sql_query"],
                    analysis_model=models["data_analysis"],
                    streaming_callback={
                        "sql": sql_streaming_handler,
                        "analysis": analysis_streaming_handler
                    }
                )

                # 결과 추출
                sql = workflow_result.get("sql", "")
                explanation = workflow_result.get("explanation", "")
                tables_used = workflow_result.get("tables_used", [])
                columns_used = workflow_result.get("columns_used", {})
                query_type = workflow_result.get("query_type", "")
                df = workflow_result.get("data")

                # SQL이 비어있거나 유효하지 않은 경우 처리
                if not sql:
                    message_placeholder.markdown("해당 질문을 이해하지 못하였습니다.")
                    response["content"] = "해당 질문을 이해하지 못하였습니다."
                    st.session_state.messages.append(response)
                    continue_processing = False

                # SQL 쿼리 기록 (화면에는 표시하지 않음)
                if continue_processing:
                    response["sql"] = sql
                    response["explanation"] = explanation
                    response["tables_used"] = tables_used
                    response["columns_used"] = columns_used
                    response["query_type"] = query_type

                    # SQL 쿼리는 로그에만 기록
                    logger.info(f"실행할 SQL 쿼리: {sql}")
                    message_placeholder.markdown("✅ SQL 쿼리 생성 완료")

                    # 2. SQL 쿼리 실행 단계
                    message_placeholder.markdown("🔄 데이터베이스에 쿼리를 실행하는 중...")

            # 워크플로우 결과 처리
            if continue_processing:
                # 오류 확인
                if workflow_result.get("error"):
                    message_placeholder.markdown(f"오류가 발생했습니다: {workflow_result.get('error')}")
                    response["content"] = f"오류가 발생했습니다: {workflow_result.get('error')}"
                    st.session_state.messages.append(response)
                    continue_processing = False

                # 데이터 표시
                if continue_processing:
                    # df가 None이 아닌지 확인
                    if df is not None:
                        response["data"] = df

                        # DataFrame인지 확인
                        if isinstance(df, pd.DataFrame):
                            # 빈 DataFrame인지 확인
                            if hasattr(df, 'empty') and df.empty:
                                message_placeholder.markdown("쿼리 결과가 없습니다.")
                                response["content"] = "쿼리 결과가 없습니다."
                                # 빈 데이터프레임이라도 분석 단계로 진행
                                data_placeholder.markdown("쿼리 결과가 없습니다.")
                            else:
                                data_placeholder.dataframe(df)
                        else:
                            # DataFrame이 아닌 경우
                            message_placeholder.markdown("쿼리 결과가 DataFrame 형식이 아닙니다.")
                            response["content"] = "쿼리 결과가 DataFrame 형식이 아닙니다."
                            data_placeholder.write(str(df))
                    else:
                        message_placeholder.markdown("쿼리 결과가 없습니다.")
                        response["content"] = "쿼리 결과가 없습니다."
                    
                    message_placeholder.markdown("✅ 쿼리 실행 완료")

                # 3. 결과 분석 및 시각화 단계
                message_placeholder.markdown("📊 데이터 분석 및 시각화 중...")

                # 그래프 처리
                if continue_processing:
                    # 그래프 객체 가져오기
                    graph = workflow_result.get("graph")
                    if graph is not None:
                        # 그래프 저장
                        response["graph"] = graph
                        logger.info(f"그래프 객체 저장 성공: {type(graph)}")
                    else:
                        logger.info("그래프 객체가 없습니다.")
                    
                    # 그래프 타입 정보 저장
                    graph_type = workflow_result.get("graph_type")
                    if graph_type:
                        response["graph_type"] = graph_type
                        logger.info(f"그래프 타입 저장: {graph_type}")
                    else:
                        response["graph_type"] = "bar"  # 기본값
                        logger.info("기본 그래프 타입(bar) 사용")

                    # 분석 결과는 스트리밍 콜백에서 이미 처리했으므로 추가 처리 없음
                    analysis_display = workflow_result.get("analysis_display")
                    if analysis_display:
                        response["analysis_display"] = analysis_display

                    # 최종 응답 생성 (이미 LangGraph 워크플로우에서 처리됨)

            # 4. 최종 응답 생성
            if continue_processing:
                # DataFrame 안전하게 처리
                if df is not None and isinstance(df, pd.DataFrame) and hasattr(df, 'empty') and not df.empty:
                    data_str = df.to_string()
                else:
                    data_str = "결과 없음"
                    
                # 이미 content가 설정되지 않은 경우에만 설정
                if not response.get("content"):
                    response["content"] = data_str

                # 최종 메시지 업데이트
                message_placeholder.markdown("✅ 분석 완료")

                # 응답 저장 (고유 식별자를 통해 중복 확인)
                is_duplicate = False
                response_id = response.get("id")
                
                # 중복 확인 로직 개선
                for msg in st.session_state.messages:
                    if msg.get("id") == response_id:
                        is_duplicate = True
                        logger.info(f"중복 메시지 발견, 저장 건너뜀: {response_id}")
                        break
                        
                if not is_duplicate:
                    st.session_state.messages.append(response)
                    logger.info(f"응답 저장 성공: {len(st.session_state.messages)} 개의 메시지")
                    
                    # AI 응답을 DynamoDB에 저장 (재시도 로직 포함)
                    if current_session_id:
                        max_retries = 3
                        retry_count = 0
                        save_success = False
                        
                        while retry_count < max_retries and not save_success:
                            try:
                                message_manager = get_message_history_manager()
                                
                                # 중복 저장 방지를 위해 기존 메시지 확인
                                existing_messages, _ = message_manager.get_session_messages(
                                    session_id=current_session_id, 
                                    limit=10
                                )
                                
                                # AI 응답 내용 구성
                                ai_content = response.get("content", "")
                                if response.get("sql"):
                                    ai_content += f"\n\n```sql\n{response.get('sql')}\n```"
                                
                                # 동일한 내용의 AI 응답이 최근에 저장되었는지 확인
                                duplicate_found = False
                                for existing_msg in existing_messages:
                                    if (existing_msg.message_type == MessageType.ASSISTANT and
                                        existing_msg.content == ai_content and
                                        (datetime.now(timezone.utc) - existing_msg.timestamp).total_seconds() < 60):  # 1분 이내
                                        duplicate_found = True
                                        logger.info(f"중복 AI 응답 감지, 저장 건너뜀: {ai_content[:50]}...")
                                        save_success = True
                                        break
                                
                                if not duplicate_found:
                                    # 메타데이터 구성 (안전한 처리)
                                    metadata = {
                                        "contains_sql_query": bool(response.get("sql")),
                                        "has_data": response.get("data") is not None,
                                        "has_graph": response.get("graph") is not None,
                                        "query_type": response.get("query_type"),
                                        "tables_used": response.get("tables_used", []),
                                        "columns_used": response.get("columns_used", {}),
                                        "graph_type": response.get("graph_type"),
                                        "message_id": response_id,  # 추가 식별자
                                        "retry_count": retry_count,
                                        "timestamp": response.get("timestamp")
                                    }
                                    
                                    if response.get("analysis_display"):
                                        metadata["analysis"] = response.get("analysis_display")
                                    
                                    # GraphState 구성
                                    from modules.states.graph_state import GraphState
                                    graph_state = GraphState()
                                    graph_state["query"] = user_query
                                    if response.get("sql"):
                                        graph_state["sql"] = response.get("sql")
                                    if response.get("data") is not None:
                                        graph_state["data"] = response.get("data")
                                    if response.get("graph") is not None:
                                        graph_state["graph"] = response.get("graph")
                                    if response.get("content"):
                                        graph_state["content"] = response.get("content")
                                    if response.get("explanation"):
                                        graph_state["explanation"] = response.get("explanation")
                                    if response.get("tables_used"):
                                        graph_state["tables_used"] = response.get("tables_used")
                                    if response.get("columns_used"):
                                        graph_state["columns_used"] = response.get("columns_used")
                                    if response.get("query_type"):
                                        graph_state["query_type"] = response.get("query_type")
                                    if response.get("analysis_display"):
                                        graph_state["analysis_display"] = response.get("analysis_display")
                                    
                                    # 스키마 정보도 포함
                                    if schema_info:
                                        graph_state["schema_info"] = schema_info
                                    
                                    saved_message = message_manager.save_message(
                                        session_id=current_session_id,
                                        message_type=MessageType.ASSISTANT,
                                        content=ai_content,
                                        sender="assistant",
                                        metadata=metadata,
                                        graph_state=graph_state  # GraphState 추가
                                    )
                                    
                                    if saved_message:
                                        logger.info(f"AI 응답 저장 성공: {saved_message.message_id} (시도 {retry_count + 1})")
                                        save_success = True
                                    else:
                                        retry_count += 1
                                        logger.warning(f"AI 응답 저장 실패, 재시도 {retry_count}/{max_retries}")
                                        
                            except Exception as e:
                                retry_count += 1
                                logger.error(f"AI 응답 저장 중 오류 (시도 {retry_count}/{max_retries}): {e}")
                                if retry_count >= max_retries:
                                    logger.error("AI 응답 저장 최종 실패")
                                time.sleep(0.5)  # 재시도 전 잠시 대기

                # 처리 완료 상태로 설정
                st.session_state.is_processing = False
                
                # 페이지 리프레시하여 그래프 표시 및 입력창 활성화
                st.rerun()

        except Exception as e:
            # 오류 처리
            error_message = f"오류가 발생했습니다: {str(e)}"
            logger.error(f"처리 중 오류 발생 - {str(e)}")
            message_placeholder.markdown(error_message)
            
            # 오류 발생 시 응답 생성 (고유 ID 포함)
            error_response: Message = {
                "role": "assistant", 
                "content": error_message,
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "id": str(uuid.uuid4()),
                "error": str(e)
            }
            st.session_state.messages.append(error_response)
            
            # 오류 메시지를 DynamoDB에 저장
            if current_session_id:
                try:
                    message_manager = get_message_history_manager()
                    
                    # 오류 상태에 대한 GraphState 구성
                    from modules.states.graph_state import GraphState
                    error_graph_state = GraphState()
                    error_graph_state["query"] = user_query if 'user_query' in locals() else ""
                    error_graph_state["error"] = str(e)
                    if 'schema_info' in locals() and schema_info:
                        error_graph_state["schema_info"] = schema_info
                    
                    saved_message = message_manager.save_message(
                        session_id=current_session_id,
                        message_type=MessageType.ASSISTANT,
                        content=error_message,
                        sender="assistant",
                        metadata={"is_error": True, "error_details": str(e)},
                        graph_state=error_graph_state  # 오류 상태 GraphState 추가
                    )
                    if saved_message:
                        logger.info(f"오류 메시지 저장 성공: {saved_message.message_id}")
                except Exception as save_error:
                    logger.error(f"오류 메시지 저장 중 오류: {save_error}")
            
            # 오류 발생 시에도 처리 완료 상태로 설정
            st.session_state.is_processing = False
            
            # 페이지 리프레시
            st.rerun()

# 채팅 기록 지우기 버튼
if st.button("채팅 기록 지우기"):
    st.session_state.messages = []
    st.session_state.current_query = None
    st.session_state.current_result = None
    st.session_state.current_graph = None
    st.session_state.error = None
    st.session_state.schema_info = None
    st.rerun()
