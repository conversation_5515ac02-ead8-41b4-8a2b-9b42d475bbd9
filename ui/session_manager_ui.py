"""
Streamlit 세션 관리 UI 컴포넌트

이 모듈은 Streamlit 애플리케이션에서 채팅 세션을 관리하는 UI 컴포넌트를 제공합니다.
세션 목록, 생성, 선택, 삭제, 편집 등의 기능을 포함합니다.
"""

import streamlit as st
import pandas as pd
from datetime import datetime, timezone, timedelta
from typing import Optional, List, Dict, Tuple
import logging
import json
import tempfile
import traceback
import uuid
import time

# 절대 import로 변경
from modules.utils import get_message_history_manager, get_session_manager, MessageType
from modules.utils.dynamodb_models import SessionStatus

logger = logging.getLogger(__name__)


class SessionManagerUI:
    """세션 관리 UI 클래스"""
    
    def __init__(self):
        self.session_manager = get_session_manager()
        self.message_manager = get_message_history_manager()
        
        # 세션 상태 초기화
        self._initialize_session_state()
    
    def _initialize_session_state(self):
        """Streamlit 세션 상태를 초기화합니다."""
        # 기본 세션 상태 초기화
        if "current_session_id" not in st.session_state:
            st.session_state.current_session_id = None
        
        if "session_list" not in st.session_state:
            st.session_state.session_list = []
        
        if "session_list_last_updated" not in st.session_state:
            st.session_state.session_list_last_updated = None
        
        if "show_session_dialog" not in st.session_state:
            st.session_state.show_session_dialog = False
        
        if "edit_session_id" not in st.session_state:
            st.session_state.edit_session_id = None
            
        # messages 초기화 (웹 새로고침 시 빈 상태로 시작)
        if "messages" not in st.session_state:
            st.session_state.messages = []
            
        # 세션 복원 플래그 초기화
        if "session_restored" not in st.session_state:
            st.session_state.session_restored = False
            
        # 현재 세션 ID 로깅
        logger.info(f"세션 초기화 시작 - current_session_id: {st.session_state.current_session_id}")
        
        # 웹 새로고침 등으로 세션이 None이고 아직 복원되지 않은 경우 자동 복원 시도
        if (not st.session_state.current_session_id and 
            not st.session_state.session_restored):
            
            logger.info("세션 복원 조건 충족, 복원 시도...")
            if self._restore_last_active_session():
                st.session_state.session_restored = True
                logger.info(f"세션 복원 성공: {st.session_state.current_session_id}")
            else:
                logger.info("복원 가능한 세션이 없음")
                st.session_state.session_restored = True  # 더 이상 시도하지 않도록 설정
    
    def _restore_last_active_session(self):
        """마지막 활성 세션을 복원합니다."""
        try:
            logger.info("마지막 활성 세션 복원 시도 중...")
            
            # 최근 세션 목록 조회
            sessions = self.session_manager.get_recent_sessions(limit=10)
            logger.info(f"최근 세션 조회 결과: {len(sessions)}개")
            
            if sessions:
                # 가장 최근 활성 세션 찾기
                active_session = None
                for i, session in enumerate(sessions):
                    logger.info(f"세션 {i+1}: {session.session_id[:12]}... - {session.title} - {session.status}")
                    if session.status == SessionStatus.ACTIVE:
                        active_session = session
                        break
                
                if active_session:
                    logger.info(f"활성 세션 발견: {active_session.session_id} - {active_session.title}")
                    
                    # 세션 ID 설정
                    old_session_id = st.session_state.current_session_id
                    st.session_state.current_session_id = active_session.session_id
                    logger.info(f"세션 ID 업데이트: {old_session_id} -> {active_session.session_id}")
                    
                    # 세션 히스토리 로드
                    self._load_session_history(active_session.session_id)
                    
                    logger.info(f"세션 복원 완료: {active_session.session_id}")
                    return True
                else:
                    logger.info("활성 세션이 없음")
                    
                    # 활성 세션이 없으면 가장 최근 세션을 활성화
                    if sessions:
                        recent_session = sessions[0]
                        logger.info(f"가장 최근 세션을 활성화: {recent_session.session_id}")
                        
                        # 세션 상태를 ACTIVE로 변경
                        self.session_manager.update_session(
                            recent_session.session_id,
                            status=SessionStatus.ACTIVE
                        )
                        
                        st.session_state.current_session_id = recent_session.session_id
                        self._load_session_history(recent_session.session_id)
                        
                        logger.info(f"세션 활성화 및 복원 완료: {recent_session.session_id}")
                        return True
            else:
                logger.info("저장된 세션이 없음")
                
        except Exception as e:
            logger.error(f"세션 복원 실패: {e}")
            import traceback
            logger.error(f"상세 오류: {traceback.format_exc()}")
            
        return False
    
    def render_session_sidebar(self):
        """사이드바에 세션 관리 컴포넌트를 렌더링합니다."""
        st.sidebar.markdown("---")
        st.sidebar.header("💬 세션 관리")
        
        # 새 세션 생성 버튼
        col1, col2 = st.sidebar.columns([3, 1])
        with col1:
            if st.button("🆕 새 세션", use_container_width=True):
                self._show_new_session_dialog()
        
        with col2:
            if st.button("🔄", help="세션 목록 새로고침"):
                self._refresh_session_list()
        
        # 세션 목록 표시
        self._render_session_list()
        
        # 현재 세션 정보 표시
        self._render_current_session_info()
        
        # 채팅 히스토리 표시 (새로 추가)
        self._render_chat_history()
        
        # 세션 관리 다이얼로그
        self._render_session_dialogs()
    
    def _show_new_session_dialog(self):
        """새 세션 생성 다이얼로그를 표시합니다."""
        st.session_state.show_session_dialog = True
    
    def _refresh_session_list(self):
        """세션 목록을 새로고침합니다."""
        try:
            sessions = self.session_manager.get_recent_sessions(limit=20)
            st.session_state.session_list = sessions
            st.session_state.session_list_last_updated = datetime.now()
            st.sidebar.success("세션 목록이 새로고침되었습니다!")
        except Exception as e:
            logger.error(f"세션 목록 새로고침 실패: {e}")
            st.sidebar.error("세션 목록 새로고침에 실패했습니다.")
    
    def _render_session_list(self):
        """세션 목록을 렌더링합니다."""
        # 세션 목록이 비어있거나 오래된 경우 자동 새로고침
        if (not st.session_state.session_list or 
            not st.session_state.session_list_last_updated or
            (datetime.now() - st.session_state.session_list_last_updated).seconds > 300):  # 5분
            self._refresh_session_list()
        
        if not st.session_state.session_list:
            st.sidebar.info("생성된 세션이 없습니다.")
            return
        
        st.sidebar.subheader("📋 세션 목록")
        
        # 세션 목록 표시
        for session in st.session_state.session_list:
            self._render_session_item(session)
    
    def _render_session_item(self, session):
        """개별 세션 아이템을 렌더링합니다."""
        # 현재 선택된 세션인지 확인
        is_current = st.session_state.current_session_id == session.session_id
        
        # 세션 상태에 따른 아이콘
        status_icon = {
            SessionStatus.ACTIVE: "🟢",
            SessionStatus.INACTIVE: "⚪",
            SessionStatus.ARCHIVED: "📦"
        }.get(session.status, "❓")
        
        # 세션 제목 (최대 30자)
        title = session.title if session.title else f"세션 {session.session_id[:8]}"
        if len(title) > 30:
            title = title[:27] + "..."
        
        # 세션 정보 컨테이너
        with st.sidebar.container():
            # 세션 선택 버튼
            button_style = "🔹 " if is_current else ""
            session_label = f"{button_style}{status_icon} {title}"
            
            if st.button(session_label, key=f"session_{session.session_id}", use_container_width=True):
                self._select_session(session.session_id)
            
            # 세션 관리 버튼들 (현재 세션인 경우만 표시)
            if is_current:
                col1, col2, col3 = st.sidebar.columns(3)
                
                with col1:
                    if st.button("✏️", key=f"edit_{session.session_id}", help="세션 편집"):
                        st.session_state.edit_session_id = session.session_id
                
                with col2:
                    if st.button("📤", key=f"export_{session.session_id}", help="세션 내보내기"):
                        self._export_session(session.session_id)
                
                with col3:
                    if st.button("🗑️", key=f"delete_{session.session_id}", help="세션 삭제"):
                        self._delete_session(session.session_id)
                
                # 세션 통계 정보
                self._render_session_stats(session)
    
    def _render_session_stats(self, session):
        """세션 통계 정보를 렌더링합니다."""
        try:
            # 메시지 통계 조회
            stats = self.message_manager.get_message_statistics(session.session_id)
            
            if stats and stats.get('total_messages', 0) > 0:
                with st.sidebar.expander("📊 세션 통계", expanded=False):
                    st.write(f"💬 총 메시지: {stats['total_messages']}개")
                    st.write(f"❓ 질문: {stats.get('questions', 0)}개")
                    st.write(f"🔍 SQL 쿼리: {stats.get('messages_with_sql', 0)}개")
                    
                    # 최근 활동 시간
                    if stats.get('date_range', {}).get('last_message'):
                        last_msg_time = datetime.fromisoformat(stats['date_range']['last_message'])
                        time_diff = datetime.now(timezone.utc) - last_msg_time
                        
                        if time_diff.days > 0:
                            st.write(f"🕒 마지막 활동: {time_diff.days}일 전")
                        elif time_diff.seconds > 3600:
                            hours = time_diff.seconds // 3600
                            st.write(f"🕒 마지막 활동: {hours}시간 전")
                        else:
                            minutes = time_diff.seconds // 60
                            st.write(f"🕒 마지막 활동: {minutes}분 전")
        
        except Exception as e:
            logger.warning(f"세션 통계 조회 실패: {e}")
    
    def _select_session(self, session_id: str):
        """세션을 선택하고 히스토리를 로드합니다."""
        try:
            logger.info(f"세션 선택 시작: {session_id}")
            
            # 현재 세션 ID 업데이트
            old_session_id = st.session_state.current_session_id
            st.session_state.current_session_id = session_id
            
            logger.info(f"세션 ID 변경: {old_session_id} -> {session_id}")
            
            # 세션 히스토리 로드
            self._load_session_history(session_id)
            
            # 세션 상태 업데이트
            session = self.session_manager.get_session(session_id)
            if session:
                logger.info(f"세션 정보 로드 성공: {session.title} (메시지 수: {len(st.session_state.messages)})")
            else:
                logger.warning(f"세션 정보 로드 실패: {session_id}")
            
            # UI 새로고침
            st.rerun()
            
        except Exception as e:
            logger.error(f"세션 선택 실패: {e}")
            import traceback
            logger.error(f"상세 오류: {traceback.format_exc()}")
            st.sidebar.error("세션 선택에 실패했습니다.")
    
    def _load_session_history(self, session_id: str):
        """세션의 메시지 히스토리를 로드합니다."""
        max_retries = 3
        retry_count = 0
        load_success = False
        
        logger.info(f"세션 히스토리 로드 시작: {session_id}")
        
        while retry_count < max_retries and not load_success:
            try:
                message_manager = get_message_history_manager()
                logger.info(f"메시지 매니저 획득 성공 (시도 {retry_count + 1})")
                
                # 메시지와 GraphState를 함께 조회 (최대 100개)
                message_state_pairs, next_key = message_manager.get_session_messages_with_states(
                    session_id=session_id,
                    limit=100,
                    ascending=True  # 오래된 메시지부터 로드
                )
                
                logger.info(f"DynamoDB에서 메시지-상태 조회 완료: {len(message_state_pairs)}개 쌍")
                
                # Streamlit 메시지 형식으로 변환
                streamlit_messages = []
                latest_graph_state = None  # 가장 최신의 GraphState 저장
                
                for msg, graph_state in message_state_pairs:
                    try:
                        # 메시지 타입에 따른 role 결정
                        role = "user" if msg.message_type == MessageType.USER else "assistant"
                        
                        # 기본 메시지 구조 생성
                        message_dict = {
                            "role": role,
                            "content": msg.content,
                            "timestamp": msg.timestamp.strftime("%Y-%m-%d %H:%M:%S"),
                            "id": getattr(msg, 'message_id', str(uuid.uuid4()))
                        }
                        
                        # GraphState가 있으면 추가 정보 추출
                        if graph_state:
                            latest_graph_state = graph_state  # 가장 최신 상태 업데이트
                            
                            # GraphState에서 데이터 추출
                            if graph_state.get("sql"):
                                message_dict["sql"] = graph_state["sql"]
                            
                            if graph_state.get("data") is not None:
                                message_dict["data"] = graph_state["data"]
                            
                            if graph_state.get("graph") is not None:
                                message_dict["graph"] = graph_state["graph"]
                            
                            if graph_state.get("explanation"):
                                message_dict["explanation"] = graph_state["explanation"]
                            
                            if graph_state.get("analysis_display"):
                                message_dict["analysis_display"] = graph_state["analysis_display"]
                            
                            if graph_state.get("query_type"):
                                message_dict["query_type"] = graph_state["query_type"]
                            
                            if graph_state.get("tables_used"):
                                message_dict["tables_used"] = graph_state["tables_used"]
                            
                            if graph_state.get("columns_used"):
                                message_dict["columns_used"] = graph_state["columns_used"]
                        
                        # 기존 메타데이터에서도 정보 추출 (GraphState 정보와 중복되지 않는 경우)
                        if msg.metadata and not graph_state:
                            # SQL 쿼리
                            if "sql_query" in msg.metadata:
                                message_dict["sql"] = msg.metadata["sql_query"]
                            
                            # 데이터
                            if "data" in msg.metadata:
                                data = msg.metadata["data"]
                                if isinstance(data, str):
                                    try:
                                        # JSON 문자열을 DataFrame으로 변환 시도
                                        import json
                                        data_dict = json.loads(data)
                                        if isinstance(data_dict, list) and len(data_dict) > 0:
                                            message_dict["data"] = pd.DataFrame(data_dict)
                                        else:
                                            message_dict["data"] = data
                                    except:
                                        message_dict["data"] = data
                                else:
                                    message_dict["data"] = data
                            
                            # 분석 결과
                            if "analysis_display" in msg.metadata:
                                message_dict["analysis_display"] = msg.metadata["analysis_display"]
                        
                        streamlit_messages.append(message_dict)
                        
                    except Exception as msg_error:
                        logger.error(f"메시지 변환 실패: {msg_error}")
                        continue
                
                # Streamlit 세션 상태에 저장
                st.session_state.messages = streamlit_messages
                logger.info(f"세션 히스토리 로드 완료: {len(streamlit_messages)}개 메시지")
                
                # 최신 GraphState를 세션 상태에 저장 (향후 워크플로우 연속성을 위해)
                if latest_graph_state:
                    st.session_state.latest_graph_state = latest_graph_state
                    logger.info(f"최신 GraphState 복원 완료: {latest_graph_state.get('query_type', 'unknown')} 타입")
                    
                    # 스키마 정보가 있으면 복원
                    if latest_graph_state.get("schema_info"):
                        st.session_state.schema_info = latest_graph_state["schema_info"]
                        logger.info("스키마 정보도 복원됨")
                
                # 세션 정보 업데이트
                if len(streamlit_messages) > 0:
                    try:
                        session_manager = get_session_manager()
                        messages = [pair[0] for pair in message_state_pairs]  # 메시지만 추출
                        session_manager.update_session(
                            session_id,
                            message_count=len(streamlit_messages),
                            last_activity_at=messages[-1].timestamp if messages else datetime.now(timezone.utc)
                        )
                    except Exception as update_error:
                        logger.warning(f"세션 정보 업데이트 실패: {update_error}")
                
                load_success = True
                
            except Exception as e:
                retry_count += 1
                logger.error(f"히스토리 로드 실패 (시도 {retry_count}/{max_retries}): {e}")
                import traceback
                logger.error(f"상세 오류: {traceback.format_exc()}")
                
                if retry_count < max_retries:
                    logger.info(f"재시도 중... ({retry_count + 1}/{max_retries})")
                    time.sleep(1)  # 재시도 전 잠시 대기
                else:
                    logger.error("히스토리 로드 최종 실패")
                    # 실패 시 빈 메시지 리스트로 초기화
                    st.session_state.messages = []
    
    def _export_session(self, session_id: str):
        """세션을 내보냅니다."""
        try:
            # 세션 데이터 내보내기
            session_data = self.session_manager.export_session(session_id, format='json')
            
            if session_data:
                # 다운로드 버튼 생성
                st.sidebar.download_button(
                    label="💾 JSON 다운로드",
                    data=session_data,
                    file_name=f"session_{session_id[:8]}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                    mime="application/json"
                )
                st.sidebar.success("세션 내보내기가 준비되었습니다!")
            else:
                st.sidebar.error("세션 내보내기에 실패했습니다.")
                
        except Exception as e:
            logger.error(f"세션 내보내기 실패: {e}")
            st.sidebar.error("세션 내보내기에 실패했습니다.")
    
    def _delete_session(self, session_id: str):
        """세션을 삭제합니다."""
        # 삭제 확인 다이얼로그
        if st.sidebar.button(f"⚠️ 정말 삭제하시겠습니까?", key=f"confirm_delete_{session_id}"):
            try:
                if self.session_manager.delete_session(session_id):
                    # 현재 세션이 삭제된 경우 초기화
                    if st.session_state.current_session_id == session_id:
                        st.session_state.current_session_id = None
                        st.session_state.messages = []
                    
                    # 세션 목록 새로고침
                    self._refresh_session_list()
                    
                    st.sidebar.success("세션이 삭제되었습니다!")
                    st.rerun()
                else:
                    st.sidebar.error("세션 삭제에 실패했습니다.")
                    
            except Exception as e:
                logger.error(f"세션 삭제 실패: {e}")
                st.sidebar.error("세션 삭제에 실패했습니다.")
    
    def _render_current_session_info(self):
        """현재 세션 정보를 표시합니다."""
        if not st.session_state.current_session_id:
            st.sidebar.info("🔍 활성 세션이 없습니다")
            
            # 세션이 없는 경우 복원 버튼 제공
            if st.sidebar.button("🔄 세션 복원", help="마지막 활성 세션을 복원합니다"):
                logger.info("수동 세션 복원 버튼 클릭됨")
                if self._restore_last_active_session():
                    logger.info("수동 세션 복원 성공")
                    st.rerun()
                else:
                    logger.warning("수동 세션 복원 실패")
                    st.sidebar.warning("복원할 활성 세션이 없습니다")
            return
        
        session = self.session_manager.get_session(st.session_state.current_session_id)
        if session:
            st.sidebar.markdown("---")
            st.sidebar.subheader("📍 현재 세션")
            
            # 세션 제목
            title = session.title if session.title else f"세션 {session.session_id[:8]}"
            st.sidebar.markdown(f"**{title}**")
            
            # 세션 상태
            status_icons = {
                SessionStatus.ACTIVE: "🟢 활성",
                SessionStatus.INACTIVE: "⚪ 비활성",
                SessionStatus.ARCHIVED: "📦 보관됨"
            }
            status_text = status_icons.get(session.status, "❓ 알 수 없음")
            st.sidebar.markdown(f"상태: {status_text}")
            
            # 세션 ID (간략하게)
            st.sidebar.markdown(f"ID: `{session.session_id[:12]}...`")
            
            # 생성 시간
            if session.created_at:
                created_time = session.created_at.strftime("%m/%d %H:%M")
                st.sidebar.markdown(f"생성: {created_time}")
            
            # 메시지 수
            if hasattr(st.session_state, 'messages') and st.session_state.messages:
                message_count = len(st.session_state.messages)
                st.sidebar.markdown(f"메시지: {message_count}개")
                
                # 마지막 메시지 시간
                if message_count > 0:
                    try:
                        last_message = st.session_state.messages[-1]
                        if last_message.get("timestamp"):
                            last_time = last_message["timestamp"]
                            st.sidebar.markdown(f"마지막: {last_time}")
                    except:
                        pass
            else:
                st.sidebar.markdown("메시지: 0개")
                
                # 메시지가 없는 경우 히스토리 로드 버튼 제공
                if st.sidebar.button("📥 히스토리 로드", help="세션의 메시지 히스토리를 다시 로드합니다"):
                    logger.info("수동 히스토리 로드 버튼 클릭됨")
                    self._load_session_history(st.session_state.current_session_id)
                    st.rerun()
            
            # 세션 복원 상태 표시 (디버그 정보)
            server_id = getattr(self.session_manager, 'server_id', 'unknown')
            st.sidebar.caption(f"🔧 서버: {server_id[:8]}...")
            
        else:
            st.sidebar.warning(f"⚠️ 세션 정보 로드 실패: {st.session_state.current_session_id[:12]}...")
            
            # 세션 정보 로드 실패 시 새로고침 버튼 제공
            if st.sidebar.button("🔄 세션 새로고침", help="세션 정보를 다시 로드합니다"):
                logger.info("세션 새로고침 버튼 클릭됨")
                # 세션 목록 새로고침
                self._refresh_session_list()
                # 현재 세션 정보 다시 확인
                session = self.session_manager.get_session(st.session_state.current_session_id)
                if session:
                    logger.info("세션 새로고침 성공")
                    st.rerun()
                else:
                    logger.warning("세션 새로고침 실패, 세션 초기화")
                    st.session_state.current_session_id = None
                    st.rerun()
    
    def _render_session_dialogs(self):
        """세션 관리 다이얼로그를 렌더링합니다."""
        # 새 세션 생성 다이얼로그
        if st.session_state.show_session_dialog:
            self._render_new_session_dialog()
        
        # 세션 편집 다이얼로그
        if st.session_state.edit_session_id:
            self._render_edit_session_dialog()
    
    def _render_new_session_dialog(self):
        """새 세션 생성 다이얼로그를 렌더링합니다."""
        with st.sidebar.form("new_session_form"):
            st.subheader("🆕 새 세션 생성")
            
            session_title = st.text_input(
                "세션 제목",
                placeholder="예: 매출 분석 세션",
                max_chars=100
            )
            
            session_description = st.text_area(
                "세션 설명 (선택사항)",
                placeholder="이 세션에서 수행할 작업에 대한 설명을 입력하세요.",
                max_chars=500,
                height=100
            )
            
            col1, col2 = st.columns(2)
            
            with col1:
                if st.form_submit_button("✅ 생성", use_container_width=True):
                    self._create_new_session(session_title, session_description)
            
            with col2:
                if st.form_submit_button("❌ 취소", use_container_width=True):
                    st.session_state.show_session_dialog = False
                    st.rerun()
    
    def _create_new_session(self, title: str, description: str = ""):
        """새 세션을 생성합니다."""
        try:
            # 제목이 비어있으면 기본 제목 생성
            if not title.strip():
                title = f"새 세션 {datetime.now().strftime('%Y-%m-%d %H:%M')}"
            
            # 새 세션 생성
            session = self.session_manager.create_new_session(title.strip(), description.strip())
            
            if session:
                # 새 세션으로 전환
                st.session_state.current_session_id = session.session_id
                st.session_state.messages = []  # 메시지 초기화
                
                # 다이얼로그 닫기
                st.session_state.show_session_dialog = False
                
                # 세션 목록 새로고침
                self._refresh_session_list()
                
                st.sidebar.success(f"새 세션이 생성되었습니다: {title}")
                st.rerun()
            else:
                st.sidebar.error("세션 생성에 실패했습니다.")
                
        except Exception as e:
            logger.error(f"새 세션 생성 실패: {e}")
            st.sidebar.error("세션 생성에 실패했습니다.")
    
    def _render_edit_session_dialog(self):
        """세션 편집 다이얼로그를 렌더링합니다."""
        try:
            session = self.session_manager.get_session(st.session_state.edit_session_id)
            if not session:
                st.sidebar.error("세션을 찾을 수 없습니다.")
                st.session_state.edit_session_id = None
                return
            
            with st.sidebar.form("edit_session_form"):
                st.subheader("✏️ 세션 편집")
                
                new_title = st.text_input(
                    "세션 제목",
                    value=session.title,
                    max_chars=100
                )
                
                new_description = st.text_area(
                    "세션 설명",
                    value=session.description or "",
                    max_chars=500,
                    height=100
                )
                
                new_status = st.selectbox(
                    "세션 상태",
                    options=[status.value for status in SessionStatus],
                    index=[status.value for status in SessionStatus].index(session.status.value)
                )
                
                col1, col2 = st.columns(2)
                
                with col1:
                    if st.form_submit_button("✅ 저장", use_container_width=True):
                        self._update_session(
                            st.session_state.edit_session_id,
                            new_title,
                            new_description,
                            SessionStatus(new_status)
                        )
                
                with col2:
                    if st.form_submit_button("❌ 취소", use_container_width=True):
                        st.session_state.edit_session_id = None
                        st.rerun()
        
        except Exception as e:
            logger.error(f"세션 편집 다이얼로그 렌더링 실패: {e}")
            st.sidebar.error("세션 편집 다이얼로그 로드에 실패했습니다.")
    
    def _update_session(self, session_id: str, title: str, description: str, status: SessionStatus):
        """세션 정보를 업데이트합니다."""
        try:
            success = self.session_manager.update_session(
                session_id=session_id,
                title=title.strip() if title.strip() else None,
                description=description.strip() if description.strip() else None,
                status=status
            )
            
            if success:
                # 편집 다이얼로그 닫기
                st.session_state.edit_session_id = None
                
                # 세션 목록 새로고침
                self._refresh_session_list()
                
                st.sidebar.success("세션 정보가 업데이트되었습니다!")
                st.rerun()
            else:
                st.sidebar.error("세션 업데이트에 실패했습니다.")
                
        except Exception as e:
            logger.error(f"세션 업데이트 실패: {e}")
            st.sidebar.error("세션 업데이트에 실패했습니다.")
    
    def get_current_session_id(self) -> Optional[str]:
        """현재 선택된 세션 ID를 반환합니다."""
        return st.session_state.current_session_id
    
    def ensure_session_exists(self) -> str:
        """세션이 없으면 자동으로 생성하고 세션 ID를 반환합니다."""
        logger.info(f"ensure_session_exists 호출 - 현재 세션 ID: {st.session_state.current_session_id}")
        
        # 세션이 없는 경우 복원 시도
        if not st.session_state.current_session_id:
            logger.info("세션 ID가 없음, 복원 시도...")
            
            # 복원 시도
            if self._restore_last_active_session():
                logger.info(f"세션 복원 성공: {st.session_state.current_session_id}")
                return st.session_state.current_session_id
            
            # 복원 실패 시 새 세션 생성
            logger.info("세션 복원 실패, 새 세션 생성...")
            session = self.session_manager.create_new_session(
                f"새 세션 {datetime.now().strftime('%Y-%m-%d %H:%M')}"
            )
            if session:
                st.session_state.current_session_id = session.session_id
                st.session_state.messages = []  # 새 세션이므로 메시지 초기화
                self._refresh_session_list()
                logger.info(f"새 세션 생성 완료: {session.session_id}")
            else:
                logger.error("새 세션 생성 실패")
        else:
            # 세션은 있지만 메시지가 없는 경우 히스토리 로드
            if not hasattr(st.session_state, 'messages') or not st.session_state.messages:
                logger.info("세션은 있지만 메시지가 없음, 히스토리 로드...")
                self._load_session_history(st.session_state.current_session_id)
        
        return st.session_state.current_session_id

    def _render_chat_history(self):
        """현재 세션의 채팅 히스토리를 사이드바에 질문-답변 쌍으로 표시합니다."""
        if not st.session_state.current_session_id:
            st.sidebar.info("🔍 세션이 선택되지 않았습니다.")
            return
        
        # 디버깅: 메시지 상태 확인
        messages_count = len(st.session_state.messages) if hasattr(st.session_state, 'messages') and st.session_state.messages else 0
        logger.info(f"히스토리 렌더링 시작 - 세션 ID: {st.session_state.current_session_id}, 메시지 수: {messages_count}")
        
        if not hasattr(st.session_state, 'messages') or not st.session_state.messages:
            st.sidebar.info(f"💭 대화 히스토리가 없습니다. (메시지 수: {messages_count})")
            
            # 세션 히스토리 다시 로드 시도
            if st.sidebar.button("🔄 히스토리 새로고침", key="refresh_history"):
                logger.info("히스토리 새로고침 버튼 클릭됨")
                self._load_session_history(st.session_state.current_session_id)
                st.rerun()
            return
        
        st.sidebar.markdown("---")
        
        # 히스토리 헤더와 통계
        col1, col2 = st.sidebar.columns([3, 1])
        with col1:
            st.sidebar.subheader("💭 대화 히스토리")
        with col2:
            total_messages = len(st.session_state.messages)
            st.sidebar.write(f"**{total_messages}**")
        
        logger.info(f"히스토리 표시 중 - 총 메시지 수: {total_messages}")
        
        # 히스토리 표시 옵션 (컴팩트하게)
        st.sidebar.markdown("**⚙️ 표시 옵션**")
        col1, col2 = st.sidebar.columns(2)
        with col1:
            show_timestamps = st.checkbox("⏰ 시간", value=False, key="show_timestamps")
        with col2:
            max_messages = st.selectbox("📝 개수", [5, 10, 20, 50], index=1, key="max_messages")
        
        col1, col2 = st.sidebar.columns(2)
        with col1:
            message_filter = st.selectbox("🔍 필터", ["전체", "사용자만", "AI만", "SQL 포함만"], key="message_filter")
        with col2:
            compact_view = st.checkbox("📋 간단히", value=True, key="compact_view")
        
        show_sql = st.checkbox("🔍 SQL 표시", value=False, key="show_sql")
        
        # 메시지 필터링
        filtered_messages = self._filter_messages(st.session_state.messages, message_filter)
        logger.info(f"필터링된 메시지 수: {len(filtered_messages)}")
        
        # 표시할 메시지 수 제한
        display_messages = filtered_messages[-max_messages:] if len(filtered_messages) > max_messages else filtered_messages
        
        # 메시지를 QA 쌍으로 그룹화
        qa_pairs = self._group_messages_into_qa_pairs(display_messages)
        logger.info(f"QA 쌍 수: {len(qa_pairs)}")
        
        if qa_pairs:
            st.sidebar.markdown(f"**표시 중: {len(qa_pairs)}개 대화 쌍**")
            
            # QA 쌍을 역순으로 표시 (최신 대화가 위에)
            for i, qa_pair in enumerate(reversed(qa_pairs)):
                self._render_qa_pair(qa_pair, i, show_timestamps, show_sql, compact_view)
        else:
            st.sidebar.info("표시할 대화가 없습니다.")
        
        # 히스토리 검색 기능
        self._show_history_search()
        
        # 히스토리 내보내기 기능
        self._show_history_export()
    
    def _filter_messages(self, messages: List[Dict], filter_type: str) -> List[Dict]:
        """메시지를 필터링합니다."""
        if filter_type == "전체":
            return messages
        elif filter_type == "사용자만":
            return [msg for msg in messages if msg.get("role") == "user"]
        elif filter_type == "AI만":
            return [msg for msg in messages if msg.get("role") == "assistant"]
        elif filter_type == "SQL 포함만":
            return [msg for msg in messages if msg.get("sql") or "```sql" in msg.get("content", "")]
        else:
            return messages
    
    def _group_messages_into_qa_pairs(self, messages: List[Dict]) -> List[Dict]:
        """메시지를 질문-답변 쌍으로 그룹화합니다."""
        qa_pairs = []
        current_question = None
        
        for message in messages:
            role = message.get("role", "")
            
            if role == "user":
                # 새로운 질문 시작
                current_question = message
            elif role == "assistant" and current_question:
                # 질문에 대한 답변
                qa_pair = {
                    "question": current_question,
                    "answer": message,
                    "pair_id": f"qa_{len(qa_pairs)}"
                }
                qa_pairs.append(qa_pair)
                current_question = None  # 답변 완료
            elif role == "assistant" and not current_question:
                # 질문 없이 AI가 먼저 말한 경우 (시스템 메시지 등)
                qa_pair = {
                    "question": None,
                    "answer": message,
                    "pair_id": f"qa_{len(qa_pairs)}"
                }
                qa_pairs.append(qa_pair)
        
        # 답변이 없는 마지막 질문이 있는 경우 추가
        if current_question:
            qa_pair = {
                "question": current_question,
                "answer": None,
                "pair_id": f"qa_{len(qa_pairs)}"
            }
            qa_pairs.append(qa_pair)
        
        return qa_pairs
    
    def _render_qa_pair(self, qa_pair: Dict, index: int, show_timestamps: bool, show_sql: bool, compact_view: bool):
        """질문-답변 쌍을 렌더링합니다."""
        question = qa_pair.get("question")
        answer = qa_pair.get("answer")
        
        # 대화 번호 계산 (역순이므로)
        pair_number = f"#{index + 1}"
        
        # 질문 내용 미리보기
        if question:
            q_content = question.get("content", "")
            q_preview = q_content[:50] + "..." if len(q_content) > 50 else q_content
        else:
            q_preview = "시스템 메시지"
        
        # 대화 쌍 헤더
        pair_header = f"💬 {pair_number} {q_preview}"
        
        # expander 대신 간단한 컨테이너 사용
        st.sidebar.markdown(f"**{pair_header}**")
        
        # 질문 표시
        if question:
            self._render_message_in_pair(question, "질문", "👤", show_timestamps, compact_view)
        
        # 답변 표시
        if answer:
            self._render_message_in_pair(answer, "답변", "🤖", show_timestamps, compact_view, show_sql)
        elif question:
            st.sidebar.markdown("⏳ **답변 대기 중...**")
        
        # 구분선
        st.sidebar.markdown("<hr style='margin: 8px 0; border: 0.5px solid #ddd;'>", unsafe_allow_html=True)
    
    def _render_message_in_pair(self, message: Dict, label: str, icon: str, show_timestamps: bool, compact_view: bool, show_sql: bool = False):
        """대화 쌍 내에서 개별 메시지를 렌더링합니다."""
        content = message.get("content", "")
        timestamp = message.get("timestamp")
        role = message.get("role", "")
        
        # 시간 정보 포맷팅
        time_str = ""
        if show_timestamps and timestamp:
            try:
                if isinstance(timestamp, str):
                    dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                else:
                    dt = timestamp
                time_str = f" ({dt.strftime('%H:%M')})"
            except:
                pass
        
        # 메시지 헤더
        header = f"**{icon} {label}{time_str}**"
        st.sidebar.markdown(header)
        
        if compact_view:
            # 간단한 표시 모드
            if role == "user":
                # 사용자 질문은 그대로 표시
                st.sidebar.markdown(f"*{content}*")
            else:
                # AI 답변은 요약해서 표시
                if len(content) > 100:
                    content_preview = content[:100] + "..."
                    st.sidebar.markdown(f"*{content_preview}*")
                    # 전체 답변 보기는 expander 대신 간단한 텍스트로
                    if st.sidebar.button(f"전체 답변 보기", key=f"full_answer_{message.get('id', hash(content))}"):
                        st.sidebar.text_area("전체 답변", content, height=200, key=f"full_content_{message.get('id', hash(content))}")
                else:
                    st.sidebar.markdown(f"*{content}*")
        else:
            # 상세 표시 모드
            st.sidebar.markdown(content)
        
        # SQL 쿼리 표시 (AI 답변이고 SQL이 있는 경우)
        if show_sql and role == "assistant" and message.get("sql"):
            st.sidebar.markdown("**🔍 실행된 SQL 쿼리:**")
            st.code(message.get("sql"), language="sql")
        
        # 분석 결과 표시 (AI 답변이고 분석이 있는 경우)
        if role == "assistant" and message.get("analysis_display"):
            st.sidebar.markdown("**📊 분석 결과:**")
            analysis = message.get("analysis_display")
            if len(analysis) > 150:
                st.sidebar.markdown(f"*{analysis[:150]}...*")
            else:
                st.sidebar.markdown(f"*{analysis}*")
        
        # 데이터 정보 표시 (AI 답변이고 데이터가 있는 경우)
        if role == "assistant" and message.get("data") is not None:
            try:
                data = message.get("data")
                if hasattr(data, 'shape'):
                    st.sidebar.caption(f"📋 데이터: {data.shape[0]}행 × {data.shape[1]}열")
                else:
                    st.sidebar.caption("📋 데이터 있음")
            except:
                st.sidebar.caption("📋 데이터 있음")
        
        # 그래프 정보 표시 (AI 답변이고 그래프가 있는 경우)
        if role == "assistant" and message.get("graph") is not None:
            graph_type = message.get("graph_type", "차트")
            st.sidebar.caption(f"📈 {graph_type} 그래프 생성됨")
        
        st.sidebar.markdown("")  # 여백 추가
    
    def _show_history_search(self):
        """히스토리 검색 다이얼로그를 표시합니다."""
        # 세션 상태에 검색 다이얼로그 플래그 추가
        if "show_history_search" not in st.session_state:
            st.session_state.show_history_search = False
        
        st.session_state.show_history_search = True
        
        # 검색 폼
        with st.sidebar.form("history_search_form"):
            st.subheader("🔍 히스토리 검색")
            
            search_query = st.text_input(
                "검색어",
                placeholder="검색할 키워드를 입력하세요",
                key="history_search_query"
            )
            
            search_in = st.selectbox(
                "검색 범위",
                ["메시지 내용", "SQL 쿼리", "전체"],
                key="history_search_scope"
            )
            
            col1, col2 = st.columns(2)
            with col1:
                if st.form_submit_button("🔍 검색"):
                    self._perform_history_search(search_query, search_in)
            with col2:
                if st.form_submit_button("❌ 닫기"):
                    st.session_state.show_history_search = False
                    st.rerun()
    
    def _perform_history_search(self, query: str, scope: str):
        """히스토리 검색을 수행합니다."""
        if not query.strip():
            st.sidebar.warning("검색어를 입력해주세요.")
            return
        
        try:
            # 메시지를 QA 쌍으로 그룹화
            qa_pairs = self._group_messages_into_qa_pairs(st.session_state.messages)
            
            # QA 쌍에서 검색
            results = []
            for i, qa_pair in enumerate(qa_pairs):
                question = qa_pair.get("question")
                answer = qa_pair.get("answer")
                
                # 검색 텍스트 구성
                search_texts = []
                
                if question:
                    q_content = question.get("content", "")
                    if scope in ["메시지 내용", "전체"]:
                        search_texts.append(q_content)
                
                if answer:
                    a_content = answer.get("content", "")
                    a_sql = answer.get("sql", "")
                    
                    if scope in ["메시지 내용", "전체"]:
                        search_texts.append(a_content)
                    if scope in ["SQL 쿼리", "전체"] and a_sql:
                        search_texts.append(a_sql)
                
                # 검색어가 포함된 QA 쌍 찾기
                search_text = " ".join(search_texts)
                if query.lower() in search_text.lower():
                    results.append((i, qa_pair))
            
            # 검색 결과 표시
            if results:
                st.sidebar.success(f"검색 결과: {len(results)}개 대화")
                
                for i, (pair_index, qa_pair) in enumerate(results[:5]):  # 최대 5개 대화만 표시
                    question = qa_pair.get("question")
                    answer = qa_pair.get("answer")
                    
                    # 검색 결과 헤더
                    if question:
                        q_preview = question.get("content", "")[:30] + "..."
                        result_header = f"🔍 대화 {pair_index + 1}: {q_preview}"
                    else:
                        result_header = f"🔍 대화 {pair_index + 1}: 시스템 메시지"
                    
                    with st.sidebar.expander(result_header, expanded=False):
                        # 질문 표시
                        if question:
                            st.markdown("**👤 질문:**")
                            st.markdown(f"*{question.get('content', '')}*")
                        
                        # 답변 표시
                        if answer:
                            st.markdown("**🤖 답변:**")
                            a_content = answer.get("content", "")
                            if len(a_content) > 150:
                                st.markdown(f"*{a_content[:150]}...*")
                            else:
                                st.markdown(f"*{a_content}*")
                            
                            # SQL이 있고 검색 범위에 포함된 경우 표시
                            if answer.get("sql") and scope in ["SQL 쿼리", "전체"]:
                                st.markdown("**🔍 SQL:**")
                                st.code(answer.get("sql"), language="sql")
            else:
                st.sidebar.info("검색 결과가 없습니다.")
        
        except Exception as e:
            logger.error(f"히스토리 검색 실패: {e}")
            st.sidebar.error("검색 중 오류가 발생했습니다.")
    
    def _show_history_export(self):
        """히스토리 내보내기 다이얼로그를 표시합니다."""
        if not st.session_state.messages:
            st.sidebar.warning("내보낼 히스토리가 없습니다.")
            return
        
        try:
            # 히스토리를 질문-답변 쌍 형태의 텍스트로 변환
            history_text = self._format_history_as_text(st.session_state.messages)
            
            # 다운로드 버튼 생성
            st.sidebar.download_button(
                label="💾 히스토리 다운로드 (TXT)",
                data=history_text,
                file_name=f"chat_history_{st.session_state.current_session_id[:8]}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt",
                mime="text/plain"
            )
            
            # JSON 형식으로도 제공
            import json
            history_json = json.dumps(st.session_state.messages, ensure_ascii=False, indent=2, default=str)
            
            st.sidebar.download_button(
                label="💾 히스토리 다운로드 (JSON)",
                data=history_json,
                file_name=f"chat_history_{st.session_state.current_session_id[:8]}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                mime="application/json"
            )
            
        except Exception as e:
            logger.error(f"히스토리 내보내기 실패: {e}")
            st.sidebar.error("히스토리 내보내기에 실패했습니다.")
    
    def _format_history_as_text(self, messages: List[Dict]) -> str:
        """메시지 히스토리를 질문-답변 쌍 형태의 텍스트로 변환합니다."""
        lines = []
        lines.append("=" * 60)
        lines.append("대화 히스토리 (질문-답변 쌍)")
        lines.append(f"세션 ID: {st.session_state.current_session_id}")
        lines.append(f"내보낸 시간: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        lines.append("=" * 60)
        lines.append("")
        
        # 메시지를 QA 쌍으로 그룹화
        qa_pairs = self._group_messages_into_qa_pairs(messages)
        
        for i, qa_pair in enumerate(qa_pairs, 1):
            question = qa_pair.get("question")
            answer = qa_pair.get("answer")
            
            lines.append(f"[대화 {i}]")
            lines.append("-" * 40)
            
            # 질문 표시
            if question:
                q_timestamp = question.get("timestamp")
                if q_timestamp:
                    try:
                        if isinstance(q_timestamp, str):
                            dt = datetime.fromisoformat(q_timestamp.replace('Z', '+00:00'))
                        else:
                            dt = q_timestamp
                        time_str = dt.strftime('%Y-%m-%d %H:%M:%S')
                        lines.append(f"👤 질문 ({time_str}):")
                    except:
                        lines.append("👤 질문:")
                else:
                    lines.append("👤 질문:")
                
                lines.append(question.get("content", ""))
                lines.append("")
            
            # 답변 표시
            if answer:
                a_timestamp = answer.get("timestamp")
                if a_timestamp:
                    try:
                        if isinstance(a_timestamp, str):
                            dt = datetime.fromisoformat(a_timestamp.replace('Z', '+00:00'))
                        else:
                            dt = a_timestamp
                        time_str = dt.strftime('%Y-%m-%d %H:%M:%S')
                        lines.append(f"🤖 답변 ({time_str}):")
                    except:
                        lines.append("🤖 답변:")
                else:
                    lines.append("🤖 답변:")
                
                lines.append(answer.get("content", ""))
                
                # SQL 쿼리가 있는 경우 추가
                if answer.get("sql"):
                    lines.append("")
                    lines.append("🔍 실행된 SQL 쿼리:")
                    lines.append(answer.get("sql"))
                
                # 분석 결과가 있는 경우 추가
                if answer.get("analysis_display"):
                    lines.append("")
                    lines.append("📊 분석 결과:")
                    lines.append(answer.get("analysis_display"))
                
                lines.append("")
            elif question:
                lines.append("⏳ 답변 대기 중...")
                lines.append("")
            
            lines.append("=" * 60)
            lines.append("")
        
        return "\n".join(lines)


# 전역 인스턴스
_session_manager_ui = SessionManagerUI()


def get_session_manager_ui() -> SessionManagerUI:
    """
    세션 매니저 UI 인스턴스를 반환합니다.
    
    Returns:
        SessionManagerUI: 세션 매니저 UI 인스턴스
    """
    return _session_manager_ui


def render_session_management():
    """
    세션 관리 컴포넌트를 렌더링하는 편의 함수
    """
    try:
        # 매번 새로운 인스턴스 생성 (상태 초기화 보장)
        session_ui = SessionManagerUI()
        session_ui.render_session_sidebar()
        return session_ui
    except Exception as e:
        logger.error(f"세션 관리 컴포넌트 생성 실패: {e}")
        # 세션 상태 수동 초기화 시도
        if "session_list" not in st.session_state:
            st.session_state.session_list = []
        if "current_session_id" not in st.session_state:
            st.session_state.current_session_id = None
        if "messages" not in st.session_state:
            st.session_state.messages = []
        
        # 기본 세션 관리 UI 표시
        st.sidebar.error("세션 관리 기능에 문제가 발생했습니다.")
        if st.sidebar.button("세션 관리 재시도"):
            st.rerun()
        
        return None


if __name__ == "__main__":
    # 테스트 코드
    print("=== Session Manager UI Test ===")
    
    # Streamlit 없이는 테스트 불가
    print("이 모듈은 Streamlit 환경에서만 테스트 가능합니다.")
    print("streamlit run ui/session_manager_ui.py 명령으로 테스트하세요.") 