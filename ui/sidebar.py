"""
Streamlit 애플리케이션의 사이드바 구성 요소 (하이브리드 버전)

이 모듈은 운송 현황은 TailwindCSS HTML로, 나머지는 Streamlit 네이티브 컴포넌트를 사용합니다.
"""

import streamlit as st
import streamlit.components.v1 as components
import logging
import os
import sys
from dotenv import load_dotenv
from modules.utils.schema_cache import schema_cache
from modules.utils.db_connector import create_db_connection, get_schema_info
from config import Config
from datetime import datetime, timedelta, timezone
import plotly.graph_objects as go
import plotly.express as px

# 세션 관리 UI 컴포넌트 import
session_manager_ui_available = False
try:
    from .session_manager_ui import get_session_manager_ui
    session_manager_ui_available = True
except ImportError:
    try:
        # 상대 import 실패 시 절대 import 시도
        from ui.session_manager_ui import get_session_manager_ui
        session_manager_ui_available = True
    except ImportError:
        logger = logging.getLogger(__name__)
        logger.warning("세션 관리 UI 모듈을 찾을 수 없습니다. 세션 관리 기능이 제한됩니다.")
        session_manager_ui_available = False

# 상위 디렉토리 경로 추가 (상대 임포트를 위해)
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# .env 파일 명시적으로 로드 (app.py에서 이미 로드했을 수 있음)
dotenv_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), '.env')
if not os.getenv("SQL_QUERY_MODEL") and os.path.exists(dotenv_path):
    load_dotenv(dotenv_path=dotenv_path)
    # SQL_QUERY_MODEL 환경 변수가 설정되어 있지 않은 경우에만 로그 출력
    if os.getenv("SQL_QUERY_MODEL"):
        print(f"sidebar.py: 환경 변수 파일에서 SQL_QUERY_MODEL 로드됨")

# 로깅 설정
logger = logging.getLogger(__name__)

def render_transport_status():
    """운송 현황 섹션을 새로운 디자인으로 렌더링"""
    # 현재 날짜
    current_date = datetime.now().strftime('%Y-%m-%d')
    
    # 새로운 디자인 HTML (패딩과 높이 조정)
    transport_html = f"""
    <div style="width: 100%; display: flex; flex-direction: column; justify-content: flex-start; align-items: center; gap: 16px; padding: 8px; background-color: #F7F7F7;">
        <div style="width: 100%; display: flex; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 16px;">
            <div style="width: 100%; display: flex; flex-direction: column; justify-content: flex-start; align-items: flex-start; gap: 20px;">
                <div style="width: 100%; display: flex; flex-direction: column; justify-content: flex-start; align-items: flex-end; gap: 24px;">
                    <div style="width: 100%; display: flex; justify-content: space-between; align-items: center;">
                        <div style="color: black; font-size: 18px; font-family: 'Pretendard', -apple-system, BlinkMacSystemFont, sans-serif; font-weight: 600; word-wrap: break-word;">오늘의 운송 현황</div>
                        <div style="color: #777777; font-size: 14px; font-family: 'Pretendard', -apple-system, BlinkMacSystemFont, sans-serif; font-weight: 500; word-wrap: break-word;">{current_date}</div>
                    </div>
                    <div style="width: 100%; display: flex; flex-direction: column; justify-content: flex-start; align-items: center; gap: 20px;">
                        <div style="width: 146px; height: 146px; position: relative; border-radius: 8px;">
                            <div style="width: 146px; height: 146px; position: absolute; border-radius: 50%; background: conic-gradient(#C1C669 0deg 3.375deg, #FF5900 3.375deg 332.625deg, #58B4B6 332.625deg 360deg);"></div>
                            <div style="width: 98px; height: 98px; position: absolute; background-color: #f3f4f6; border-radius: 50%; display: flex; align-items: center; justify-content: center; left: 24px; top: 24px;">
                                <div style="color: #111111; font-size: 28px; font-family: 'Pretendard', -apple-system, BlinkMacSystemFont, sans-serif; font-weight: 600; line-height: 30.80px;">320</div>
                            </div>
                        </div>
                        <div style="width: 100%; height: 13px; display: flex; justify-content: center; align-items: flex-start;">
                            <div style="width: 274px; display: flex; justify-content: space-between; align-items: center;">
                                <div style="width: 86px; display: flex; justify-content: space-between; align-items: center;">
                                    <div style="display: flex; justify-content: flex-start; align-items: center; gap: 4px;">
                                        <div style="width: 10px; height: 10px; background: #C1C669; border-radius: 50%;"></div>
                                        <div style="color: #646464; font-size: 12px; font-family: 'Pretendard', -apple-system, BlinkMacSystemFont, sans-serif; font-weight: 500; line-height: 13.20px; word-wrap: break-word;">운송 대기</div>
                                    </div>
                                    <div style="color: #646464; font-size: 12px; font-family: 'Pretendard', -apple-system, BlinkMacSystemFont, sans-serif; font-weight: 600; line-height: 13.20px; word-wrap: break-word;">3</div>
                                </div>
                                <div style="width: 86px; display: flex; justify-content: space-between; align-items: center;">
                                    <div style="display: flex; justify-content: flex-start; align-items: center; gap: 4px;">
                                        <div style="width: 10px; height: 10px; background: #FF5900; border-radius: 50%;"></div>
                                        <div style="color: #646464; font-size: 12px; font-family: 'Pretendard', -apple-system, BlinkMacSystemFont, sans-serif; font-weight: 500; line-height: 13.20px; word-wrap: break-word;">운송 중</div>
                                    </div>
                                    <div style="color: #646464; font-size: 12px; font-family: 'Pretendard', -apple-system, BlinkMacSystemFont, sans-serif; font-weight: 600; line-height: 13.20px; word-wrap: break-word;">297</div>
                                </div>
                                <div style="width: 86px; display: flex; justify-content: space-between; align-items: center;">
                                    <div style="display: flex; justify-content: flex-start; align-items: center; gap: 4px;">
                                        <div style="width: 10px; height: 10px; background: #58B4B6; border-radius: 50%;"></div>
                                        <div style="color: #646464; font-size: 12px; font-family: 'Pretendard', -apple-system, BlinkMacSystemFont, sans-serif; font-weight: 500; line-height: 13.20px; word-wrap: break-word;">운송 완료</div>
                                    </div>
                                    <div style="color: #646464; font-size: 12px; font-family: 'Pretendard', -apple-system, BlinkMacSystemFont, sans-serif; font-weight: 600; line-height: 13.20px; word-wrap: break-word;">20</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div style="width: 100%; height: 1px; background-color: #e5e5e5; margin-top: 20px;"></div>
            </div>
        </div>
    </div>
    """
    
    components.html(transport_html, height=310, scrolling=False)

def render_sidebar():
    """사이드바의 모든 컴포넌트를 렌더링하는 함수"""
    
    # 세션 관리 UI 초기화 (렌더링하지 않고 데이터만 준비)
    session_ui = None
    if session_manager_ui_available:
        try:
            # 세션 UI 객체만 생성하고 렌더링은 하지 않음
            session_ui = get_session_manager_ui()
            sessions_data = get_sessions_data(session_ui)
        except Exception as e:
            logger.error(f"세션 데이터 로드 실패: {e}")
            sessions_data = get_fallback_sessions_data()
    else:
        sessions_data = get_fallback_sessions_data()

    with st.sidebar:
        # 커스텀 CSS 적용
        apply_custom_css()
        
        # 운송 현황 섹션 (TailwindCSS HTML)
        render_transport_status()
        
        # 기사 관련 추천 질문 섹션 (Streamlit)
        render_driver_questions()
        
        # 물류 관련 추천 질문 섹션 (Streamlit)
        render_logistics_questions()
        
        st.divider()
        
        # History 섹션 (Streamlit)
        render_history_section(sessions_data)
        
        st.divider()
        
        # 고급 설정 토글 (Streamlit)
        render_settings_toggle(session_ui)
    
    return session_ui

def apply_custom_css():
    """사이드바에 커스텀 CSS 적용"""
    st.markdown("""
    <style>
    /* 사이드바 전체 스타일 */
    .css-1d391kg {
        background-color: #F7F7F7;
    }
    
    /* 사이드바 내 버튼을 텍스트처럼 보이게 하는 스타일 */
    .stSidebar button[kind="secondary"] {
        background: #F7F7F7 !important;
        border: 1px solid #F7F7F7 !important;
        color: #111111 !important;
        text-align: left !important;
        padding: 15px 28px !important;
        margin: 0px 0 !important;
        border-radius: 0px !important;
        font-size: 14px !important;
        font-weight: 400 !important;
        font-family: 'Pretendard', -apple-system, BlinkMacSystemFont, sans-serif !important;
        width: 100% !important;
        height: 44px !important;
        transition: all 0.2s ease !important;
        box-shadow: none !important;
        justify-content: flex-start !important;
        word-wrap: break-word !important;
    }
    
    .stSidebar button[kind="secondary"]:hover {
        background: #f9f9f9 !important;
        border-color: #f9f9f9 !important;
        transform: translateY(-1px) !important;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
    }
    
    .stSidebar button[kind="secondary"]:active {
        background: #f0f0f0 !important;
        border-color: #f0f0f0 !important;
        transform: translateY(0px) !important;
    }
    
    .stSidebar button[kind="secondary"]:focus {
        border-color: #F7F7F7 !important;
        box-shadow: 0 0 0 1px rgba(76, 175, 80, 0.3) !important;
    }
    
    /* 섹션 헤더 스타일 */
    .section-header {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin: 0.5rem 0 0.5rem 0;
        font-weight: 600;
        font-size: 16px;
        color: #111111;
        font-family: 'Pretendard', -apple-system, BlinkMacSystemFont, sans-serif;
    }
    
    /* 날짜 그룹 헤더 */
    .history-section-header {
        height: 24px;
        padding: 5px 28px;
        color: #888888;
        font-size: 12px;
        font-family: 'Pretendard', -apple-system, BlinkMacSystemFont, sans-serif;
        font-weight: 500;
        word-wrap: break-word;
    }
    
    /* 토글 버튼 스타일 */
    .stSidebar button[data-testid="baseButton-secondary"] {
        color: white !important;
    }
    
    .stSidebar div[data-testid="column"] button[kind="secondary"] {
        color: white !important;
    }
    </style>
    """, unsafe_allow_html=True)

def render_driver_questions():
    """기사 관련 추천 질문 섹션을 새로운 디자인으로 렌더링"""
    # 토글 상태 관리
    if 'driver_questions_expanded' not in st.session_state:
        st.session_state.driver_questions_expanded = True
    
    # 헤더와 토글 버튼
    col1, col2 = st.columns([7, 1])
    with col1:
        st.markdown('''
        <div class="section-header">
            <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="10" cy="7" r="4" stroke="#000000" stroke-width="1.5" fill="none"/>
                <path d="M2.5 17.5c0-5.5 3.358-7.5 7.5-7.5s7.5 2 7.5 7.5" stroke="#000000" stroke-width="1.5" fill="none"/>
            </svg>
            기사 관련 추천 질문
        </div>
        ''', unsafe_allow_html=True)
    with col2:
        if st.button("△" if st.session_state.driver_questions_expanded else "▽", 
                    key="driver_toggle", 
                    help="섹션 접기/펼치기"):
            st.session_state.driver_questions_expanded = not st.session_state.driver_questions_expanded
            st.rerun()
    
    # 질문 목록 (토글 상태에 따라 표시)
    if st.session_state.driver_questions_expanded:
        driver_questions = [
            "오늘 기사별 배송 건수 알려 줘",
            "지난달 기사별 평균 배송 시간 비교해 줘"
        ]
        
        for i, question in enumerate(driver_questions):
            if st.button(question, key=f"driver_q_{i}", 
                        use_container_width=True,
                        help="클릭하여 질문하기"):
                send_query_to_main(question)

def render_logistics_questions():
    """물류 관련 추천 질문 섹션 렌더링"""
    # 토글 상태 관리
    if 'logistics_questions_expanded' not in st.session_state:
        st.session_state.logistics_questions_expanded = True
    
    # 헤더와 토글 버튼
    col1, col2 = st.columns([7, 1])
    with col1:
        st.markdown('''
        <div class="section-header">
            <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="2.5" y="5" width="15" height="10" stroke="#000000" stroke-width="1.5" fill="none"/>
                <path d="M7.5 5V3a1 1 0 0 1 1-1h3a1 1 0 0 1 1 1v2" stroke="#000000" stroke-width="1.5" fill="none"/>
            </svg>
            물류 관련 추천 질문
        </div>
        ''', unsafe_allow_html=True)
    with col2:
        if st.button("△" if st.session_state.logistics_questions_expanded else "▽", 
                    key="logistics_toggle", 
                    help="섹션 접기/펼치기"):
            st.session_state.logistics_questions_expanded = not st.session_state.logistics_questions_expanded
            st.rerun()
    
    # 질문 목록 (토글 상태에 따라 표시)
    if st.session_state.logistics_questions_expanded:
        logistics_questions = [
            "이번 주 배송 지연 건수는 몇 건이야?",
            "현재 배송 중인 건 실시간 현황 보여줘"
        ]
        
        for i, question in enumerate(logistics_questions):
            if st.button(question, key=f"logistics_q_{i}", 
                        use_container_width=True,
                        help="클릭하여 질문하기"):
                send_query_to_main(question)

def render_history_section(sessions_data):
    """History 섹션 렌더링"""
    # 토글 상태 관리
    if 'history_expanded' not in st.session_state:
        st.session_state.history_expanded = True
    
    # 헤더와 토글 버튼
    col1, col2 = st.columns([7, 1])
    with col1:
        st.markdown('<div class="section-header">History</div>', unsafe_allow_html=True)
    with col2:
        if st.button("△" if st.session_state.history_expanded else "▽", 
                    key="history_toggle", 
                    help="섹션 접기/펼치기"):
            st.session_state.history_expanded = not st.session_state.history_expanded
            st.rerun()
    
    # 히스토리 목록 (토글 상태에 따라 표시)
    if st.session_state.history_expanded:
        # 오늘 세션들
        if sessions_data['today']:
            st.markdown('<div class="history-section-header">오늘</div>', unsafe_allow_html=True)
            for session in sessions_data['today'][:3]:
                if st.button(session['title'], 
                           key=f"today_{session['id']}", 
                           use_container_width=True,
                           help="이 세션으로 전환"):
                    select_session(session['id'])
        
        # 어제 세션들
        if sessions_data['yesterday']:
            st.markdown('<div class="history-section-header">어제</div>', unsafe_allow_html=True)
            for session in sessions_data['yesterday'][:3]:
                if st.button(session['title'], 
                           key=f"yesterday_{session['id']}", 
                           use_container_width=True,
                           help="이 세션으로 전환"):
                    select_session(session['id'])
        
        # 이전 세션들
        if sessions_data['older']:
            st.markdown('<div class="history-section-header">4월</div>', unsafe_allow_html=True)
            for session in sessions_data['older'][:3]:
                if st.button(session['title'], 
                           key=f"older_{session['id']}", 
                           use_container_width=True,
                           help="이 세션으로 전환"):
                    select_session(session['id'])

def render_settings_toggle(session_ui):
    """고급 설정 토글 버튼 및 설정 패널 렌더링"""
    # 고급 설정 토글 버튼
    if st.button("⚙️ 고급 설정", key="settings_toggle", help="데이터베이스 및 AI 모델 설정, 세션 관리"):
        st.session_state.show_advanced_settings = not st.session_state.get('show_advanced_settings', False)
    
    # 고급 설정 렌더링 (조건부)
    if st.session_state.get('show_advanced_settings', False):
        render_advanced_settings(session_ui)

def send_query_to_main(query):
    """메인 채팅으로 질문 전송"""
    # 세션 상태에 쿼리를 저장하여 메인 앱에서 처리하도록 함
    st.session_state.pending_query = query
    logger.info(f"사이드바에서 질문 전송: {query}")
    st.rerun()

def select_session(session_id):
    """세션 선택 처리"""
    try:
        # 현재 세션 ID 업데이트
        st.session_state.current_session_id = session_id
        logger.info(f"세션 선택됨: {session_id}")
        
        # 세션 히스토리 로드 트리거
        st.session_state.load_session_history = session_id
        st.rerun()
        
    except Exception as e:
        logger.error(f"세션 선택 오류: {e}")
        st.error(f"세션 선택 중 오류가 발생했습니다: {str(e)}")

def get_sessions_data(session_ui):
    """세션 데이터를 가져와서 Streamlit에서 사용할 수 있는 형태로 변환"""
    sessions_data = {
        'today': [],
        'yesterday': [],
        'older': []
    }
    
    if session_ui and hasattr(session_ui, 'session_manager'):
        try:
            # 최근 세션들 가져오기
            sessions = session_ui.session_manager.get_recent_sessions(limit=10)
            
            if sessions:
                kst = timezone(timedelta(hours=9))
                now = datetime.now(kst)
                today = now.date()
                yesterday = today - timedelta(days=1)
                
                for session in sessions:
                    # UTC 시간을 KST로 변환
                    session_date = session.created_at.replace(tzinfo=timezone.utc).astimezone(kst).date()
                    title = session.title[:30] + "..." if len(session.title) > 30 else session.title
                    
                    session_data = {
                        'id': session.session_id,
                        'title': title
                    }
                    
                    if session_date == today:
                        sessions_data['today'].append(session_data)
                    elif session_date == yesterday:
                        sessions_data['yesterday'].append(session_data)
                    else:
                        sessions_data['older'].append(session_data)
        
        except Exception as e:
            logger.error(f"세션 데이터 처리 실패: {e}")
    
    return sessions_data

def get_fallback_sessions_data():
    """세션 데이터를 로드할 수 없을 때 사용할 예시 데이터"""
    return {
        'today': [
            {'id': 'example1', 'title': '3월부터 5월까지의 실적 요약'}
        ],
        'yesterday': [
            {'id': 'example2', 'title': '오늘 운송 정보 요약'},
            {'id': 'example3', 'title': '최근 출고 처리 내역'},
            {'id': 'example4', 'title': '운송장 번호 조회'}
        ],
        'older': [
            {'id': 'example5', 'title': '지연된 배송 조회'},
            {'id': 'example6', 'title': '정비 필요한 차량 정리'},
            {'id': 'example7', 'title': '누적 주행 거리 기준 상위 10대 차량 조회'}
        ]
    }

def render_advanced_settings(session_ui):
    """고급 설정 섹션 렌더링"""
    st.subheader("🔧 고급 설정")
    
    # === 1. 세션 관리 섹션 (맨 위에 배치) ===
    st.markdown("### 🗂️ 세션 관리")
    
    if session_manager_ui_available and session_ui:
        try:
            # 현재 세션 정보 표시
            current_session = st.session_state.get('current_session_id')
            if current_session:
                st.info(f"🟢 현재 세션: `{current_session[:12]}...`")
            else:
                st.warning("❌ 활성 세션이 없습니다")
            
            # 세션 관리 버튼들
            col1, col2, col3 = st.columns(3)
            
            with col1:
                if st.button("🆕 새 세션", key="new_session_btn", help="새로운 채팅 세션을 시작합니다"):
                    try:
                        # 새 세션 생성 (세션 매니저 직접 호출)
                        new_session = session_ui.session_manager.create_new_session()
                        if new_session:
                            # 세션 상태 업데이트
                            st.session_state.current_session_id = new_session.session_id
                            st.session_state.messages = []  # 메시지 초기화
                            st.success(f"✅ 새 세션 생성: `{new_session.session_id[:12]}...`")
                            st.rerun()
                        else:
                            st.error("❌ 세션 생성에 실패했습니다")
                    except Exception as e:
                        st.error(f"❌ 세션 생성 오류: {str(e)}")
            
            with col2:
                if st.button("📋 세션 목록", key="session_list_btn", help="모든 세션 목록을 표시합니다"):
                    st.session_state.show_session_list = not st.session_state.get('show_session_list', False)
            
            with col3:
                if st.button("🧹 세션 정리", key="cleanup_btn", help="오래된 세션들을 정리합니다"):
                    try:
                        cleaned_count = session_ui.session_manager.cleanup_expired_sessions()
                        if cleaned_count > 0:
                            st.success(f"✅ {cleaned_count}개 세션이 정리되었습니다")
                        else:
                            st.info("ℹ️ 정리할 세션이 없습니다")
                    except Exception as e:
                        st.error(f"❌ 세션 정리 오류: {str(e)}")
            
            # 세션 목록 표시
            if st.session_state.get('show_session_list', False):
                st.markdown("#### 📝 세션 목록")
                try:
                    sessions = session_ui.session_manager.get_recent_sessions(limit=20)
                    if sessions:
                        for session in sessions:
                            # session이 SessionSummary 객체인 경우 처리
                            if hasattr(session, 'session_id'):
                                session_id = session.session_id
                                session_title = session.title
                                created_at = session.created_at
                                updated_at = session.updated_at
                                message_count = session.message_count
                            else:
                                # dict인 경우 처리
                                session_id = session.get('session_id', 'Unknown')
                                session_title = session.get('title', 'Untitled')
                                created_at = session.get('created_at')
                                updated_at = session.get('updated_at') 
                                message_count = session.get('message_count', 0)
                            
                            created_time = created_at.strftime('%Y-%m-%d %H:%M') if created_at else 'Unknown'
                            display_title = session_title[:50] + "..." if len(session_title) > 50 else session_title
                            
                            with st.expander(f"🗂️ {display_title} ({created_time})"):
                                col1, col2 = st.columns([3, 1])
                                with col1:
                                    st.text(f"📌 세션 ID: {session_id}")
                                    st.text(f"💬 메시지 수: {message_count}")
                                    st.text(f"📅 생성일: {created_at}")
                                    st.text(f"🔄 수정일: {updated_at}")
                                with col2:
                                    if st.button("🔄 복원", key=f"restore_{session_id}", help="이 세션을 복원합니다"):
                                        try:
                                            # 세션 복원: 현재 세션 ID 변경 및 히스토리 로드
                                            st.session_state.current_session_id = session_id
                                            # 세션 히스토리 로드 (SessionManagerUI의 메서드 사용)
                                            session_ui._load_session_history(session_id)
                                            st.success("✅ 세션이 복원되었습니다!")
                                            st.rerun()
                                        except Exception as e:
                                            st.error(f"❌ 세션 복원 오류: {str(e)}")
                                    
                                    if st.button("🗑️ 삭제", key=f"delete_{session_id}", help="이 세션을 삭제합니다"):
                                        try:
                                            deleted = session_ui.session_manager.delete_session(session_id)
                                            if deleted:
                                                st.success("✅ 세션이 삭제되었습니다!")
                                                st.rerun()
                                            else:
                                                st.error("❌ 세션 삭제에 실패했습니다")
                                        except Exception as e:
                                            st.error(f"❌ 세션 삭제 오류: {str(e)}")
                    else:
                        st.info("ℹ️ 저장된 세션이 없습니다.")
                except Exception as e:
                    st.error(f"❌ 세션 목록 로드 실패: {str(e)}")
        
        except Exception as e:
            st.error(f"❌ 세션 관리 UI 렌더링 실패: {str(e)}")
    
    elif not session_manager_ui_available:
        st.warning("⚠️ 세션 관리 모듈이 사용할 수 없습니다. 세션 관리 기능이 제한됩니다.")
    
    else:
        st.warning("⚠️ 세션 관리자가 초기화되지 않았습니다.")
    
    st.markdown("---")
    
    # === 2. 데이터베이스 및 AI 설정 ===
    # 기본값 가져오기
    default_values = Config.get_default_values()

    # 데이터베이스 연결 설정
    render_database_settings(default_values)

    # OpenAI 설정
    render_openai_settings(default_values)

    # LangSmith 설정
    render_langsmith_settings(default_values)

    # 스키마 캐시 설정
    render_schema_cache_settings(default_values)

    # 데이터베이스 스키마
    render_database_schema()

def render_database_settings(default_values):
    """데이터베이스 연결 설정을 렌더링"""
    st.subheader("데이터베이스 연결 정보")
    db_host = st.text_input("호스트", value=default_values["db_host"])
    db_port = st.text_input("포트", value=default_values["db_port"])
    db_user = st.text_input("사용자명", value=default_values["db_user"])
    db_password = st.text_input("비밀번호", value=default_values["db_password"], type="password")
    db_name = st.text_input("데이터베이스명", value=default_values["db_name"])

    if st.button("연결 정보 저장"):
        Config.save_session_config(
            db_host=db_host,
            db_port=int(db_port) if db_port.isdigit() else None,
            db_user=db_user,
            db_password=db_password,
            db_name=db_name
        )
        st.success("연결 정보가 저장되었습니다!")

def render_openai_settings(default_values):
    """OpenAI 설정을 렌더링"""
    st.subheader("OpenAI API 설정")
    openai_api_key = st.text_input("OpenAI API 키", value=default_values["openai_api_key"], type="password")

    st.subheader("OpenAI 모델 설정")
    # 현재 설정된 모델 값 가져오기
    models = Config.get_model_config()
    
    # 환경변수에서 모델 설정 가져오기 (dotenv를 통해 로드됨)
    env_sql_model = os.getenv("SQL_QUERY_MODEL", "환경변수 없음")
    env_data_model = os.getenv("DATA_ANALYSIS_MODEL", "환경변수 없음")
    
    logger.info(f"환경변수 SQL_QUERY_MODEL: {env_sql_model}")
    logger.info(f"환경변수 DATA_ANALYSIS_MODEL: {env_data_model}")
    logger.info(f"default_values SQL 모델: {default_values.get('sql_query_model', '없음')}")
    logger.info(f"default_values 데이터 분석 모델: {default_values.get('data_analysis_model', '없음')}")
    logger.info(f"get_model_config SQL 모델: {models['sql_query']}")
    logger.info(f"get_model_config 데이터 분석 모델: {models['data_analysis']}")
    
    # 모델 설정 정보를 확장 가능한 섹션에 표시
    with st.expander("현재 모델 설정 정보", expanded=False):
        st.markdown("### 환경변수 설정")
        st.markdown(f"- SQL_QUERY_MODEL: `{env_sql_model}`")
        st.markdown(f"- DATA_ANALYSIS_MODEL: `{env_data_model}`")
        
        st.markdown("### 현재 적용된 설정")
        st.markdown(f"- SQL 쿼리 모델: `{models['sql_query']}`")
        st.markdown(f"- 데이터 분석 모델: `{models['data_analysis']}`")
        
        if '\n' in env_sql_model or '\n' in env_data_model:
            st.warning("환경변수에 중복된 모델 설정이 발견되었습니다. 마지막 값이 적용됩니다.")
    
    # 모델 옵션 리스트
    model_options = ["gpt-4o", "gpt-4o-mini", "gpt-3.5-turbo", "gpt-4"]
    
    # SQL 쿼리 모델 셀렉트박스
    # 환경변수에서 값을 가져와 우선 적용
    env_sql_model = os.getenv("SQL_QUERY_MODEL")
    if env_sql_model and env_sql_model in model_options:
        default_sql_model = env_sql_model
        logger.info(f"환경변수에서 가져온 SQL 모델 적용: {default_sql_model}")
    else:
        # default_values에서 모델 값을 가져옴 (이미 환경변수, 시크릿, 세션 상태가 반영된 값)
        default_sql_model = default_values.get("sql_query_model", "gpt-4o")
        logger.info(f"기본값에서 가져온 SQL 모델 적용: {default_sql_model}")
    
    sql_model_index = model_options.index(default_sql_model) if default_sql_model in model_options else 0
    logger.info(f"SQL 모델 셀렉트박스 인덱스: {sql_model_index}, 모델: {model_options[sql_model_index]}")
    
    sql_query_model = st.selectbox(
        "SQL 쿼리 생성 모델",
        options=model_options,
        index=sql_model_index
    )
    logger.info(f"선택된 SQL 쿼리 모델: {sql_query_model}")
    
    # 데이터 분석 모델 셀렉트박스
    env_data_model = os.getenv("DATA_ANALYSIS_MODEL")
    if env_data_model and env_data_model in model_options:
        default_data_model = env_data_model
        logger.info(f"환경변수에서 가져온 데이터 분석 모델 적용: {default_data_model}")
    else:
        default_data_model = default_values.get("data_analysis_model", "gpt-4o")
        logger.info(f"기본값에서 가져온 데이터 분석 모델 적용: {default_data_model}")
    
    data_model_index = model_options.index(default_data_model) if default_data_model in model_options else 0
    logger.info(f"데이터 분석 모델 셀렉트박스 인덱스: {data_model_index}, 모델: {model_options[data_model_index]}")
    
    data_analysis_model = st.selectbox(
        "데이터 분석 모델",
        options=model_options,
        index=data_model_index
    )
    logger.info(f"선택된 데이터 분석 모델: {data_analysis_model}")
    
    # 현재 선택된 모델 값 저장
    if st.button("모델 설정 저장"):
        Config.save_session_config(
            sql_query_model=sql_query_model,
            data_analysis_model=data_analysis_model
        )
        st.success("모델 설정이 저장되었습니다!")
        # 로깅 추가
        logger.info(f"저장된 SQL 쿼리 모델: {sql_query_model}")
        logger.info(f"저장된 데이터 분석 모델: {data_analysis_model}")
    
    # API 키 업데이트는 입력 필드에서 변경될 때마다 적용
    Config.save_session_config(
        openai_api_key=openai_api_key,
        sql_query_model=sql_query_model,
        data_analysis_model=data_analysis_model
    )

def render_langsmith_settings(default_values):
    """LangSmith 설정을 렌더링"""
    st.subheader("LangSmith 설정")
    langsmith_api_key = st.text_input("LangSmith API 키", value=default_values.get("langsmith_api_key", ""), type="password")
    langsmith_tracing = st.checkbox("LangSmith 추적 활성화", value=default_values.get("langsmith_tracing", "false").lower() == "true")
    langsmith_project = st.text_input("LangSmith 프로젝트", value=default_values.get("langsmith_project", "text_to_sql_app"))

    if st.button("LangSmith 설정 저장"):
        Config.save_session_config(
            langsmith_api_key=langsmith_api_key,
            langsmith_tracing=langsmith_tracing,
            langsmith_project=langsmith_project
        )
        st.success("LangSmith 설정이 저장되었습니다!")

def render_schema_cache_settings(default_values):
    """스키마 캐시 설정을 렌더링"""
    st.subheader("스키마 캐시 설정")
    schema_cache_enabled = st.checkbox("스키마 캐시 활성화", value=default_values.get("schema_cache_enabled", "true").lower() == "true")
    schema_cache_ttl = st.number_input("캐시 유효 시간(초)", min_value=60, max_value=86400, value=int(default_values.get("schema_cache_ttl", "3600")), step=300)

    if st.button("캐시 설정 저장"):
        Config.save_session_config(
            schema_cache_enabled=schema_cache_enabled,
            schema_cache_ttl=schema_cache_ttl
        )
        # 캐시 설정 적용
        schema_cache.set_ttl(schema_cache_ttl)
        st.success("스키마 캐시 설정이 저장되었습니다!")

    if st.button("캐시 초기화"):
        schema_cache.clear()
        st.success("스키마 캐시가 초기화되었습니다!")

def render_database_schema():
    """데이터베이스 스키마 설정을 렌더링"""
    st.subheader("데이터베이스 스키마")

    if st.button("예시 스키마 사용"):
        example_schema = """
        CREATE TABLE users (
            id INT PRIMARY KEY,
            name VARCHAR(100),
            age INT,
            email VARCHAR(100),
            created_at DATETIME
        );

        CREATE TABLE orders (
            id INT PRIMARY KEY,
            user_id INT,
            product_name VARCHAR(100),
            amount DECIMAL(10, 2),
            order_date DATETIME,
            FOREIGN KEY (user_id) REFERENCES users(id)
        );
        """
        st.session_state["schema_info"] = example_schema
        st.success("예시 스키마가 로드되었습니다!")
        logger.info(f"예시 스키마 정보 로드됨 - 길이: {len(example_schema)} 문자")
        logger.info(f"예시 스키마 정보: {example_schema}")

    if st.button("연결 테스트"):
        db_config = Config.get_db_config()
        db, engine = create_db_connection(db_config)

        if db and engine:
            st.success("데이터베이스 연결에 성공했습니다!")
            # 캐시 설정 가져오기
            cache_config = Config.get_schema_cache_config()
            use_cache = cache_config["enabled"]

            # 스키마 정보 가져오기 (캐시 사용 여부 전달)
            schema_info = get_schema_info(db_config, use_cache=use_cache)
            if schema_info:
                st.session_state["schema_info"] = schema_info
                st.success("데이터베이스 스키마 정보를 가져왔습니다!")
                logger.info(f"스키마 정보 가져오기 성공 - 길이: {len(schema_info)} 문자, 캐시 사용={use_cache}")
            # 연결 닫기
            if hasattr(engine, 'dispose'):
                engine.dispose()
        else:
            st.error("데이터베이스 연결에 실패했습니다. 연결 정보를 확인해주세요.")

    if st.button("SQL 툴킷 테스트"):
        api_key = Config.get_openai_api_key()
        db_config = Config.get_db_config()

        # 캐시 설정 가져오기
        cache_config = Config.get_schema_cache_config()
        use_cache = cache_config["enabled"]

        if not api_key:
            st.error("OpenAI API 키가 설정되지 않았습니다.")
        elif not db_config:
            st.error("데이터베이스 연결 정보가 설정되지 않았습니다.")
        else:
            with st.spinner("SQL 툴킷 테스트 중..."):
                result = get_schema_info(db_config, "__test__", api_key, use_cache=use_cache)
                if result:
                    st.success(f"SQL 툴킷 테스트 완료! 로그를 확인하세요. (캐시 사용={use_cache})")
                else:
                    st.error("SQL 툴킷 테스트 실패. 로그를 확인하세요.")
