"""
결과 분석 노드

이 모듈은 쿼리 결과를 분석하고 시각화하는 노드 함수를 제공합니다.
"""

from typing import Dict, Any, Optional, Union, Callable
import pandas as pd
import traceback
import logging

from langchain_openai import ChatOpenAI
from langchain_core.output_parsers import StrOutputParser
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.messages import HumanMessage, SystemMessage

# 로깅 유틸리티 임포트
from modules.utils.logging_utils import log_openai_model_usage

# 로깅 설정
logger = logging.getLogger(__name__)

def analyze_results(state: Dict[str, Any], api_key: str, model_name: str, streaming_callback: Optional[Union[Callable, Dict[str, Any]]] = None) -> Dict[str, Any]:
    """
    쿼리 결과를 분석하고 시각화

    Args:
        state: 현재 그래프 상태
        api_key: OpenAI API 키
        model_name: 사용할 모델명
        streaming_callback: 스트리밍 콜백 함수 또는 콜백 함수 딕셔너리

    Returns:
        업데이트된 상태
    """
    # 유효한 모델명인지 확인 및 수정
    valid_models = ["gpt-4o", "gpt-4o-mini", "gpt-3.5-turbo", "gpt-4"]
    if model_name not in valid_models:
        logger.warning(f"잘못된 모델명이 지정되었습니다: {model_name}. 모델이 존재하는지 확인하세요.")
        # 기본값으로 변경하지 않고 원래 모델명을 그대로 사용
    
    logger.info(f"데이터 분석 시작: 모델={model_name}")
    
    # 데이터가 없는 경우 처리
    if state.get("data") is None:
        return {
            **state,
            "content": "데이터를 가져올 수 없습니다."
        }

    # 데이터프레임 가져오기
    df = state["data"]

    # 데이터프레임이 비어있는지 확인 (DataFrame에 대한 불리언 연산 피하기)
    if hasattr(df, 'empty') and df.empty:
        print("분석할 데이터가 없습니다: 데이터프레임이 비어있습니다.")
        return {
            **state,
            "content": "쿼리 결과가 없습니다."
        }

    try:
        # 그래프 생성 여부 판단
        graph = None
        try:
            # 데이터프레임이 비어있지 않고 최소 2개 이상의 열이 있는 경우 그래프 생성 시도
            print(f"DataFrame 열 수: {len(df.columns)}, 열 이름: {df.columns.tolist()}")
            print(f"DataFrame 데이터 타입:\n{df.dtypes}")

            if hasattr(df, 'empty') and not df.empty and len(df.columns) >= 2:
                # 숫자형 데이터가 있는지 확인
                numeric_cols = [col for col in df.columns if pd.api.types.is_numeric_dtype(df[col])]
                print(f"숫자형 열: {numeric_cols}")

                # 열 데이터 샘플 출력
                for col in df.columns:
                    print(f"열 '{col}' 샘플: {df[col].head(3).tolist()}, 타입: {df[col].dtype}")

                # NaN 값 확인 및 출력 (처리하지 않고 그대로 유지)
                for col in df.columns:
                    nan_count = df[col].isna().sum()
                    if nan_count > 0:
                        print(f"경고: '{col}' 컬럼에 {nan_count}개의 NaN 값이 있습니다. 원본 그대로 유지합니다.")

                # 문자열 열을 숫자형으로 변환 시도
                for col in df.columns:
                    if col not in numeric_cols:
                        # 변환 시도 전 원본 데이터 백업
                        original_data = df[col].copy()
                        try:
                            # 숫자형 변환 시도
                            df[col] = pd.to_numeric(df[col], errors='coerce')
                            print(f"열 '{col}'을 숫자형으로 변환 시도했습니다.")
                            # 변환 후 유효한 값이 있는지 확인
                            if df[col].notna().any():
                                numeric_cols.append(col)
                                print(f"열 '{col}'을 숫자형으로 변환 성공했습니다.")
                            else:
                                # 모든 값이 NaN인 경우 원본 데이터로 복원
                                df[col] = original_data
                                print(f"열 '{col}'의 모든 값이 NaN으로 변환되어 원본 텍스트로 복원했습니다.")
                        except Exception as e:
                            # 변환 실패 시 원본 데이터로 복원
                            df[col] = original_data
                            print(f"열 '{col}' 변환 실패: {str(e)}. 원본 텍스트로 복원했습니다.")

                # 숫자형 열 재확인
                numeric_cols = [col for col in df.columns if pd.api.types.is_numeric_dtype(df[col])]
                print(f"변환 후 숫자형 열: {numeric_cols}")

                if len(numeric_cols) > 0:
                    # 적절한 차트 타입 결정
                    # 행이 많은 경우 라인 차트, 적은 경우 막대 차트
                    chart_type = "bar" if len(df) <= 15 else "line"

                    # 데이터 준비 (숫자형 컬럼이 있는 경우)
                    if len(numeric_cols) >= 1:
                        # 첫 번째 열을 x축으로, 첫 번째 숫자형 열을 y축으로 사용
                        # 시각화 모듈의 함수 사용
                        from modules.visualization import generate_graph
                        print(f"그래프 생성 시도: 타입={chart_type}, 행 수={len(df)}, 열 수={len(df.columns)}")
                        graph = generate_graph(df, chart_type)
                        if graph is not None:
                            print("그래프 생성 성공")
                        else:
                            print("그래프 생성 실패: generate_graph 함수가 None을 반환함")
        except Exception as e:
            print(f"그래프 생성 중 오류 발생: {str(e)}")
            # 오류가 발생해도 계속 진행 (그래프 없이)

        # 분석 결과 생성
        if graph is not None:
            # ChatOpenAI 모델 초기화 (스트리밍 지원)
            analysis_callback = None
            if streaming_callback is not None:
                if isinstance(streaming_callback, dict) and "analysis" in streaming_callback:
                    analysis_callback = streaming_callback["analysis"]
                else:
                    analysis_callback = streaming_callback

            # OpenAI 모델 사용 로깅
            log_openai_model_usage(model_name, "데이터 분석 (그래프 포함)")

            llm = ChatOpenAI(
                model=model_name,
                temperature=0,
                api_key=api_key,
                streaming=analysis_callback is not None,
                callbacks=[analysis_callback] if analysis_callback else None
            )

            # 프롬프트 템플릿 정의
            prompt = ChatPromptTemplate.from_messages([
                SystemMessage(content="""당신은 데이터 분석 전문가입니다.
                사용자의 질문과 쿼리 결과를 바탕으로 데이터에 대한 심층적인 분석을 제공해야 합니다.

                응답은 다음 구조를 따르세요:
                1. 원래 질문에 대한 적합한 응답
                2. 데이터에서 발견한 주요 패턴 및 분석을 간략하게 3줄로 표시해주세요.
                """),
                HumanMessage(content=f"""원래 질문: {state.get("query", "")}

                SQL 쿼리: {state.get("sql", "")}

                데이터 정보: {f'행 수: {len(df)}, 열 수: {len(df.columns)}, 컬럼: {list(df.columns)}'}

                데이터 미리보기:
                {df.head(10).to_string()}
                
                """)
            ])

            # 체인 생성 및 실행
            chain = prompt | llm | StrOutputParser()
            analysis_result = chain.invoke({})

            # 상태 업데이트
            return {
                **state,
                "graph": graph,
                "analysis_display": analysis_result,
                "content": "분석이 완료되었습니다."
            }
        else:
            # 그래프는 없지만 분석 결과 생성
            # ChatOpenAI 모델 초기화 (스트리밍 지원)
            analysis_callback = None
            if streaming_callback is not None:
                if isinstance(streaming_callback, dict) and "analysis" in streaming_callback:
                    analysis_callback = streaming_callback["analysis"]
                else:
                    analysis_callback = streaming_callback

            # OpenAI 모델 사용 로깅
            log_openai_model_usage(model_name, "데이터 분석 (그래프 없음)")

            llm = ChatOpenAI(
                model=model_name,
                temperature=0,
                api_key=api_key,
                streaming=analysis_callback is not None,
                callbacks=[analysis_callback] if analysis_callback else None
            )

            # 프롬프트 템플릿 정의
            prompt = ChatPromptTemplate.from_messages([
                SystemMessage(content="""당신은 데이터 분석 전문가입니다.
                사용자의 질문과 쿼리 결과를 바탕으로 데이터에 대한 심층적인 분석을 제공해야 합니다.

                응답은 다음 구조를 따르세요:
                1. 원래 질문에 대한 간략한 정리
                2. 데이터에서 발견한 주요 트렌드와 패턴
                3. 데이터에 대한 추가적인 통찰과 분석
                4. 주요 인사이트와 결론
                5. 가능한 경우 데이터에 기반한 제안이나 예측

                사용자에게 더 유용한 통찰을 제공해야 합니다.
                """),
                HumanMessage(content=f"""원래 질문: {state.get("query", "")}

                SQL 쿼리: {state.get("sql", "")}

                데이터 정보: {f'행 수: {len(df)}, 열 수: {len(df.columns)}, 컬럼: {list(df.columns)}'}

                데이터 미리보기:
                {df.head(10).to_string()}

                위 정보를 바탕으로 데이터에 대한 추가 질문과 분석을 제공해주세요.""")
            ])

            # 체인 생성 및 실행
            chain = prompt | llm | StrOutputParser()
            analysis_result = chain.invoke({})

            # 상태 업데이트
            return {
                **state,
                "analysis_display": analysis_result,
                "content": f"쿼리 결과: {len(df)}개의 행이 반환되었습니다. 분석이 완료되었습니다."
            }
    except Exception as e:
        print(f"분석 중 오류 발생: {str(e)}")
        print(f"상세 오류 정보: {traceback.format_exc()}")
        return {
            **state,
            "error": f"분석 오류: {str(e)}",
            "content": f"데이터 분석 중 오류가 발생했습니다: {str(e)}"
        }
