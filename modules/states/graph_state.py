"""
GraphState 타입 정의

이 모듈은 LangGraph 워크플로우에서 사용되는 상태 타입을 정의합니다.
"""

import pandas as pd
from typing import Dict, List, Any, Optional
from typing_extensions import TypedDict

class GraphState(TypedDict, total=False):
    """그래프 상태를 정의하는 클래스"""
    query: str
    schema_info: str
    sql: Optional[str]
    data: Optional[pd.DataFrame]
    graph: Optional[Any]
    error: Optional[str]
    content: Optional[str]
    explanation: Optional[str]
    tables_used: Optional[List[str]]
    columns_used: Optional[Dict[str, List[str]]]
    query_type: Optional[str]
    analysis_display: Optional[str]
