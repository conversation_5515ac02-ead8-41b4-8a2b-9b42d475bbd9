"""
쿼리 실행 노드

이 모듈은 SQL 쿼리를 실행하고 결과를 반환하는 노드 함수를 제공합니다.
Lang<PERSON><PERSON>n의 SQLDatabase와 SQLDatabaseToolkit을 활용하여 쿼리를 실행합니다.
"""

from typing import Dict, Any, Union
import pandas as pd
import traceback
import logging

# 로컬 모듈 임포트
from modules.utils.db_connector import execute_query_with_langchain

# 로깅 설정
logger = logging.getLogger(__name__)

def execute_query(state: Dict[str, Any], db_config: Dict[str, Any]) -> Dict[str, Any]:
    """
    SQL 쿼리를 실행하고 결과를 반환

    Args:
        state: 현재 그래프 상태
        db_config: 데이터베이스 연결 설정

    Returns:
        업데이트된 상태
    """
    logger.info(f"쿼리 실행 시작: {state.get('sql', '')}")

    # SQL이 없는 경우 오류 상태 반환
    if not state.get("sql"):
        logger.error("SQL 쿼리가 생성되지 않았습니다.")
        return {
            **state,
            "error": "SQL 쿼리가 생성되지 않았습니다.",
            "content": "SQL 쿼리가 생성되지 않았습니다."
        }

    try:
        # 데이터베이스 연결 설정 확인
        for key, value in db_config.items():
            logger.info(f"DB 설정 - {key}: {'*****' if key == 'password' else value}")

        # LangChain을 사용하여 SQL 쿼리 실행
        logger.info("LangChain을 사용하여 SQL 쿼리 실행 중...")
        success, result = execute_query_with_langchain(db_config, state["sql"])

        if not success:
            # 실패 시 오류 메시지 반환
            error_msg = result if isinstance(result, str) else "알 수 없는 오류가 발생했습니다."
            logger.error(f"SQL 쿼리 실행 실패: {error_msg}")

            # 사용자 친화적 오류 메시지 생성
            user_msg = "SQL 쿼리 실행 중 오류가 발생했습니다: "

            if isinstance(result, str):
                if "Unknown column" in result:
                    user_msg += "존재하지 않는 열(컬럼)을 참조했습니다."
                elif "Table" in result and "doesn't exist" in result:
                    user_msg += "존재하지 않는 테이블을 참조했습니다."
                elif "syntax error" in result.lower():
                    user_msg += "SQL 구문 오류가 있습니다."
                else:
                    user_msg += result
            else:
                user_msg += "데이터베이스 오류가 발생했습니다."

            return {
                **state,
                "error": f"SQL 쿼리 실행 오류: {error_msg}",
                "content": user_msg
            }

        # 성공 시 결과 처리
        df = result if isinstance(result, pd.DataFrame) else pd.DataFrame()
        result_count = len(df) if not df.empty else 0
        logger.info(f"쿼리 실행 결과: {result_count}개의 행 반환됨")

        # 결과가 없는 경우 추가 로그
        if result_count == 0:
            logger.info("=== 쿼리 결과가 없습니다 ===")
            logger.info(f"SQL 쿼리: {state.get('sql', '')}")
            logger.info(f"결과 타입: {type(result).__name__}")
            if isinstance(result, pd.DataFrame):
                logger.info(f"DataFrame 정보: 열 개수={len(df.columns)}, 열 목록={df.columns.tolist() if not df.empty else '[]'}, 빈 DataFrame={df.empty}")
            else:
                logger.info(f"결과 값: {result}")

            # WHERE 절이 있는지 확인
            sql = state.get('sql', '')
            if "WHERE" in sql.upper():
                logger.info("쿼리에 WHERE 절이 있습니다. 조건에 맞는 데이터가 없을 수 있습니다.")

        # 상태 업데이트
        return {
            **state,
            "data": df
        }
    except Exception as e:
        logger.error(f"SQL 실행 중 예상치 못한 오류: {str(e)}")
        logger.error(f"상세 오류 정보: {traceback.format_exc()}")
        return {
            **state,
            "error": f"예상치 못한 오류: {str(e)}",
            "content": f"오류가 발생했습니다: {str(e)}"
        }
