"""
SQL 생성 노드

이 모듈은 자연어 질문을 SQL 쿼리로 변환하는 노드 함수를 제공합니다.
"""

from typing import Dict, List, Any, Optional, Union, Callable
import json
import re
import traceback
import logging

from langchain_openai import ChatOpenAI
from langchain_core.output_parsers import StrOutputParser
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.messages import HumanMessage, SystemMessage

# 로깅 유틸리티 임포트
from modules.utils.logging_utils import log_openai_model_usage

# 로깅 설정
logger = logging.getLogger(__name__)

def generate_sql(state: Dict[str, Any], api_key: str, model_name: str, streaming_callback: Optional[Union[Callable, Dict[str, Any]]] = None) -> Dict[str, Any]:
    """
    자연어 질문을 SQL 쿼리로 변환

    Args:
        state: 현재 그래프 상태
        api_key: OpenAI API 키
        model_name: 사용할 모델명
        streaming_callback: 스트리밍 콜백 함수 또는 콜백 함수 딕셔너리

    Returns:
        업데이트된 상태
    """
    # 상태에서 필요한 정보 추출
    query = state.get("query", "")
    schema_info = state.get("schema_info", "")

    # 유효한 모델명인지 확인 및 수정
    valid_models = ["gpt-4o", "gpt-4o-mini", "gpt-3.5-turbo", "gpt-4"]
    if model_name not in valid_models:
        logger.warning(f"잘못된 모델명이 지정되었습니다: {model_name}. 모델이 존재하는지 확인하세요.")
        # 기본값으로 변경하지 않고 원래 모델명을 그대로 사용

    logger.info(f"SQL 생성 시작: 쿼리='{query}', 모델={model_name}")
    print(f"SQL 생성 시작: 쿼리='{query}', 모델={model_name}")

    try:
        # ChatOpenAI 모델 초기화 (스트리밍 지원)
        sql_callback = None
        if streaming_callback is not None:
            if isinstance(streaming_callback, dict) and "sql" in streaming_callback:
                sql_callback = streaming_callback["sql"]
            else:
                sql_callback = streaming_callback

        # OpenAI 모델 사용 로깅
        log_openai_model_usage(model_name, "SQL 생성")

        llm = ChatOpenAI(
            model=model_name,
            temperature=0,
            api_key=api_key,
            streaming=sql_callback is not None,
            callbacks=[sql_callback] if sql_callback else None
        )

        # 프롬프트 템플릿 정의
        prompt = ChatPromptTemplate.from_messages([
            SystemMessage(content="""당신은 자연어 질문을 정확한 SQL 쿼리로 변환하는 전문가입니다.
            주어진 스키마를 기반으로 MySQL 호환 SQL 쿼리를 생성하세요.

            중요 규칙:
            - 항상 유효한 MySQL 문법을 사용하세요.
            - 스키마에 명시된 테이블과 컬럼만 사용하세요.
            - 복잡한 조인이 필요한 경우 적절한 JOIN 구문을 사용하세요.
            - 집계 함수(COUNT, SUM, AVG 등)가 필요한 경우 적절히 사용하세요.
            - 결과를 정렬해야 하는 경우 ORDER BY를 사용하세요.
            - 100개이상의 결과가 있을 경우에는 LIMIT을 사용하세요.
            - 테이블 이름과 컬럼 이름에는 백틱(`)을 사용하세요.
            - 서브쿼리가 필요한 경우 적절히 사용하세요.
            - 숫자 관련 집계는 COUNT(*)를 사용하세요.
            - 중요: 오직 SELECT 쿼리만 생성해야 합니다. INSERT, UPDATE, DELETE 등의 쿼리는 허용되지 않습니다.

            기사 관련 쿼리 특별 규칙:
            - 기사 정보는 rider_info 테이블에서 가져와야 합니다
            - rider 테이블은 사용하지 마세요 (존재하지 않음)
            - GROUP BY에서는 실제 JOIN된 테이블의 컬럼만 참조하세요
            - 기사별 집계 시 rider_info.owner_user_id를 사용하세요

            테이블 별칭 사용 권장:
            - 복잡한 쿼리에서는 테이블 별칭을 사용하세요
            - 별칭 사용 시 SELECT, GROUP BY, ORDER BY에서 일관되게 사용하세요
            
            시간 관련 함수:
            - 월별 집계: MONTH() 함수 사용 (DATE_FORMAT() 사용 금지)
            - 연도 추출: YEAR() 함수 사용
            - 월별 그룹화: GROUP BY MONTH() 사용

            올바른 예시:
            - 월별 집계: SELECT MONTH(date_column) AS month, COUNT(*) AS count ...
            - 연도 필터: WHERE YEAR(date_column) = 2024
            - 그룹화: GROUP BY MONTH(date_column)
            - 기사별 집계 (별칭 사용):
              SELECT ri.owner_user_id, AVG(TIMESTAMPDIFF(MINUTE, dd.create_at, dd.delivery_end_time)) AS avg_time
              FROM delivery d
              JOIN delivery_detail dd ON d.delivery_detail_id = dd.delivery_detail_id
              JOIN rider_info ri ON d.user_id = ri.owner_user_id
              GROUP BY ri.owner_user_id

            다음 형식으로 JSON 응답을 제공하세요:
            ```json
            {
                "sql": "생성된 SQL 쿼리",
                "explanation": "쿼리에 대한 설명",
                "tables_used": ["사용된 테이블 목록"],
                "columns_used": {"테이블명": ["컬럼1", "컬럼2"]},
                "query_type": "쿼리 유형(SELECT, INSERT 등)"
            }
            ```
            질문을 이해할 수 없거나 SQL로 변환할 수 없는 경우에도 항상 JSON 응답을 제공해야 합니다.
            """),
            HumanMessage(content=f"""
            # 데이터베이스 스키마:
            ```sql
            {schema_info}
            ```

            # 질문:
            {query}

            MySQL 호환 SQL 쿼리로 변환해주세요.
            """)
        ])

        print("LLM 호출 중...")
        # 체인 생성 및 실행
        chain = prompt | llm | StrOutputParser()
        result_str = chain.invoke({})
        print(f"LLM 응답 받음: {len(result_str)} 글자")

        # JSON 응답 파싱
        # JSON 부분 추출
        json_match = re.search(r'```json\s*(.*?)\s*```', result_str, re.DOTALL)
        if json_match:
            print("JSON 형식 응답을 찾았습니다.")
            result_str = json_match.group(1)
        else:
            print("JSON 블록을 찾을 수 없습니다. 전체 응답을 파싱 시도합니다.")

        # JSON 파싱
        try:
            print("JSON 파싱 시도...")
            result = json.loads(result_str)
            print("JSON 파싱 성공")
        except json.JSONDecodeError:
            print("JSON 파싱 실패, 정규식으로 필드 추출 시도")
            # 정규식으로 각 필드 추출 시도
            sql_match = re.search(r'"sql"\s*:\s*"(.*?)"', result_str, re.DOTALL)
            sql = sql_match.group(1) if sql_match else ""

            explanation_match = re.search(r'"explanation"\s*:\s*"(.*?)"', result_str, re.DOTALL)
            explanation = explanation_match.group(1) if explanation_match else ""

            # 테이블 추출 시도
            tables_used = []
            tables_match = re.search(r'"tables_used"\s*:\s*\[(.*?)\]', result_str, re.DOTALL)
            if tables_match:
                tables_str = tables_match.group(1)
                tables_used = [table.strip(' "\'') for table in tables_str.split(',') if table.strip()]

            # 기본 결과 구성
            result = {
                "sql": sql,
                "explanation": explanation,
                "tables_used": tables_used,
                "columns_used": {},
                "query_type": "SELECT"
            }

        # SQL이 유효한지 확인
        sql = result.get("sql", "")
        if not sql or "invalid" in sql.lower() or "이해할 수 없" in sql or "cannot understand" in sql.lower():
            print(f"유효하지 않은 SQL: {sql}")
            return {
                **state,
                "error": "질문을 이해할 수 없습니다.",
                "content": "해당 질문을 SQL로 변환할 수 없습니다. 질문을 다시 작성해주세요."
            }

        print(f"SQL 생성 완료: {result.get('sql', '')[:50]}...")
        # 상태 업데이트
        return {
            **state,
            "sql": result.get("sql", ""),
            "explanation": result.get("explanation", ""),
            "tables_used": result.get("tables_used", []),
            "columns_used": result.get("columns_used", {}),
            "query_type": result.get("query_type", "")
        }
    except Exception as e:
        print(f"SQL 생성 중 오류 발생: {str(e)}")
        print(f"상세 오류 정보: {traceback.format_exc()}")
        return {
            **state,
            "error": f"SQL 생성 오류: {str(e)}",
            "content": f"SQL 생성 중 오류가 발생했습니다: {str(e)}"
        }
