"""
데이터 시각화 모듈

이 모듈은 데이터프레임을 기반으로 시각화 기능을 제공합니다.
"""

from venv import logger
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import seaborn as sns
import plotly.express as px
import streamlit as st
import os
import platform
from typing import Optional, Any, Dict, Union, List

# 한글 폰트 설정
try:
    system_name = platform.system()
    
    if system_name == 'Darwin':  # macOS
        plt.rc('font', family='AppleGothic')
    elif system_name == 'Windows':
        plt.rc('font', family='Malgun Gothic')
    elif system_name == 'Linux':
        plt.rc('font', family='NanumGothic')
    
    # 그래프에 마이너스 폰트 깨짐 방지
    plt.rc('axes', unicode_minus=False)
    
    # 폰트 설정 로깅
    print(f"Matplotlib 한글 폰트 설정 완료: {system_name} 환경")
    
except Exception as e:
    print(f"한글 폰트 설정 중 오류 발생: {str(e)}")
    # 오류 발생 시 기본 폰트 사용

def st_generate_pie_chart(df: pd.DataFrame, 
                        names: Optional[str] = None, 
                        values: Optional[str] = None,
                        title: Optional[str] = None,
                        color_discrete_sequence: Optional[List[str]] = None,
                        height: Optional[int] = None, 
                        use_container_width: bool = True) -> Any:
    """
    Streamlit을 사용하여 파이 차트 생성
    
    Args:
        df: 데이터가 있는 DataFrame
        names: 파이 조각 이름으로 사용할 컬럼
        values: 파이 조각 크기로 사용할 컬럼
        title: 차트 제목
        color_discrete_sequence: 색상 목록
        height: 차트 높이 (픽셀)
        use_container_width: 컨테이너 너비를 사용할지 여부
        
    Returns:
        Streamlit 차트 객체
    """
    if hasattr(df, 'empty') and df.empty:
        st.warning("빈 DataFrame으로 그래프를 생성할 수 없습니다.")
        return None
    
    try:
        # 데이터 프레임 정보 출력 (개발 환경에서 참고용)
        if st.config.get_option("client.showErrorDetails"):
            logger.info(f"DataFrame 정보: 행={len(df)}, 열={len(df.columns)}")
            logger.info(f"컬럼: {df.columns.tolist()}")
        
        # 데이터가 많은 경우 상위 데이터만 표시
        if len(df) > 10:
            st.info(f"파이 차트에는 상위 10개만 표시합니다. (전체: {len(df)}개)")
            plot_df = df.head(10)
        else:
            plot_df = df.copy()
        
        # 컬럼 선택 로직
        if len(df.columns) < 2:
            st.warning("컬럼이 하나만 있어 파이 차트를 생성할 수 없습니다.")
            return None
        
        # 숫자형 컬럼 찾기
        numeric_cols = [col for col in plot_df.columns if pd.api.types.is_numeric_dtype(plot_df[col])]
        
        # names와 values가 지정되지 않은 경우 자동 선택
        if names is None:
            # 첫 번째 비숫자형 컬럼을 names로 사용
            for col in plot_df.columns:
                if col not in numeric_cols:
                    names = col
                    break
            
            # 비숫자형 컬럼이 없으면 첫 번째 컬럼 사용
            if names is None:
                names = plot_df.columns[0]
        
        if values is None and numeric_cols:
            # 첫 번째 숫자형 컬럼을 values로 사용
            values = numeric_cols[0]
            
            # names와 values가 같은 경우 다른 컬럼 선택
            if values == names and len(numeric_cols) > 1:
                values = numeric_cols[1]
        
        # values가 지정되지 않고 숫자형 컬럼도 없는 경우
        if values is None:
            # names 컬럼의 각 값 개수를 세어서 사용
            value_counts = plot_df[names].value_counts().reset_index()
            plot_df = value_counts
            values = "count"
            names = "index"
            
        # 타이틀 자동 설정
        if title is None:
            title = f"{names} 분포"
            
        # Plotly를 사용하여 파이 차트 생성
        fig = px.pie(
            plot_df,
            names=names,
            values=values,
            title=title,
            color_discrete_sequence=color_discrete_sequence,
            template="streamlit"
        )
        
        # 레이아웃 조정
        fig.update_traces(textposition='inside', textinfo='percent+label')
        fig.update_layout(
            margin=dict(l=20, r=20, t=40, b=20),
            height=height or 400
        )
        
        # Streamlit에 그래프 표시
        return st.plotly_chart(
            fig,
            use_container_width=use_container_width,
            theme="streamlit"
        )
        
    except Exception as e:
        st.error(f"파이 차트 생성 중 오류 발생: {str(e)}")
        return None

def generate_graph(df: pd.DataFrame, chart_type: str = "bar", 
                     use_container_width: bool = True) -> Optional[plt.Figure]:
    """
    DataFrame 데이터로 그래프 객체 생성

    Args:
        df: 데이터가 있는 DataFrame
        chart_type: 생성할 그래프 유형 ('bar', 'line', 'pie', 'scatter' 등)
        use_container_width: 컨테이너 너비를 사용할지 여부 (현재는 사용되지 않음)

    Returns:
        plt.Figure: 생성된 그래프 객체 (Matplotlib Figure) 또는 그래프 생성이 불가능할 경우 None
    """
    # DataFrame이 없거나 비어있는 경우
    if df is None or (hasattr(df, 'empty') and df.empty):
        print("빈 DataFrame으로 그래프를 생성할 수 없습니다.")
        return None
        
    # 단일 값(COUNT 쿼리 등)만 있는 경우 그래프 생성이 의미가 없음
    if len(df) == 1 and len(df.columns) == 1:
        print(f"단일 값({df.values[0][0]})만 있어 그래프를 생성하지 않습니다.")
        return None
    
    try:
        # 라인 차트일 경우
        if chart_type == "line":
            return generate_line_chart(df)
        
        # 막대 차트일 경우
        if chart_type == "bar":
            return generate_bar_chart(df, color_scheme='tab10', alpha=0.8, gradient=True)
            
        # 파이 차트일 경우
        if chart_type == "pie":
            return generate_pie_chart(df)
            
        # 지정된 차트 타입이 line, bar, pie 중 하나가 아닌 경우 기본적으로 bar 차트 생성
        return generate_bar_chart(df, color_scheme='viridis', alpha=0.8)
            
    except Exception as e:
        # 오류 메시지가 있는 빈 그래프 반환
        fig, ax = plt.subplots()
        ax.text(0.5, 0.5, f"그래프 생성 중 오류 발생: {str(e)}", 
                horizontalalignment='center', verticalalignment='center', color='red')
        ax.axis('off')
        return fig

def st_generate_line_chart(df: pd.DataFrame, x: Optional[str] = None, 
                        y: Optional[Union[str, List[str]]] = None,
                        x_label: Optional[str] = None, 
                        y_label: Optional[str] = None,
                        color: Optional[Union[str, List[str]]] = None,
                        height: Optional[int] = None,
                        use_container_width: bool = True) -> Any:
    """
    Streamlit의 st.line_chart를 활용한 라인 차트 생성
    
    Args:
        df: 데이터가 있는 DataFrame
        x: x축으로 사용할 열 이름. None이면 인덱스 사용
        y: y축으로 사용할 열 이름 또는 열 이름 리스트. None이면 모든 열 사용
        x_label: x축 레이블. None이면 열 이름 사용
        y_label: y축 레이블. None이면 열 이름 사용
        color: 색상 지정. 문자열 또는 색상 리스트
        height: 차트 높이 (픽셀)
        use_container_width: 컨테이너 너비를 사용할지 여부
        
    Returns:
        Streamlit 차트 객체 (add_rows 메서드로 데이터 추가 가능)
    """
    if hasattr(df, 'empty') and df.empty:
        st.warning("빈 DataFrame으로 그래프를 생성할 수 없습니다.")
        return None
    
    try:
        # 데이터 프레임 정보 출력 (개발 환경에서 참고용)
        if st.config.get_option("client.showErrorDetails"):
            logger.info(f"DataFrame 정보: 행={len(df)}, 열={len(df.columns)}")
            logger.info(f"컬럼: {df.columns.tolist()}")
        
        # 데이터가 많은 경우 상위 데이터만 표시
        if len(df) > 100:
            st.info(f"데이터가 많아 상위 100개만 표시합니다. (전체: {len(df)}개)")
            plot_df = df.head(100)
        else:
            plot_df = df.copy()
        
        # 숫자형 컬럼 확인
        numeric_cols = [col for col in plot_df.columns if pd.api.types.is_numeric_dtype(plot_df[col])]
        
        # x나 y가 지정되지 않은 경우 자동 선택
        if x is None and len(plot_df.columns) >= 1:
            # 인덱스가 시간 관련 타입이면 인덱스 사용, 아니면 첫번째 컬럼
            if isinstance(plot_df.index, pd.DatetimeIndex):
                # x는 None으로 유지 (인덱스 사용)
                pass
            else:
                x = plot_df.columns[0]
        
        if y is None and len(plot_df.columns) >= 1:
            # x가 이미 선택된 경우 나머지 숫자형 컬럼 사용
            if x is not None:
                y = [col for col in numeric_cols if col != x]
            else:
                # 모든 숫자형 컬럼 사용
                y = numeric_cols
        
        # 라인 차트 생성 및 반환
        return st.line_chart(
            plot_df,
            x=x,
            y=y,
            x_label=x_label,
            y_label=y_label,
            color=color,
            height=height,
            use_container_width=use_container_width
        )
        
    except Exception as e:
        st.error(f"라인 차트 생성 중 오류 발생: {str(e)}")
        return None

def st_generate_multiple_graphs(df: pd.DataFrame, use_container_width: bool = True) -> Dict[str, Any]:
    """
    데이터프레임을 기반으로 여러 Streamlit 그래프 생성

    Args:
        df: 데이터 프레임
        use_container_width: 컨테이너 너비를 사용할지 여부

    Returns:
        생성된 그래프 정보 (사용자가 선택한 그래프 타입)
    """
    # 데이터프레임이 비어있거나 충분한 열이 없으면 빈 딕셔너리 반환
    if hasattr(df, 'empty') and (df.empty or len(df.columns) < 2):
        st.warning("데이터가 부족하여 그래프를 생성할 수 없습니다.")
        return {}

    # 사용자에게 그래프 타입 선택 옵션 제공
    chart_type = st.selectbox(
        "그래프 유형 선택", 
        ["bar", "line", "pie", "scatter"],
        format_func=lambda x: {
            "bar": "막대 그래프", 
            "line": "선 그래프", 
            "pie": "파이 차트",
            "scatter": "산점도"
        }.get(x, x)
    )
    
    # 선택한 그래프 생성 및 표시
    if chart_type == "line":
        st_generate_line_chart(df, use_container_width=use_container_width)
    elif chart_type == "bar":
        # matplotlib 테마를 사용하여 더 다양한 스타일 적용
        st_generate_bar_chart(df, use_container_width=use_container_width, 
                           color="tab20", stack=False, theme='matplotlib')
    elif chart_type == "pie":
        st_generate_pie_chart(df, use_container_width=use_container_width)
    else:
        # 지원되지 않는 차트 타입은 막대 차트로 표시
        st_generate_bar_chart(df, use_container_width=use_container_width, 
                           color="viridis", stack=False, theme='matplotlib')
    
    return {"selected_chart_type": chart_type}

def generate_line_chart(df: pd.DataFrame, x: Optional[str] = None, 
                      y: Optional[Union[str, List[str]]] = None,
                      x_label: Optional[str] = None, 
                      y_label: Optional[str] = None,
                      title: Optional[str] = None) -> plt.Figure:
    """
    Matplotlib을 사용하여 DataFrame 데이터로 라인 차트 생성
    
    Args:
        df: 데이터가 있는 DataFrame
        x: x축으로 사용할 열 이름. None이면 인덱스 사용
        y: y축으로 사용할 열 이름 또는 열 이름 리스트. None이면 모든 열 사용
        x_label: x축 레이블. None이면 열 이름 사용
        y_label: y축 레이블. None이면 열 이름 사용
        title: 그래프 제목
        
    Returns:
        plt.Figure: 생성된 라인 차트 Figure 객체
    """
    # 데이터가 많은 경우 상위 데이터만 표시
    if len(df) > 100:
        plot_df = df.head(100).copy()
    else:
        plot_df = df.copy()
    
    # 숫자형 컬럼 확인
    numeric_cols = [col for col in plot_df.columns if pd.api.types.is_numeric_dtype(plot_df[col])]
    
    # x나 y가 지정되지 않은 경우 자동 선택
    if x is None and len(plot_df.columns) >= 1:
        # 인덱스가 시간 관련 타입이면 인덱스 사용, 아니면 첫번째 컬럼
        if isinstance(plot_df.index, pd.DatetimeIndex):
            x_data = plot_df.index
        else:
            x = plot_df.columns[0]
            x_data = plot_df[x]
    else:
        x_data = plot_df[x] if x is not None else plot_df.index
    
    if y is None and len(numeric_cols) >= 1:
        # x가 이미 선택된 경우 나머지 숫자형 컬럼 사용
        if x is not None:
            y = [col for col in numeric_cols if col != x]
        else:
            # 모든 숫자형 컬럼 사용
            y = numeric_cols
    
    # 리스트가 아닌 경우 리스트로 변환
    if y is not None and not isinstance(y, list):
        y = [y]
    
    # 그래프 생성
    fig, ax = plt.subplots(figsize=(10, 6))
    
    # 데이터 표시
    if y is not None:
        for column in y:
            if column in plot_df.columns and pd.api.types.is_numeric_dtype(plot_df[column]):
                ax.plot(x_data, plot_df[column], marker='o', linewidth=2, label=column)
    
    # 레이블 설정
    if x_label:
        ax.set_xlabel(x_label)
    elif x is not None:
        ax.set_xlabel(x)
    
    if y_label:
        ax.set_ylabel(y_label)
    elif y is not None and len(y) == 1:
        ax.set_ylabel(y[0])
    
    # 제목 설정
    if title:
        ax.set_title(title)
    
    # 범례 표시
    if y is not None and len(y) > 1:
        ax.legend()
    
    # 그리드 표시
    ax.grid(True, linestyle='--', alpha=0.7)
    
    # x축 레이블 회전 (데이터가 많은 경우)
    if len(x_data) > 10:
        plt.xticks(rotation=45)
    
    plt.tight_layout()
    
    return fig

def generate_bar_chart(df: pd.DataFrame, x: Optional[str] = None, 
                     y: Optional[Union[str, List[str]]] = None,
                     x_label: Optional[str] = None, 
                     y_label: Optional[str] = None,
                     title: Optional[str] = None,
                     horizontal: bool = False,
                     color_scheme: Optional[str] = None,
                     edge_color: Optional[str] = None,
                     alpha: float = 0.8,
                     gradient: bool = False,
                     pattern: bool = False) -> plt.Figure:
    """
    Matplotlib을 사용하여 DataFrame 데이터로 막대 차트 생성
    
    Args:
        df: 데이터가 있는 DataFrame
        x: x축으로 사용할 열 이름. None이면 인덱스 사용
        y: y축으로 사용할 열 이름 또는 열 이름 리스트. None이면 모든 열 사용
        x_label: x축 레이블. None이면 열 이름 사용
        y_label: y축 레이블. None이면 열 이름 사용
        title: 그래프 제목
        horizontal: 가로 막대 차트 여부
        color_scheme: 색상 스키마 ('viridis', 'plasma', 'inferno', 'magma', 'cividis', 'tab10', 'tab20', 'pastel', None)
        edge_color: 막대 테두리 색상 (None이면 테두리 없음)
        alpha: 막대 투명도 (0.0 ~ 1.0)
        gradient: 그라데이션 색상 사용 여부
        pattern: 패턴 채우기 사용 여부 (단일 열에만 적용)
        
    Returns:
        plt.Figure: 생성된 막대 차트 Figure 객체
    """
    import numpy as np
    from matplotlib.colors import LinearSegmentedColormap
    from matplotlib import cm
    import matplotlib.patches as mpatches
    
    # 데이터가 많은 경우 상위 데이터만 표시
    if len(df) > 50:
        plot_df = df.head(50).copy()
    else:
        plot_df = df.copy()
    
    # 숫자형 컬럼 확인
    numeric_cols = [col for col in plot_df.columns if pd.api.types.is_numeric_dtype(plot_df[col])]
    
    # x나 y가 지정되지 않은 경우 자동 선택
    if x is None and len(plot_df.columns) >= 1:
        # 첫번째 컬럼을 x로 사용
        x = plot_df.columns[0]
    
    if y is None and len(numeric_cols) >= 1:
        # x가 이미 선택된 경우 나머지 숫자형 컬럼 사용
        if x is not None:
            y = [col for col in numeric_cols if col != x]
        else:
            # 모든 숫자형 컬럼 사용
            y = numeric_cols
    
    # 리스트가 아닌 경우 리스트로 변환
    if y is not None and not isinstance(y, list):
        y = [y]
    
    # 그래프 생성
    fig, ax = plt.subplots(figsize=(10, 6))
    
    # 색상 스키마 설정
    if color_scheme is None:
        # 기본 색상 스키마
        color_scheme = 'tab10'
    
    # 색상 맵 가져오기
    cmap = plt.get_cmap(color_scheme)
    
    # 데이터 표시
    if x is not None and y is not None and len(y) > 0:
        x_data = plot_df[x] if x in plot_df.columns else plot_df.index
        
        # 다중 열일 경우 그룹화된 막대 차트 생성
        if len(y) > 1:
            bar_width = 0.8 / len(y)
            for i, column in enumerate(y):
                if column in plot_df.columns and pd.api.types.is_numeric_dtype(plot_df[column]):
                    positions = np.arange(len(x_data)) + (i - len(y)/2 + 0.5) * bar_width
                    color = cmap(i / len(y))
                    
                    if horizontal:
                        bars = ax.barh(positions, plot_df[column], height=bar_width, 
                                      label=column, alpha=alpha, color=color, 
                                      edgecolor=edge_color)
                    else:
                        bars = ax.bar(positions, plot_df[column], width=bar_width, 
                                     label=column, alpha=alpha, color=color, 
                                     edgecolor=edge_color)
            
            # x축 레이블 설정
            if not horizontal:
                ax.set_xticks(np.arange(len(x_data)))
                ax.set_xticklabels(x_data)
            else:
                ax.set_yticks(np.arange(len(x_data)))
                ax.set_yticklabels(x_data)
        else:
            # 단일 열일 경우 단순 막대 차트 생성
            column = y[0]
            if column in plot_df.columns and pd.api.types.is_numeric_dtype(plot_df[column]):
                # 그라데이션 색상 사용 여부
                if gradient:
                    # 값에 따른 그라데이션 색상 적용
                    norm = plt.Normalize(plot_df[column].min(), plot_df[column].max())
                    colors = cmap(norm(plot_df[column].values))
                    
                    if horizontal:
                        bars = ax.barh(x_data, plot_df[column], color=colors, alpha=alpha, 
                                       edgecolor=edge_color)
                    else:
                        bars = ax.bar(x_data, plot_df[column], color=colors, alpha=alpha, 
                                      edgecolor=edge_color)
                        
                    # 컬러바 추가
                    sm = plt.cm.ScalarMappable(cmap=cmap, norm=norm)
                    sm.set_array([])
                    cbar = plt.colorbar(sm, ax=ax)
                    cbar.set_label(column)
                    
                # 패턴 채우기 사용 여부
                elif pattern and len(x_data) <= 10:  # 데이터가 많을 때는 패턴이 복잡해 보일 수 있어 제한
                    patterns = ['/', '\\', '|', '-', '+', 'x', 'o', 'O', '.', '*']
                    
                    if horizontal:
                        bars = ax.barh(x_data, plot_df[column], alpha=alpha, 
                                       edgecolor=edge_color, color=cmap(0.5))
                    else:
                        bars = ax.bar(x_data, plot_df[column], alpha=alpha, 
                                      edgecolor=edge_color, color=cmap(0.5))
                    
                    # 패턴 적용
                    for i, bar in enumerate(bars):
                        bar.set_hatch(patterns[i % len(patterns)])
                        
                else:
                    # 일반 단색 막대 차트
                    if horizontal:
                        bars = ax.barh(x_data, plot_df[column], alpha=alpha, 
                                       color=cmap(0.5), edgecolor=edge_color)
                    else:
                        bars = ax.bar(x_data, plot_df[column], alpha=alpha, 
                                      color=cmap(0.5), edgecolor=edge_color)
                    
                    # 값 표시 (선택적)
                    for i, v in enumerate(plot_df[column]):
                        if not horizontal:
                            ax.text(i, v + 0.1, str(round(v, 2)), 
                                   ha='center', va='bottom', fontsize=8)
                        else:
                            ax.text(v + 0.1, i, str(round(v, 2)), 
                                   ha='left', va='center', fontsize=8)
    
    # 레이블 설정
    if horizontal:
        if y_label:
            ax.set_xlabel(y_label)
        elif y is not None and len(y) == 1:
            ax.set_xlabel(y[0])
        
        if x_label:
            ax.set_ylabel(x_label)
        elif x is not None:
            ax.set_ylabel(x)
    else:
        if x_label:
            ax.set_xlabel(x_label)
        elif x is not None:
            ax.set_xlabel(x)
        
        if y_label:
            ax.set_ylabel(y_label)
        elif y is not None and len(y) == 1:
            ax.set_ylabel(y[0])
    
    # 제목 설정
    if title:
        ax.set_title(title)
    
    # 범례 표시
    if y is not None and len(y) > 1:
        ax.legend(loc='best')
    
    # 그리드 표시
    ax.grid(True, linestyle='--', alpha=0.7, axis='y' if not horizontal else 'x')
    
    # x축 레이블 회전 (데이터가 많은 경우)
    if not horizontal and len(x_data) > 10:
        plt.xticks(rotation=45, ha='right')
    
    # 배경 스타일 설정
    ax.set_facecolor('#f8f9fa')
    fig.patch.set_facecolor('#ffffff')
    
    plt.tight_layout()
    
    return fig

def generate_pie_chart(df: pd.DataFrame, values: Optional[str] = None, 
                     labels: Optional[str] = None,
                     title: Optional[str] = None) -> plt.Figure:
    """
    Matplotlib을 사용하여 DataFrame 데이터로 파이 차트 생성
    
    Args:
        df: 데이터가 있는 DataFrame
        values: 값 열 이름. None이면 두 번째 숫자형 열 사용
        labels: 레이블 열 이름. None이면 첫 번째 열 사용
        title: 그래프 제목
        
    Returns:
        plt.Figure: 생성된 파이 차트 Figure 객체
    """
    # 데이터가 많은 경우 상위 데이터만 표시
    if len(df) > 10:
        plot_df = df.head(10).copy()
    else:
        plot_df = df.copy()
    
    # 숫자형 컬럼 확인
    numeric_cols = [col for col in plot_df.columns if pd.api.types.is_numeric_dtype(plot_df[col])]
    
    # values나 labels가 지정되지 않은 경우 자동 선택
    if labels is None and len(plot_df.columns) >= 1:
        labels = plot_df.columns[0]
    
    if values is None and len(numeric_cols) >= 1:
        # labels가 이미 선택된 경우 첫 번째 숫자형 컬럼 사용
        if labels is not None and labels in numeric_cols and len(numeric_cols) > 1:
            values = [col for col in numeric_cols if col != labels][0]
        else:
            # 첫 번째 숫자형 컬럼 사용
            values = numeric_cols[0] if numeric_cols else None
    
    # 그래프 생성
    fig, ax = plt.subplots(figsize=(8, 8))
    
    # 데이터 표시
    if values is not None and labels is not None:
        if values in plot_df.columns and labels in plot_df.columns:
            # 값이 음수인 경우 처리
            plot_data = plot_df[values].copy()
            plot_data[plot_data < 0] = 0
            
            # 레이블 가져오기
            label_data = plot_df[labels]
            
            # 파이 차트 생성
            wedges, texts, autotexts = ax.pie(
                plot_data, 
                labels=label_data,
                autopct='%1.1f%%',
                startangle=90,
                shadow=False
            )
            
            # 텍스트 스타일 설정
            plt.setp(autotexts, size=10, weight='bold')
    
    # 제목 설정
    if title:
        ax.set_title(title)
    
    # 동등한 비율로 표시
    ax.axis('equal')
    
    plt.tight_layout()
    
    return fig

def generate_multiple_graphs(df: pd.DataFrame) -> dict[str, plt.Figure]:
    """
    데이터프레임을 기반으로 여러 그래프 생성

    Args:
        df: 데이터 프레임

    Returns:
        그래프 타입을 키로, Figure 객체를 값으로 하는 딕셔너리
    """
    graphs = {}

    # 데이터프레임이 비어있거나 충분한 열이 없으면 빈 딕셔너리 반환
    if hasattr(df, 'empty') and (df.empty or len(df.columns) < 2):
        return graphs

    # 막대 그래프 생성
    try:
        graphs["bar"] = generate_bar_chart(df, color_scheme='tab20', alpha=0.85, edge_color='gray')
    except Exception:
        pass

    # 선 그래프 생성
    try:
        graphs["line"] = generate_line_chart(df)
    except Exception:
        pass

    # 파이 그래프 (2개 열이 있고 행이 10개 이하인 경우)
    if len(df.columns) >= 2 and len(df) <= 10:
        try:
            graphs["pie"] = generate_pie_chart(df)
        except Exception:
            pass

    return graphs


def st_generate_bar_chart(df: pd.DataFrame, x: Optional[str] = None, 
                       y: Optional[Union[str, List[str]]] = None,
                       x_label: Optional[str] = None, 
                       y_label: Optional[str] = None,
                       color: Optional[Union[str, List[str]]] = None,
                       height: Optional[int] = None,
                       use_container_width: bool = True,
                       horizontal: bool = False,
                       stack: Optional[Union[bool, str]] = None,
                       theme: Optional[str] = None) -> Any:
    """
    Streamlit의 st.bar_chart를 활용한 막대 차트 생성
    
    Args:
        df: 데이터가 있는 DataFrame
        x: x축으로 사용할 열 이름. None이면 인덱스 사용
        y: y축으로 사용할 열 이름 또는 열 이름 리스트. None이면 모든 열 사용
        x_label: x축 레이블. None이면 열 이름 사용
        y_label: y축 레이블. None이면 열 이름 사용
        color: 색상 지정. 문자열(컬럼명) 또는 색상 리스트
              색상 스키마로 'viridis', 'plasma', 'inferno', 'magma', 'cividis', 
              'category10', 'category20', 'pastel' 등을 지정할 수 있음
        height: 차트 높이 (픽셀)
        use_container_width: 컨테이너 너비를 사용할지 여부
        horizontal: 가로 막대 차트 여부
        stack: 막대 스택 방식 (True, False, "normalize", "center", "layered" 또는 None)
        theme: 차트 테마 ('streamlit', 'plotly', 'matplotlib' 등)
              'matplotlib'을 선택하면 matplotlib 그래프를 생성하여 더 다양한 스타일 적용 가능
        
    Returns:
        Streamlit 차트 객체 (add_rows 메서드로 데이터 추가 가능)
    """
    if hasattr(df, 'empty') and df.empty:
        st.warning("빈 DataFrame으로 그래프를 생성할 수 없습니다.")
        return None
    
    try:
        # 데이터 프레임 정보 출력 (개발 환경에서 참고용)
        if st.config.get_option("client.showErrorDetails"):
            logger.info(f"DataFrame 정보: 행={len(df)}, 열={len(df.columns)}")
            logger.info(f"컬럼: {df.columns.tolist()}")
        
        # 데이터가 많은 경우 상위 데이터만 표시
        if len(df) > 50:
            st.info(f"데이터가 많아 상위 50개만 표시합니다. (전체: {len(df)}개)")
            plot_df = df.head(50)
        else:
            plot_df = df.copy()
        
        # 숫자형 컬럼 확인
        numeric_cols = [col for col in plot_df.columns if pd.api.types.is_numeric_dtype(plot_df[col])]
        
        # x나 y가 지정되지 않은 경우 자동 선택
        if x is None and len(plot_df.columns) >= 1:
            # 인덱스가 시간 관련 타입이면 인덱스 사용, 아니면 첫번째 컬럼
            if isinstance(plot_df.index, pd.DatetimeIndex):
                # x는 None으로 유지 (인덱스 사용)
                pass
            else:
                x = plot_df.columns[0]
        
        if y is None and len(plot_df.columns) >= 1:
            # x가 이미 선택된 경우 나머지 숫자형 컬럼 사용
            if x is not None:
                y = [col for col in numeric_cols if col != x]
            else:
                # 모든 숫자형 컬럼 사용
                y = numeric_cols
        
        # 테마가 matplotlib인 경우 matplotlib 그래프 생성
        if theme == 'matplotlib':
            # matplotlib 그래프 생성
            fig = generate_bar_chart(
                plot_df, 
                x=x, 
                y=y, 
                x_label=x_label, 
                y_label=y_label, 
                horizontal=horizontal,
                color_scheme=color if isinstance(color, str) else 'tab10',
                gradient=True if color == 'viridis' else False,
                alpha=0.8,
                edge_color='gray' if color != 'viridis' else None
            )
            
            # Streamlit에 표시
            return st.pyplot(fig)
        else:
            # 기본 Streamlit 차트 사용
            return st.bar_chart(
                plot_df,
                x=x,
                y=y,
                x_label=x_label,
                y_label=y_label,
                color=color,
                height=height,
                use_container_width=use_container_width,
                horizontal=horizontal,
                stack=stack
            )
        
    except Exception as e:
        st.error(f"막대 차트 생성 중 오류 발생: {str(e)}")
        return None