"""
모듈 패키지 초기화 파일

이 파일은 모듈 패키지가 임포트될 때 실행되는 초기화 코드를 포함합니다.
"""

import logging

# 로깅 설정
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 스키마 캐시 초기화
try:
    from modules.utils.schema_cache import schema_cache
    from config import Config

    # 환경 변수나 설정에서 캐시 설정 가져오기
    cache_config = Config.get_schema_cache_config()

    # TTL 설정
    schema_cache.set_ttl(cache_config["ttl"])

    logger.info(f"스키마 캐시 초기화 완료: 활성화={cache_config['enabled']}, TTL={cache_config['ttl']}초")
except Exception as e:
    logger.warning(f"스키마 캐시 초기화 실패: {e}")
