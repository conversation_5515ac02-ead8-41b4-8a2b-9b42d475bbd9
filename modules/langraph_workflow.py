"""
LangGraph 기반 텍스트-SQL 워크플로우

이 모듈은 텍스트-SQL 변환, 쿼리 실행 및 결과 분석을 LangGraph를 사용하여 구현합니다.
"""

from typing import Dict, Any, Optional, Callable, Union
import logging

from langgraph.graph import StateGraph, END

# 모듈 임포트
from modules.utils.db_connector import get_schema_info
from modules.states.graph_state import GraphState
from modules.states.generate_sql import generate_sql
from modules.states.execute_query import execute_query
from modules.states.analyze_results import analyze_results

# 로깅 설정
logger = logging.getLogger(__name__)

# 상태 결정 함수
def should_continue(state: Dict[str, Any]) -> str:
    """
    다음 단계를 결정

    Args:
        state: 현재 그래프 상태

    Returns:
        다음 노드 이름
    """
    # 오류가 있으면 종료
    if state.get("error"):
        return "end"

    # SQL이 없으면 종료
    if not state.get("sql"):
        return "end"

    # 데이터가 없으면 종료
    data = state.get("data")
    if data is None:
        return "end"

    # DataFrame이 비어있는지 확인 (DataFrame에 대한 불리언 연산 피하기)
    if hasattr(data, 'empty') and data.empty:
        print("데이터프레임이 비어있습니다.")
        return "end"

    # 데이터가 있으면 분석 단계로
    return "analyze"

# 그래프 생성 함수
def create_text_to_sql_graph(api_key: str, db_config: Dict[str, Any],
                            sql_model: str = "gpt-4o",
                            analysis_model: str = "gpt-4o",
                            streaming_callback: Optional[Union[Callable, Dict[str, Any]]] = None) -> StateGraph:
    """
    텍스트-SQL 워크플로우 그래프 생성

    Args:
        api_key: OpenAI API 키
        db_config: 데이터베이스 연결 설정
        sql_model: SQL 생성에 사용할 모델
        analysis_model: 데이터 분석에 사용할 모델

    Returns:
        StateGraph 객체
    """
    # 워크플로우 그래프 생성 (GraphState를 상태 스키마로 사용)
    workflow = StateGraph(GraphState)

    # 노드 추가 (스트리밍 콜백 전달)
    workflow.add_node("generate_sql", lambda state: generate_sql(state, api_key, sql_model, streaming_callback))
    workflow.add_node("execute_query", lambda state: execute_query(state, db_config))
    workflow.add_node("analyze", lambda state: analyze_results(state, api_key, analysis_model, streaming_callback))

    # 엣지 추가
    workflow.add_edge("generate_sql", "execute_query")
    workflow.add_conditional_edges(
        "execute_query",
        should_continue,
        {
            "analyze": "analyze",
            "end": END
        }
    )
    workflow.add_edge("analyze", END)

    # 시작 노드 설정
    workflow.set_entry_point("generate_sql")

    return workflow

# 메인 실행 함수
def process_query_with_langgraph(query: str, schema_info: Optional[str], api_key: str, db_config: Dict[str, Any],
                               sql_model: str = "gpt-4o", analysis_model: str = "gpt-4o",
                               streaming_callback: Optional[Union[Callable, Dict[str, Any]]] = None) -> Dict[str, Any]:
    """
    LangGraph를 사용하여 자연어 질문을 처리

    Args:
        query: 사용자 질문
        schema_info: 데이터베이스 스키마 정보
        api_key: OpenAI API 키
        db_config: 데이터베이스 연결 설정
        sql_model: SQL 생성에 사용할 모델
        analysis_model: 데이터 분석에 사용할 모델

    Returns:
        처리 결과
    """
    # 유효한 모델 목록
    valid_models = ["gpt-4o", "gpt-4o-mini", "gpt-3.5-turbo", "gpt-4"]
    
    # 모델명 유효성 검사 및 교정
    if sql_model not in valid_models:
        logger.warning(f"유효하지 않은 SQL 모델명: '{sql_model}'이 지정되었습니다. 모델이 존재하는지 확인하세요.")
        # 기본값으로 변경하지 않고 원래 모델명을 그대로 사용
        
    if analysis_model not in valid_models:
        logger.warning(f"유효하지 않은 분석 모델명: '{analysis_model}'이 지정되었습니다. 모델이 존재하는지 확인하세요.")
        # 기본값으로 변경하지 않고 원래 모델명을 그대로 사용
    
    logger.info(f"LangGraph 워크플로우 시작: 쿼리='{query}', SQL 모델={sql_model}, 분석 모델={analysis_model}")
    print(f"LangGraph 워크플로우 시작: 쿼리='{query}'")

    # API 키 확인
    if not api_key:
        print("API 키 오류: OpenAI API 키가 제공되지 않았습니다.")
        return {
            "error": "OpenAI API 키가 설정되지 않았습니다.",
            "content": "OpenAI API 키가 설정되지 않았습니다. 사이드바에서 API 키를 입력해주세요."
        }

    # 스키마 정보 확인 및 가져오기
    if not schema_info and db_config:
        try:
            # 캐시 설정 가져오기
            from config import Config
            cache_config = Config.get_schema_cache_config()
            use_cache = cache_config["enabled"]

            print(f"질문 '{query}'에 관련된 스키마 정보 가져오기 시도... (캐시 사용={use_cache})")
            schema_info = get_schema_info(db_config, query, api_key, use_cache=use_cache)
            if schema_info:
                print(f"스키마 정보 가져오기 성공 - 길이: {len(schema_info)} 문자")
        except Exception as e:
            print(f"스키마 정보 가져오기 실패: {str(e)}")

    if not schema_info:
        print("스키마 오류: 데이터베이스 스키마 정보가 제공되지 않았습니다.")
        return {
            "error": "데이터베이스 스키마 정보가 없습니다.",
            "content": "데이터베이스 스키마 정보가 제공되지 않았습니다. 스키마 파일을 업로드하거나 예시 스키마를 사용해주세요."
        }

    # 데이터베이스 설정 확인
    if not db_config or not all(key in db_config for key in ["host", "port", "user", "password", "database"]):
        print("DB 설정 오류: 데이터베이스 연결 정보가 불완전합니다.")
        return {
            "error": "데이터베이스 연결 정보가 불완전합니다.",
            "content": "데이터베이스 연결 정보가 불완전합니다. 모든 필드(호스트, 포트, 사용자명, 비밀번호, 데이터베이스명)를 입력해주세요."
        }

    # 초기 상태 생성
    initial_state = {
        "query": query,
        "schema_info": schema_info,
        "sql": None,
        "data": None,
        "graph": None,
        "error": None,
        "content": None
    }

    try:
        print(f"워크플로우 그래프 생성 중...")
        # 그래프 생성 (스트리밍 콜백 전달)
        graph = create_text_to_sql_graph(api_key, db_config, sql_model, analysis_model, streaming_callback)

        print(f"그래프 컴파일 중...")
        # 그래프 컴파일 및 실행
        app = graph.compile()

        print(f"워크플로우 실행 중...")
        result = app.invoke(initial_state)

        print(f"워크플로우 완료: 오류={result.get('error', None) is not None}")
        # 중간 단계 결과를 포함한 최종 결과 반환
        return result
    except Exception as e:
        import traceback
        print(f"워크플로우 실행 오류: {str(e)}")
        print(f"상세 오류 정보: {traceback.format_exc()}")
        # 오류 처리
        return {
            **initial_state,
            "error": f"워크플로우 실행 오류: {str(e)}",
            "content": f"처리 중 오류가 발생했습니다: {str(e)}"
        }