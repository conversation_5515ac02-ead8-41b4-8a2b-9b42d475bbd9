"""
스키마 캐싱 모듈

이 모듈은 데이터베이스 스키마 정보를 메모리에 캐싱하여 반복적인 데이터베이스 호출을 줄이는 기능을 제공합니다.
"""

import time
from typing import Dict, Any, Optional, Tuple
import logging

# 로거 설정
logger = logging.getLogger(__name__)

class SchemaCache:
    """
    데이터베이스 스키마 정보를 메모리에 캐싱하는 클래스
    """
    _instance = None
    
    def __new__(cls):
        """싱글톤 패턴 구현"""
        if cls._instance is None:
            cls._instance = super(SchemaCache, cls).__new__(cls)
            cls._instance._cache = {}  # {cache_key: (schema_info, timestamp)}
            cls._instance._ttl = 3600  # 기본 TTL: 1시간 (초 단위)
        return cls._instance
    
    def set_ttl(self, ttl: int) -> None:
        """
        캐시 TTL(Time To Live) 설정
        
        Args:
            ttl: 캐시 유효 시간 (초 단위)
        """
        self._ttl = ttl
        logger.info(f"스키마 캐시 TTL 설정: {ttl}초")
    
    def _generate_cache_key(self, db_config: Dict[str, Any], query: Optional[str] = None) -> str:
        """
        캐시 키 생성
        
        Args:
            db_config: 데이터베이스 연결 설정
            query: 자연어 질문 (선택적)
            
        Returns:
            캐시 키 문자열
        """
        # 데이터베이스 연결 정보로 기본 키 생성
        base_key = f"{db_config.get('host')}:{db_config.get('port')}:{db_config.get('database')}"
        
        # 질문이 있는 경우 질문 관련 키 생성
        if query:
            # 질문이 너무 길면 해시 사용
            if len(query) > 50:
                import hashlib
                query_hash = hashlib.md5(query.encode()).hexdigest()
                return f"{base_key}:query:{query_hash}"
            return f"{base_key}:query:{query}"
        
        # 전체 스키마 정보에 대한 키
        return f"{base_key}:all"
    
    def get(self, db_config: Dict[str, Any], query: Optional[str] = None) -> Optional[str]:
        """
        캐시에서 스키마 정보 가져오기
        
        Args:
            db_config: 데이터베이스 연결 설정
            query: 자연어 질문 (선택적)
            
        Returns:
            캐시된 스키마 정보 또는 None (캐시 미스)
        """
        cache_key = self._generate_cache_key(db_config, query)
        
        # 캐시에 있는지 확인
        if cache_key in self._cache:
            schema_info, timestamp = self._cache[cache_key]
            
            # TTL 확인
            if time.time() - timestamp <= self._ttl:
                logger.info(f"스키마 캐시 히트: {cache_key[:50]}...")
                return schema_info
            else:
                # TTL 만료
                logger.info(f"스키마 캐시 만료: {cache_key[:50]}...")
                del self._cache[cache_key]
        
        logger.info(f"스키마 캐시 미스: {cache_key[:50]}...")
        return None
    
    def set(self, db_config: Dict[str, Any], schema_info: str, query: Optional[str] = None) -> None:
        """
        스키마 정보를 캐시에 저장
        
        Args:
            db_config: 데이터베이스 연결 설정
            schema_info: 저장할 스키마 정보
            query: 자연어 질문 (선택적)
        """
        cache_key = self._generate_cache_key(db_config, query)
        self._cache[cache_key] = (schema_info, time.time())
        logger.info(f"스키마 캐시 저장: {cache_key[:50]}... (길이: {len(schema_info)} 문자)")
    
    def clear(self, db_config: Optional[Dict[str, Any]] = None) -> None:
        """
        캐시 초기화
        
        Args:
            db_config: 특정 데이터베이스 연결에 대한 캐시만 초기화 (선택적)
        """
        if db_config:
            # 특정 데이터베이스 연결에 대한 캐시만 초기화
            base_key = f"{db_config.get('host')}:{db_config.get('port')}:{db_config.get('database')}"
            keys_to_delete = [k for k in self._cache.keys() if k.startswith(base_key)]
            for key in keys_to_delete:
                del self._cache[key]
            logger.info(f"특정 데이터베이스 스키마 캐시 초기화: {base_key} (삭제된 항목: {len(keys_to_delete)}개)")
        else:
            # 전체 캐시 초기화
            self._cache.clear()
            logger.info("전체 스키마 캐시 초기화")
    
    def get_stats(self) -> Dict[str, Any]:
        """
        캐시 통계 정보 반환
        
        Returns:
            캐시 통계 정보
        """
        return {
            "cache_size": len(self._cache),
            "ttl": self._ttl,
            "keys": list(self._cache.keys()),
            "memory_usage": sum(len(v[0]) for v in self._cache.values())
        }

# 싱글톤 인스턴스 생성
schema_cache = SchemaCache()
