"""
서버 고유 아이디 생성 및 관리 시스템

이 모듈은 서버의 IP 주소나 도메인을 기반으로 고유 아이디를 생성하고 관리합니다.
DynamoDB에서 파티션 키로 사용될 서버 식별자를 제공합니다.
"""

import hashlib
import socket
import os
import uuid
from typing import Optional, Dict, Any
from functools import lru_cache
import logging
from datetime import datetime

logger = logging.getLogger(__name__)


class ServerIdManager:
    """서버 고유 아이디 생성 및 관리 클래스"""
    
    def __init__(self):
        self._cached_server_id: Optional[str] = None
        self._server_info: Dict[str, Any] = {}
        
    def get_server_id(self) -> str:
        """
        서버 고유 아이디를 반환합니다.
        캐시된 값이 있으면 반환하고, 없으면 새로 생성합니다.
        
        Returns:
            str: 서버 고유 아이디
        """
        if self._cached_server_id is None:
            self._cached_server_id = self._generate_server_id()
            logger.info(f"Generated new server ID: {self._cached_server_id}")
        
        return self._cached_server_id
    
    def _generate_server_id(self) -> str:
        """
        서버 고유 아이디를 생성합니다.
        
        우선순위:
        1. 환경 변수 SERVER_ID (고정값)
        2. 환경 변수 SERVER_DOMAIN 기반 해시
        3. 로컬 개발 환경 (localhost/127.0.0.1) 전용 ID
        4. 호스트명 기반 해시
        5. IP 주소 기반 해시
        6. UUID 기반 (최후 수단)
        
        Returns:
            str: 생성된 서버 고유 아이디
        """
        # 1. 환경 변수에서 직접 설정된 SERVER_ID 확인
        server_id = os.getenv('SERVER_ID')
        if server_id and server_id.lower() != 'auto':
            self._server_info['source'] = 'environment_variable'
            self._server_info['value'] = server_id
            return server_id
        
        # 2. 환경 변수 SERVER_DOMAIN 사용
        server_domain = os.getenv('SERVER_DOMAIN')
        if server_domain:
            server_id = self._hash_identifier(server_domain)
            self._server_info['source'] = 'server_domain'
            self._server_info['value'] = server_domain
            return server_id
        
        # 3. 로컬 개발 환경 감지 (localhost/127.0.0.1)
        try:
            hostname = socket.gethostname()
            ip_address = self._get_local_ip()
            
            # 로컬 개발 환경인지 확인
            is_local = (
                hostname in ['localhost', '127.0.0.1'] or
                ip_address in ['127.0.0.1', '::1'] or
                hostname.startswith('localhost') or
                hostname.endswith('.local')
            )
            
            if is_local:
                # 로컬 개발 환경용 고정 ID
                local_identifier = "localhost-dev"
                server_id = self._hash_identifier(local_identifier)
                self._server_info['source'] = 'localhost_dev'
                self._server_info['value'] = local_identifier
                logger.info(f"Local development environment detected, using stable ID: {server_id}")
                return server_id
                
        except Exception as e:
            logger.warning(f"Failed to detect local environment: {e}")
        
        # 4. 호스트명 사용
        try:
            hostname = socket.gethostname()
            if hostname and hostname != 'localhost':
                server_id = self._hash_identifier(hostname)
                self._server_info['source'] = 'hostname'
                self._server_info['value'] = hostname
                return server_id
        except Exception as e:
            logger.warning(f"Failed to get hostname: {e}")
        
        # 5. IP 주소 사용
        try:
            ip_address = self._get_local_ip()
            if ip_address and ip_address != '127.0.0.1':
                server_id = self._hash_identifier(ip_address)
                self._server_info['source'] = 'ip_address'
                self._server_info['value'] = ip_address
                return server_id
        except Exception as e:
            logger.warning(f"Failed to get IP address: {e}")
        
        # 6. UUID 기반 (최후 수단)
        unique_id = str(uuid.uuid4())
        server_id = self._hash_identifier(unique_id)
        self._server_info['source'] = 'uuid'
        self._server_info['value'] = unique_id
        logger.warning(f"Using UUID-based server ID as fallback: {server_id}")
        
        return server_id
    
    def _hash_identifier(self, identifier: str) -> str:
        """
        식별자를 해시하여 고유 아이디를 생성합니다.
        
        Args:
            identifier: 해시할 식별자 문자열
            
        Returns:
            str: SHA256 해시의 첫 16자리
        """
        # 타임스탬프를 추가하여 고유성 보장 (선택적)
        # identifier_with_salt = f"{identifier}_{datetime.now().strftime('%Y%m%d')}"
        
        hash_object = hashlib.sha256(identifier.encode('utf-8'))
        return hash_object.hexdigest()[:16]  # 16자리로 제한
    
    @lru_cache(maxsize=1)
    def _get_local_ip(self) -> str:
        """
        로컬 IP 주소를 가져옵니다.
        
        Returns:
            str: 로컬 IP 주소
        """
        try:
            # 외부 연결을 시도하여 로컬 IP 주소 확인
            with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
                s.connect(("*******", 80))
                return s.getsockname()[0]
        except Exception:
            # 실패 시 localhost 반환
            return "127.0.0.1"
    
    def get_server_info(self) -> Dict[str, Any]:
        """
        서버 정보를 반환합니다.
        
        Returns:
            Dict[str, Any]: 서버 ID 생성에 사용된 정보
        """
        if not self._server_info:
            # 서버 ID가 아직 생성되지 않았다면 생성
            self.get_server_id()
        
        return {
            'server_id': self._cached_server_id,
            'source': self._server_info.get('source'),
            'source_value': self._server_info.get('value'),
            'generated_at': datetime.now().isoformat()
        }
    
    def validate_server_id(self, server_id: str) -> bool:
        """
        서버 아이디의 유효성을 검증합니다.
        
        Args:
            server_id: 검증할 서버 아이디
            
        Returns:
            bool: 유효성 여부
        """
        if not server_id:
            return False
        
        # 길이 검증 (16자리 해시 또는 사용자 정의)
        if len(server_id) < 8 or len(server_id) > 64:
            return False
        
        # 허용된 문자만 포함하는지 검증 (영숫자, 하이픈, 언더스코어)
        allowed_chars = set('abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-_')
        if not all(c in allowed_chars for c in server_id):
            return False
        
        return True
    
    def reset_cache(self):
        """캐시된 서버 아이디를 초기화합니다."""
        self._cached_server_id = None
        self._server_info.clear()
        logger.info("Server ID cache reset")


# 전역 인스턴스
_server_id_manager = ServerIdManager()


def get_server_id() -> str:
    """
    서버 고유 아이디를 반환하는 편의 함수
    
    Returns:
        str: 서버 고유 아이디
    """
    return _server_id_manager.get_server_id()


def get_server_info() -> Dict[str, Any]:
    """
    서버 정보를 반환하는 편의 함수
    
    Returns:
        Dict[str, Any]: 서버 정보
    """
    return _server_id_manager.get_server_info()


def validate_server_id(server_id: str) -> bool:
    """
    서버 아이디 유효성 검증 편의 함수
    
    Args:
        server_id: 검증할 서버 아이디
        
    Returns:
        bool: 유효성 여부
    """
    return _server_id_manager.validate_server_id(server_id)


def reset_server_id_cache():
    """서버 아이디 캐시 초기화 편의 함수"""
    _server_id_manager.reset_cache()


if __name__ == "__main__":
    # 테스트 코드
    print("=== Server ID Manager Test ===")
    
    manager = ServerIdManager()
    
    # 서버 ID 생성 및 출력
    server_id = manager.get_server_id()
    print(f"Generated Server ID: {server_id}")
    
    # 서버 정보 출력
    server_info = manager.get_server_info()
    print(f"Server Info: {server_info}")
    
    # 유효성 검증 테스트
    print(f"Is valid: {manager.validate_server_id(server_id)}")
    print(f"Invalid test: {manager.validate_server_id('invalid@id!')}")
    
    # 캐시 테스트
    print(f"Cached ID (should be same): {manager.get_server_id()}") 