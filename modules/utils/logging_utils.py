"""
로깅 유틸리티 모듈

이 모듈은 OpenAI API 호출과 관련된 로깅 유틸리티 함수를 제공합니다.
"""

import logging
import os
from datetime import datetime

# 로깅 설정
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def log_openai_model_usage(model_name: str, purpose: str) -> None:
    """
    OpenAI API 모델 사용 정보를 로깅합니다.

    Args:
        model_name: 사용된 OpenAI 모델명
        purpose: API 호출 목적 (예: 'SQL 생성', '데이터 분석')
    """
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    # 환경변수 설정 확인
    env_var_name = "SQL_QUERY_MODEL" if purpose == "SQL 생성" else "DATA_ANALYSIS_MODEL"
    env_var_value = os.getenv(env_var_name, "환경변수 미설정")
    
    # 모델 비교 로그
    if env_var_value != model_name and env_var_value != "환경변수 미설정":
        logger.warning(f"[OpenAI API] 주의: 환경변수 {env_var_name}({env_var_value})와 실제 사용 모델({model_name})이 다릅니다!")
    
    # 기본 사용 로그
    logger.info(f"[OpenAI API] 모델: {model_name}, 목적: {purpose}, 시간: {timestamp}")

def log_config_info() -> None:
    """
    현재 설정된 환경변수 및 구성 정보를 로깅합니다.
    """
    logger.info("----- 애플리케이션 환경설정 정보 -----")
    
    # OpenAI 모델 관련 환경변수 로깅
    sql_model = os.getenv('SQL_QUERY_MODEL', '설정되지 않음')
    data_model = os.getenv('DATA_ANALYSIS_MODEL', '설정되지 않음')
    
    # 환경변수의 중복 설정 확인 및 처리
    if sql_model and '\n' in sql_model:
        sql_models = [m.strip() for m in sql_model.strip().split('\n') if m.strip()]
        if sql_models:
            logger.warning(f"SQL_QUERY_MODEL에 중복 값이 발견되었습니다: {sql_models}")
            logger.warning(f"마지막 값인 '{sql_models[-1]}'이 사용됩니다.")
            sql_model = sql_models[-1]
    
    if data_model and '\n' in data_model:
        data_models = [m.strip() for m in data_model.strip().split('\n') if m.strip()]
        if data_models:
            logger.warning(f"DATA_ANALYSIS_MODEL에 중복 값이 발견되었습니다: {data_models}")
            logger.warning(f"마지막 값인 '{data_models[-1]}'이 사용됩니다.")
            data_model = data_models[-1]
    
    logger.info(f"SQL_QUERY_MODEL (환경변수): {sql_model}")
    logger.info(f"DATA_ANALYSIS_MODEL (환경변수): {data_model}")
    
    # 기타 중요 환경변수 확인
    openai_api_key = "설정됨" if os.getenv('OPENAI_API_KEY') else "설정되지 않음"
    mysql_host = os.getenv('MYSQL_HOST', '설정되지 않음')
    mysql_database = os.getenv('MYSQL_DATABASE', '설정되지 않음')
    
    logger.info(f"OPENAI_API_KEY: {openai_api_key}")
    logger.info(f"MYSQL_HOST: {mysql_host}")
    logger.info(f"MYSQL_DATABASE: {mysql_database}")
    
    # 로깅 레벨 확인
    current_level = logging.getLevelName(logger.getEffectiveLevel())
    logger.info(f"현재 로깅 레벨: {current_level}")
    
    logger.info("----- 환경설정 정보 로깅 완료 -----") 