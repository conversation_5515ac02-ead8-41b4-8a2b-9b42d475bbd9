"""
LangGraph 워크플로우 상태 관리 서비스

이 모듈은 LangGraph 워크플로우의 상태를 DynamoDB에 저장하고 복원하는 
고급 상태 관리 기능을 구현합니다. 체크포인트 관리, 상태 직렬화/역직렬화,
버전 관리 등의 기능을 제공합니다.
"""

import logging
import json
import pickle
import base64
import uuid
from typing import Dict, Any, List, Optional, Tuple, Union, Iterator
from datetime import datetime, timezone, timedelta
from dataclasses import dataclass, asdict
from collections import defaultdict

try:
    from .dynamodb_crud import get_dynamodb_crud, DynamoDBCRUD
    from .dynamodb_models import (
        create_session_key, parse_session_key,
        compress_content, decompress_content
    )
    from .server_id_manager import get_server_id
    from .session_manager import get_session_manager
except ImportError:
    # 직접 실행 시 절대 import 사용
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
    from modules.utils.dynamodb_crud import get_dynamodb_crud, DynamoDBCRUD
    from modules.utils.dynamodb_models import (
        create_session_key, parse_session_key,
        compress_content, decompress_content
    )
    from modules.utils.server_id_manager import get_server_id
    from modules.utils.session_manager import get_session_manager

logger = logging.getLogger(__name__)


@dataclass
class WorkflowCheckpoint:
    """워크플로우 체크포인트 데이터 클래스"""
    checkpoint_id: str
    thread_id: str
    session_id: str
    server_id: str
    version: int
    timestamp: datetime
    state_data: Dict[str, Any]
    metadata: Dict[str, Any]
    parent_checkpoint_id: Optional[str] = None
    compressed: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        """딕셔너리로 변환"""
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'WorkflowCheckpoint':
        """딕셔너리에서 생성"""
        data = data.copy()
        data['timestamp'] = datetime.fromisoformat(data['timestamp'])
        return cls(**data)


@dataclass
class StateSnapshot:
    """상태 스냅샷 데이터 클래스"""
    config: Dict[str, Any]
    values: Dict[str, Any]
    next: List[str]
    metadata: Dict[str, Any]
    created_at: datetime
    parent_config: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """딕셔너리로 변환"""
        data = asdict(self)
        data['created_at'] = self.created_at.isoformat()
        if self.parent_config:
            data['parent_config'] = self.parent_config
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'StateSnapshot':
        """딕셔너리에서 생성"""
        data = data.copy()
        data['created_at'] = datetime.fromisoformat(data['created_at'])
        return cls(**data)


class LangGraphStateManager:
    """LangGraph 워크플로우 상태 관리 클래스"""
    
    def __init__(self):
        self.crud = get_dynamodb_crud()
        self.server_id = get_server_id()
        self.session_manager = get_session_manager()
        self._checkpoint_cache: Dict[str, WorkflowCheckpoint] = {}
        self._state_cache: Dict[str, StateSnapshot] = {}
        
        # 상태 버전 관리
        self.current_version = 1
        self.supported_versions = [1]
    
    # ========== 체크포인트 관리 ==========
    
    def create_checkpoint(self, thread_id: str, session_id: str, state_data: Dict[str, Any],
                         metadata: Optional[Dict[str, Any]] = None,
                         parent_checkpoint_id: Optional[str] = None) -> Optional[WorkflowCheckpoint]:
        """
        새로운 체크포인트를 생성합니다.
        
        Args:
            thread_id: 스레드 ID
            session_id: 세션 ID
            state_data: 상태 데이터
            metadata: 메타데이터
            parent_checkpoint_id: 부모 체크포인트 ID
            
        Returns:
            WorkflowCheckpoint: 생성된 체크포인트 또는 None
        """
        try:
            # 체크포인트 ID 생성
            checkpoint_id = str(uuid.uuid4())
            
            # 상태 데이터 직렬화 및 압축
            serialized_state, compressed = self._serialize_state_data(state_data)
            
            # 체크포인트 객체 생성
            checkpoint = WorkflowCheckpoint(
                checkpoint_id=checkpoint_id,
                thread_id=thread_id,
                session_id=session_id,
                server_id=self.server_id,
                version=self.current_version,
                timestamp=datetime.now(timezone.utc),
                state_data=serialized_state,
                metadata=metadata or {},
                parent_checkpoint_id=parent_checkpoint_id,
                compressed=compressed
            )
            
            # DynamoDB에 저장
            if self._save_checkpoint_to_db(checkpoint):
                # 캐시에 저장
                self._checkpoint_cache[checkpoint_id] = checkpoint
                
                logger.info(f"체크포인트 생성 완료: {checkpoint_id} (thread: {thread_id})")
                return checkpoint
            else:
                logger.error("체크포인트 저장 실패")
                return None
                
        except Exception as e:
            logger.error(f"체크포인트 생성 중 오류: {e}")
            return None
    
    def get_checkpoint(self, checkpoint_id: str, use_cache: bool = True) -> Optional[WorkflowCheckpoint]:
        """
        체크포인트를 조회합니다.
        
        Args:
            checkpoint_id: 체크포인트 ID
            use_cache: 캐시 사용 여부
            
        Returns:
            WorkflowCheckpoint: 조회된 체크포인트 또는 None
        """
        try:
            # 캐시에서 먼저 확인
            if use_cache and checkpoint_id in self._checkpoint_cache:
                return self._checkpoint_cache[checkpoint_id]
            
            # DynamoDB에서 조회
            checkpoint = self._load_checkpoint_from_db(checkpoint_id)
            if checkpoint:
                # 캐시에 저장
                self._checkpoint_cache[checkpoint_id] = checkpoint
                return checkpoint
            
            return None
            
        except Exception as e:
            logger.error(f"체크포인트 조회 중 오류: {e}")
            return None
    
    def list_checkpoints(self, thread_id: str, limit: int = 50) -> List[WorkflowCheckpoint]:
        """
        스레드의 체크포인트 목록을 조회합니다.
        
        Args:
            thread_id: 스레드 ID
            limit: 조회할 체크포인트 수
            
        Returns:
            List[WorkflowCheckpoint]: 체크포인트 목록
        """
        try:
            return self._list_checkpoints_from_db(thread_id, limit)
        except Exception as e:
            logger.error(f"체크포인트 목록 조회 중 오류: {e}")
            return []
    
    def get_latest_checkpoint(self, thread_id: str) -> Optional[WorkflowCheckpoint]:
        """
        스레드의 최신 체크포인트를 조회합니다.
        
        Args:
            thread_id: 스레드 ID
            
        Returns:
            WorkflowCheckpoint: 최신 체크포인트 또는 None
        """
        try:
            checkpoints = self.list_checkpoints(thread_id, limit=1)
            return checkpoints[0] if checkpoints else None
        except Exception as e:
            logger.error(f"최신 체크포인트 조회 중 오류: {e}")
            return None
    
    # ========== 상태 스냅샷 관리 ==========
    
    def create_state_snapshot(self, config: Dict[str, Any], values: Dict[str, Any],
                            next_nodes: List[str], metadata: Optional[Dict[str, Any]] = None,
                            parent_config: Optional[Dict[str, Any]] = None) -> Optional[StateSnapshot]:
        """
        상태 스냅샷을 생성합니다.
        
        Args:
            config: 설정 정보
            values: 상태 값
            next_nodes: 다음 노드 목록
            metadata: 메타데이터
            parent_config: 부모 설정
            
        Returns:
            StateSnapshot: 생성된 스냅샷 또는 None
        """
        try:
            snapshot = StateSnapshot(
                config=config,
                values=values,
                next=next_nodes,
                metadata=metadata or {},
                created_at=datetime.now(timezone.utc),
                parent_config=parent_config
            )
            
            # 스냅샷 ID 생성 및 캐시 저장
            snapshot_id = f"{config.get('configurable', {}).get('thread_id', 'unknown')}_{datetime.now().timestamp()}"
            self._state_cache[snapshot_id] = snapshot
            
            logger.info(f"상태 스냅샷 생성 완료: {snapshot_id}")
            return snapshot
            
        except Exception as e:
            logger.error(f"상태 스냅샷 생성 중 오류: {e}")
            return None
    
    def get_state_snapshot(self, config: Dict[str, Any]) -> Optional[StateSnapshot]:
        """
        설정에 따른 상태 스냅샷을 조회합니다.
        
        Args:
            config: 설정 정보
            
        Returns:
            StateSnapshot: 조회된 스냅샷 또는 None
        """
        try:
            thread_id = config.get('configurable', {}).get('thread_id')
            checkpoint_id = config.get('configurable', {}).get('checkpoint_id')
            
            if not thread_id:
                logger.error("thread_id가 설정에 없음")
                return None
            
            # 특정 체크포인트 ID가 지정된 경우
            if checkpoint_id:
                checkpoint = self.get_checkpoint(checkpoint_id)
            else:
                # 최신 체크포인트 조회
                checkpoint = self.get_latest_checkpoint(thread_id)
            
            if not checkpoint:
                return None
            
            # 상태 데이터 역직렬화
            state_data = self._deserialize_state_data(checkpoint.state_data, checkpoint.compressed)
            
            # 스냅샷 생성
            snapshot = StateSnapshot(
                config=config,
                values=state_data,
                next=[],  # 실제 구현에서는 워크플로우 정의에서 가져와야 함
                metadata=checkpoint.metadata,
                created_at=checkpoint.timestamp,
                parent_config={'configurable': {'checkpoint_id': checkpoint.parent_checkpoint_id}} if checkpoint.parent_checkpoint_id else None
            )
            
            return snapshot
            
        except Exception as e:
            logger.error(f"상태 스냅샷 조회 중 오류: {e}")
            return None
    
    # ========== 상태 업데이트 ==========
    
    def update_state(self, config: Dict[str, Any], values: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        상태를 업데이트하고 새로운 체크포인트를 생성합니다.
        
        Args:
            config: 설정 정보
            values: 업데이트할 값
            
        Returns:
            Dict[str, Any]: 새로운 설정 정보 또는 None
        """
        try:
            thread_id = config.get('configurable', {}).get('thread_id')
            if not thread_id:
                logger.error("thread_id가 설정에 없음")
                return None
            
            # 현재 상태 조회
            current_snapshot = self.get_state_snapshot(config)
            if not current_snapshot:
                logger.error("현재 상태를 찾을 수 없음")
                return None
            
            # 상태 병합
            updated_values = current_snapshot.values.copy()
            updated_values.update(values)
            
            # 세션 ID 추출 (thread_id에서 또는 별도 설정에서)
            session_id = config.get('configurable', {}).get('session_id', thread_id)
            
            # 새로운 체크포인트 생성
            parent_checkpoint_id = config.get('configurable', {}).get('checkpoint_id')
            new_checkpoint = self.create_checkpoint(
                thread_id=thread_id,
                session_id=session_id,
                state_data=updated_values,
                metadata={'updated_at': datetime.now(timezone.utc).isoformat()},
                parent_checkpoint_id=parent_checkpoint_id
            )
            
            if new_checkpoint:
                # 새로운 설정 반환
                new_config = config.copy()
                new_config['configurable']['checkpoint_id'] = new_checkpoint.checkpoint_id
                return new_config
            
            return None
            
        except Exception as e:
            logger.error(f"상태 업데이트 중 오류: {e}")
            return None
    
    # ========== 상태 직렬화/역직렬화 ==========
    
    def _serialize_state_data(self, state_data: Dict[str, Any]) -> Tuple[Dict[str, Any], bool]:
        """
        상태 데이터를 직렬화합니다.
        
        Args:
            state_data: 상태 데이터
            
        Returns:
            Tuple[Dict[str, Any], bool]: (직렬화된 데이터, 압축 여부)
        """
        try:
            # JSON 직렬화 시도
            try:
                json_str = json.dumps(state_data, ensure_ascii=False, default=str)
                
                # 크기가 크면 압축
                if len(json_str) > 1024:  # 1KB 이상
                    compressed = compress_content(json_str)
                    if len(compressed) < len(json_str):
                        return {'compressed_json': compressed}, True
                
                return {'json': json_str}, False
                
            except (TypeError, ValueError):
                # JSON 직렬화 실패 시 pickle 사용
                pickled_data = pickle.dumps(state_data)
                encoded_data = base64.b64encode(pickled_data).decode('utf-8')
                
                # 압축 시도
                if len(encoded_data) > 1024:
                    compressed = compress_content(encoded_data)
                    if len(compressed) < len(encoded_data):
                        return {'compressed_pickle': compressed}, True
                
                return {'pickle': encoded_data}, False
                
        except Exception as e:
            logger.error(f"상태 데이터 직렬화 실패: {e}")
            return {'error': str(e)}, False
    
    def _deserialize_state_data(self, serialized_data: Dict[str, Any], compressed: bool) -> Dict[str, Any]:
        """
        상태 데이터를 역직렬화합니다.
        
        Args:
            serialized_data: 직렬화된 데이터
            compressed: 압축 여부
            
        Returns:
            Dict[str, Any]: 역직렬화된 상태 데이터
        """
        try:
            if 'error' in serialized_data:
                logger.error(f"직렬화 오류 데이터: {serialized_data['error']}")
                return {}
            
            # JSON 데이터 처리
            if 'json' in serialized_data:
                return json.loads(serialized_data['json'])
            elif 'compressed_json' in serialized_data:
                decompressed = decompress_content(serialized_data['compressed_json'])
                return json.loads(decompressed)
            
            # Pickle 데이터 처리
            elif 'pickle' in serialized_data:
                decoded_data = base64.b64decode(serialized_data['pickle'])
                return pickle.loads(decoded_data)
            elif 'compressed_pickle' in serialized_data:
                decompressed = decompress_content(serialized_data['compressed_pickle'])
                decoded_data = base64.b64decode(decompressed)
                return pickle.loads(decoded_data)
            
            else:
                logger.error(f"알 수 없는 직렬화 형식: {list(serialized_data.keys())}")
                return {}
                
        except Exception as e:
            logger.error(f"상태 데이터 역직렬화 실패: {e}")
            return {}
    
    # ========== DynamoDB 저장/로드 ==========
    
    def _save_checkpoint_to_db(self, checkpoint: WorkflowCheckpoint) -> bool:
        """체크포인트를 DynamoDB에 저장합니다."""
        try:
            # 세션 키 생성 (체크포인트 저장용)
            session_key = f"checkpoint#{checkpoint.server_id}#{checkpoint.thread_id}"
            
            # DynamoDB 아이템 생성
            item = {
                'session_key': session_key,
                'timestamp': checkpoint.timestamp.isoformat(),
                'checkpoint_id': checkpoint.checkpoint_id,
                'session_id': checkpoint.session_id,
                'version': checkpoint.version,
                'state_data': checkpoint.state_data,
                'metadata': checkpoint.metadata,
                'compressed': checkpoint.compressed
            }
            
            if checkpoint.parent_checkpoint_id:
                item['parent_checkpoint_id'] = checkpoint.parent_checkpoint_id
            
            # DynamoDB 리소스 및 테이블 가져오기
            table_names = self.crud._get_table_names()
            dynamodb = self.crud.connector.get_resource()
            table = dynamodb.Table(table_names['messages'])
            
            # 메시지 테이블에 저장
            table.put_item(Item=item)
            return True
            
        except Exception as e:
            logger.error(f"체크포인트 DB 저장 실패: {e}")
            return False
    
    def _load_checkpoint_from_db(self, checkpoint_id: str) -> Optional[WorkflowCheckpoint]:
        """DynamoDB에서 체크포인트를 로드합니다."""
        try:
            # 체크포인트 ID로 검색 (GSI 사용 필요)
            # 실제 구현에서는 GSI를 추가하거나 다른 방법 사용
            logger.warning("체크포인트 직접 로드는 thread_id를 통한 조회를 권장합니다")
            return None
            
        except Exception as e:
            logger.error(f"체크포인트 DB 로드 실패: {e}")
            return None
    
    def _list_checkpoints_from_db(self, thread_id: str, limit: int) -> List[WorkflowCheckpoint]:
        """DynamoDB에서 체크포인트 목록을 조회합니다."""
        try:
            # 세션 키 생성
            session_key = f"checkpoint#{self.server_id}#{thread_id}"
            
            # DynamoDB 리소스 및 테이블 가져오기
            table_names = self.crud._get_table_names()
            dynamodb = self.crud.connector.get_resource()
            table = dynamodb.Table(table_names['messages'])
            
            # 메시지 테이블에서 조회
            response = table.query(
                KeyConditionExpression='session_key = :sk',
                ExpressionAttributeValues={':sk': session_key},
                ScanIndexForward=False,  # 최신 순으로 정렬
                Limit=limit
            )
            
            checkpoints = []
            for item in response.get('Items', []):
                try:
                    checkpoint = WorkflowCheckpoint(
                        checkpoint_id=item['checkpoint_id'],
                        thread_id=thread_id,
                        session_id=item['session_id'],
                        server_id=self.server_id,
                        version=item.get('version', 1),
                        timestamp=datetime.fromisoformat(item['timestamp']),
                        state_data=item['state_data'],
                        metadata=item.get('metadata', {}),
                        parent_checkpoint_id=item.get('parent_checkpoint_id'),
                        compressed=item.get('compressed', False)
                    )
                    
                    checkpoints.append(checkpoint)
                    
                except Exception as e:
                    logger.warning(f"체크포인트 파싱 실패: {e}")
                    continue
            
            return checkpoints
            
        except Exception as e:
            logger.error(f"체크포인트 목록 DB 조회 실패: {e}")
            return []
    
    # ========== 상태 복원 및 관리 ==========
    
    def restore_workflow_state(self, thread_id: str, checkpoint_id: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """
        워크플로우 상태를 복원합니다.
        
        Args:
            thread_id: 스레드 ID
            checkpoint_id: 특정 체크포인트 ID (없으면 최신)
            
        Returns:
            Dict[str, Any]: 복원된 상태 데이터 또는 None
        """
        try:
            # 체크포인트 조회
            if checkpoint_id:
                checkpoint = self.get_checkpoint(checkpoint_id)
            else:
                checkpoint = self.get_latest_checkpoint(thread_id)
            
            if not checkpoint:
                logger.error(f"체크포인트를 찾을 수 없음: thread_id={thread_id}, checkpoint_id={checkpoint_id}")
                return None
            
            # 상태 데이터 역직렬화
            state_data = self._deserialize_state_data(checkpoint.state_data, checkpoint.compressed)
            
            logger.info(f"워크플로우 상태 복원 완료: {checkpoint.checkpoint_id}")
            return state_data
            
        except Exception as e:
            logger.error(f"워크플로우 상태 복원 중 오류: {e}")
            return None
    
    def get_checkpoint_history(self, thread_id: str, limit: int = 10) -> List[Dict[str, Any]]:
        """
        체크포인트 히스토리를 조회합니다.
        
        Args:
            thread_id: 스레드 ID
            limit: 조회할 체크포인트 수
            
        Returns:
            List[Dict[str, Any]]: 체크포인트 히스토리
        """
        try:
            checkpoints = self.list_checkpoints(thread_id, limit)
            
            history = []
            for checkpoint in checkpoints:
                history.append({
                    'checkpoint_id': checkpoint.checkpoint_id,
                    'timestamp': checkpoint.timestamp.isoformat(),
                    'version': checkpoint.version,
                    'metadata': checkpoint.metadata,
                    'parent_checkpoint_id': checkpoint.parent_checkpoint_id,
                    'compressed': checkpoint.compressed,
                    'state_size': len(str(checkpoint.state_data))
                })
            
            return history
            
        except Exception as e:
            logger.error(f"체크포인트 히스토리 조회 중 오류: {e}")
            return []
    
    def cleanup_old_checkpoints(self, thread_id: str, keep_count: int = 10) -> int:
        """
        오래된 체크포인트를 정리합니다.
        
        Args:
            thread_id: 스레드 ID
            keep_count: 유지할 체크포인트 수
            
        Returns:
            int: 삭제된 체크포인트 수
        """
        try:
            checkpoints = self.list_checkpoints(thread_id, limit=1000)
            
            if len(checkpoints) <= keep_count:
                return 0
            
            # 삭제할 체크포인트 선택 (오래된 것부터)
            checkpoints_to_delete = checkpoints[keep_count:]
            deleted_count = 0
            
            for checkpoint in checkpoints_to_delete:
                try:
                    # 실제 삭제 로직 구현 필요
                    # self._delete_checkpoint_from_db(checkpoint.checkpoint_id)
                    
                    # 캐시에서 제거
                    self._checkpoint_cache.pop(checkpoint.checkpoint_id, None)
                    deleted_count += 1
                    
                except Exception as e:
                    logger.warning(f"체크포인트 삭제 실패: {checkpoint.checkpoint_id} - {e}")
            
            logger.info(f"오래된 체크포인트 정리 완료: {deleted_count}개 삭제")
            return deleted_count
            
        except Exception as e:
            logger.error(f"체크포인트 정리 중 오류: {e}")
            return 0
    
    def cleanup_cache(self):
        """캐시를 정리합니다."""
        self._checkpoint_cache.clear()
        self._state_cache.clear()
        logger.info("LangGraph 상태 매니저 캐시 정리 완료")


# 전역 인스턴스
_langgraph_state_manager = LangGraphStateManager()


def get_langgraph_state_manager() -> LangGraphStateManager:
    """
    LangGraph 상태 매니저 인스턴스를 반환합니다.
    
    Returns:
        LangGraphStateManager: LangGraph 상태 매니저 인스턴스
    """
    return _langgraph_state_manager


if __name__ == "__main__":
    # 테스트 코드
    print("=== LangGraph State Manager Test ===")
    
    manager = LangGraphStateManager()
    
    # 테스트용 데이터
    test_thread_id = "test-thread-" + str(datetime.now().timestamp())
    test_session_id = "test-session-" + str(datetime.now().timestamp())
    
    # 체크포인트 생성 테스트
    print("1. 체크포인트 생성 테스트")
    state_data = {
        'messages': [
            {'role': 'user', 'content': '안녕하세요'},
            {'role': 'assistant', 'content': '안녕하세요! 무엇을 도와드릴까요?'}
        ],
        'current_step': 'generate_sql',
        'sql_query': 'SELECT * FROM users',
        'metadata': {'user_id': 'test_user'}
    }
    
    checkpoint1 = manager.create_checkpoint(
        thread_id=test_thread_id,
        session_id=test_session_id,
        state_data=state_data,
        metadata={'step': 'initial'}
    )
    
    if checkpoint1:
        print(f"✅ 체크포인트 생성 성공: {checkpoint1.checkpoint_id}")
        
        # 상태 업데이트 및 새 체크포인트 생성
        print("2. 상태 업데이트 테스트")
        updated_state = state_data.copy()
        updated_state['current_step'] = 'execute_query'
        updated_state['query_result'] = [{'count': 100}]
        
        checkpoint2 = manager.create_checkpoint(
            thread_id=test_thread_id,
            session_id=test_session_id,
            state_data=updated_state,
            metadata={'step': 'query_executed'},
            parent_checkpoint_id=checkpoint1.checkpoint_id
        )
        
        if checkpoint2:
            print(f"✅ 상태 업데이트 성공: {checkpoint2.checkpoint_id}")
            
            # 체크포인트 목록 조회
            print("3. 체크포인트 목록 조회 테스트")
            checkpoints = manager.list_checkpoints(test_thread_id)
            print(f"✅ 체크포인트 목록 조회: {len(checkpoints)}개")
            
            # 최신 체크포인트 조회
            print("4. 최신 체크포인트 조회 테스트")
            latest = manager.get_latest_checkpoint(test_thread_id)
            if latest:
                print(f"✅ 최신 체크포인트: {latest.checkpoint_id}")
            
            # 상태 복원 테스트
            print("5. 상태 복원 테스트")
            restored_state = manager.restore_workflow_state(test_thread_id)
            if restored_state:
                print(f"✅ 상태 복원 성공: current_step = {restored_state.get('current_step')}")
            
            # 체크포인트 히스토리 조회
            print("6. 체크포인트 히스토리 조회 테스트")
            history = manager.get_checkpoint_history(test_thread_id)
            if history:
                print(f"✅ 히스토리 조회 성공: {len(history)}개 항목")
                for item in history:
                    print(f"   - {item['checkpoint_id']}: {item['metadata']}")
        
        # 정리
        print("7. 테스트 데이터 정리")
        manager.cleanup_cache()
        print("✅ 캐시 정리 완료")
    else:
        print("❌ 체크포인트 생성 실패") 