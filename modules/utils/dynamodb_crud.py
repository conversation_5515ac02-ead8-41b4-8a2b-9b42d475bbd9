"""
DynamoDB CRUD 작업 구현

이 모듈은 DynamoDB에서 채팅 세션 및 메시지 데이터에 대한
CRUD(Create, Read, Update, Delete) 작업을 구현합니다.
"""

import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timezone, timedelta
from botocore.exceptions import ClientError
import json
import time

try:
    from .dynamodb_connector import get_dynamodb_connector
    from .dynamodb_models import (
        ChatSession, ChatMessage, SessionSummary, SessionStatus, MessageType,
        create_session_key, parse_session_key, compress_content, decompress_content
    )
    from .server_id_manager import get_server_id
except ImportError:
    # 직접 실행 시 절대 import 사용
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
    from modules.utils.dynamodb_connector import get_dynamodb_connector
    from modules.utils.dynamodb_models import (
        ChatSession, ChatMessage, SessionSummary, SessionStatus, MessageType,
        create_session_key, parse_session_key, compress_content, decompress_content
    )
    from modules.utils.server_id_manager import get_server_id

logger = logging.getLogger(__name__)


class DynamoDBCRUD:
    """DynamoDB CRUD 작업 클래스"""
    
    def __init__(self):
        self.connector = get_dynamodb_connector()
        self._ensure_connection()
    
    def _ensure_connection(self):
        """DynamoDB 연결을 확인하고 필요시 연결합니다."""
        if not self.connector.is_connected():
            if not self.connector.connect():
                raise Exception(f"DynamoDB 연결 실패: {self.connector.get_connection_error()}")
            
            # 테이블 생성 확인
            if not self.connector.create_tables():
                raise Exception("DynamoDB 테이블 생성 실패")
    
    def _get_table_names(self) -> Dict[str, str]:
        """테이블 이름을 반환합니다."""
        config = self.connector.get_config()
        return {
            'sessions': config['sessions_table'],
            'messages': config['messages_table']
        }
    
    def _retry_operation(self, operation, max_retries: int = 3, delay: float = 0.1):
        """
        DynamoDB 작업을 재시도합니다.
        
        Args:
            operation: 실행할 작업 함수
            max_retries: 최대 재시도 횟수
            delay: 재시도 간격 (초)
        """
        for attempt in range(max_retries + 1):
            try:
                return operation()
            except ClientError as e:
                error_code = e.response['Error']['Code']
                
                # 재시도 가능한 오류인지 확인
                if error_code in ['ProvisionedThroughputExceededException', 'ThrottlingException']:
                    if attempt < max_retries:
                        wait_time = delay * (2 ** attempt)  # 지수 백오프
                        logger.warning(f"DynamoDB 스로틀링 발생, {wait_time}초 후 재시도 ({attempt + 1}/{max_retries})")
                        time.sleep(wait_time)
                        continue
                
                # 재시도 불가능한 오류는 즉시 발생
                raise
            except Exception as e:
                # 기타 예외는 재시도하지 않음
                raise
        
        # 모든 재시도 실패
        raise Exception(f"DynamoDB 작업이 {max_retries}번의 재시도 후에도 실패했습니다.")

    # ========== 세션 CRUD 작업 ==========
    
    def create_session(self, session: ChatSession) -> bool:
        """
        새로운 채팅 세션을 생성합니다.
        
        Args:
            session: 생성할 세션 객체
            
        Returns:
            bool: 생성 성공 여부
        """
        try:
            table_names = self._get_table_names()
            dynamodb = self.connector.get_resource()
            table = dynamodb.Table(table_names['sessions'])
            
            def _create():
                # 세션이 이미 존재하는지 확인
                response = table.get_item(
                    Key={
                        'server_id': session.server_id,
                        'session_id': session.session_id
                    }
                )
                
                if 'Item' in response:
                    logger.warning(f"세션이 이미 존재함: {session.session_id}")
                    return False
                
                # 새 세션 생성
                item = session.to_dynamodb_item()
                table.put_item(Item=item)
                logger.info(f"세션 생성 완료: {session.session_id}")
                return True
            
            return self._retry_operation(_create)
            
        except Exception as e:
            logger.error(f"세션 생성 실패: {e}")
            return False
    
    def get_session(self, server_id: str, session_id: str) -> Optional[ChatSession]:
        """
        세션을 조회합니다.
        
        Args:
            server_id: 서버 ID
            session_id: 세션 ID
            
        Returns:
            ChatSession: 조회된 세션 객체 또는 None
        """
        try:
            table_names = self._get_table_names()
            dynamodb = self.connector.get_resource()
            table = dynamodb.Table(table_names['sessions'])
            
            def _get():
                response = table.get_item(
                    Key={
                        'server_id': server_id,
                        'session_id': session_id
                    }
                )
                
                if 'Item' not in response:
                    return None
                
                return ChatSession.from_dynamodb_item(response['Item'])
            
            return self._retry_operation(_get)
            
        except Exception as e:
            logger.error(f"세션 조회 실패: {e}")
            return None
    
    def update_session(self, session: ChatSession) -> bool:
        """
        세션을 업데이트합니다.
        
        Args:
            session: 업데이트할 세션 객체
            
        Returns:
            bool: 업데이트 성공 여부
        """
        try:
            table_names = self._get_table_names()
            dynamodb = self.connector.get_resource()
            table = dynamodb.Table(table_names['sessions'])
            
            def _update():
                # 업데이트 시간 갱신
                session.updated_at = datetime.now(timezone.utc)
                session.last_activity_at = datetime.now(timezone.utc)
                
                item = session.to_dynamodb_item()
                table.put_item(Item=item)
                logger.info(f"세션 업데이트 완료: {session.session_id}")
                return True
            
            return self._retry_operation(_update)
            
        except Exception as e:
            logger.error(f"세션 업데이트 실패: {e}")
            return False
    
    def delete_session(self, server_id: str, session_id: str) -> bool:
        """
        세션을 삭제합니다.
        
        Args:
            server_id: 서버 ID
            session_id: 세션 ID
            
        Returns:
            bool: 삭제 성공 여부
        """
        try:
            table_names = self._get_table_names()
            dynamodb = self.connector.get_resource()
            sessions_table = dynamodb.Table(table_names['sessions'])
            messages_table = dynamodb.Table(table_names['messages'])
            
            def _delete():
                # 먼저 세션의 모든 메시지 삭제
                session_key = create_session_key(server_id, session_id)
                self._delete_all_messages_for_session(session_key)
                
                # 세션 삭제
                sessions_table.delete_item(
                    Key={
                        'server_id': server_id,
                        'session_id': session_id
                    }
                )
                logger.info(f"세션 삭제 완료: {session_id}")
                return True
            
            return self._retry_operation(_delete)
            
        except Exception as e:
            logger.error(f"세션 삭제 실패: {e}")
            return False
    
    def list_sessions(self, server_id: str, limit: int = 50, last_evaluated_key: Optional[Dict] = None) -> Tuple[List[SessionSummary], Optional[Dict]]:
        """
        서버의 세션 목록을 조회합니다.
        
        Args:
            server_id: 서버 ID
            limit: 조회할 세션 수
            last_evaluated_key: 페이지네이션을 위한 마지막 키
            
        Returns:
            Tuple[List[SessionSummary], Optional[Dict]]: (세션 목록, 다음 페이지 키)
        """
        try:
            table_names = self._get_table_names()
            dynamodb = self.connector.get_resource()
            table = dynamodb.Table(table_names['sessions'])
            
            def _list():
                query_kwargs = {
                    'IndexName': 'CreatedAtIndex',
                    'KeyConditionExpression': 'server_id = :server_id',
                    'ExpressionAttributeValues': {
                        ':server_id': server_id
                    },
                    'ScanIndexForward': False,  # 최신 순으로 정렬
                    'Limit': limit
                }
                
                if last_evaluated_key:
                    query_kwargs['ExclusiveStartKey'] = last_evaluated_key
                
                response = table.query(**query_kwargs)
                
                sessions = []
                for item in response.get('Items', []):
                    session = ChatSession.from_dynamodb_item(item)
                    sessions.append(SessionSummary.from_session(session))
                
                next_key = response.get('LastEvaluatedKey')
                return sessions, next_key
            
            return self._retry_operation(_list)
            
        except Exception as e:
            logger.error(f"세션 목록 조회 실패: {e}")
            return [], None

    # ========== 메시지 CRUD 작업 ==========
    
    def create_message(self, message: ChatMessage) -> bool:
        """
        새로운 메시지를 생성합니다.
        
        Args:
            message: 생성할 메시지 객체
            
        Returns:
            bool: 생성 성공 여부
        """
        try:
            table_names = self._get_table_names()
            dynamodb = self.connector.get_resource()
            table = dynamodb.Table(table_names['messages'])
            
            def _create():
                # 내용 압축 처리
                if len(message.content) > 1000:  # 1KB 이상이면 압축
                    compressed_content, is_compressed = compress_content(message.content)
                    if is_compressed:
                        message.compressed_content = compressed_content
                        message.is_compressed = True
                        # 원본 내용은 압축된 내용으로 대체하지 않음 (조회 시 압축 해제)
                
                item = message.to_dynamodb_item()
                table.put_item(Item=item)
                
                # 세션의 메시지 수 증가
                self._increment_session_message_count(message.get_server_id(), message.get_session_id())
                
                logger.info(f"메시지 생성 완료: {message.message_id}")
                return True
            
            return self._retry_operation(_create)
            
        except Exception as e:
            logger.error(f"메시지 생성 실패: {e}")
            return False
    
    def get_message(self, session_key: str, timestamp: str) -> Optional[ChatMessage]:
        """
        메시지를 조회합니다.
        
        Args:
            session_key: 세션 키
            timestamp: 메시지 타임스탬프
            
        Returns:
            ChatMessage: 조회된 메시지 객체 또는 None
        """
        try:
            table_names = self._get_table_names()
            dynamodb = self.connector.get_resource()
            table = dynamodb.Table(table_names['messages'])
            
            def _get():
                response = table.get_item(
                    Key={
                        'session_key': session_key,
                        'timestamp': timestamp
                    }
                )
                
                if 'Item' not in response:
                    return None
                
                message = ChatMessage.from_dynamodb_item(response['Item'])
                
                # 압축된 내용 해제
                if message.is_compressed and message.compressed_content:
                    message.content = decompress_content(message.compressed_content, True)
                
                return message
            
            return self._retry_operation(_get)
            
        except Exception as e:
            logger.error(f"메시지 조회 실패: {e}")
            return None
    
    def list_messages(self, session_key: str, limit: int = 50, last_evaluated_key: Optional[Dict] = None, ascending: bool = True) -> Tuple[List[ChatMessage], Optional[Dict]]:
        """
        세션의 메시지 목록을 조회합니다.
        
        Args:
            session_key: 세션 키
            limit: 조회할 메시지 수
            last_evaluated_key: 페이지네이션을 위한 마지막 키
            ascending: 시간 순 정렬 (True: 오래된 순, False: 최신 순)
            
        Returns:
            Tuple[List[ChatMessage], Optional[Dict]]: (메시지 목록, 다음 페이지 키)
        """
        try:
            table_names = self._get_table_names()
            dynamodb = self.connector.get_resource()
            table = dynamodb.Table(table_names['messages'])
            
            def _list():
                query_kwargs = {
                    'KeyConditionExpression': 'session_key = :session_key',
                    'ExpressionAttributeValues': {
                        ':session_key': session_key
                    },
                    'ScanIndexForward': ascending,
                    'Limit': limit
                }
                
                if last_evaluated_key:
                    query_kwargs['ExclusiveStartKey'] = last_evaluated_key
                
                response = table.query(**query_kwargs)
                
                messages = []
                for item in response.get('Items', []):
                    message = ChatMessage.from_dynamodb_item(item)
                    
                    # 압축된 내용 해제
                    if message.is_compressed and message.compressed_content:
                        message.content = decompress_content(message.compressed_content, True)
                    
                    messages.append(message)
                
                next_key = response.get('LastEvaluatedKey')
                return messages, next_key
            
            return self._retry_operation(_list)
            
        except Exception as e:
            logger.error(f"메시지 목록 조회 실패: {e}")
            return [], None
    
    def update_message(self, message: ChatMessage) -> bool:
        """
        메시지를 업데이트합니다.
        
        Args:
            message: 업데이트할 메시지 객체
            
        Returns:
            bool: 업데이트 성공 여부
        """
        try:
            table_names = self._get_table_names()
            dynamodb = self.connector.get_resource()
            table = dynamodb.Table(table_names['messages'])
            
            def _update():
                # 내용 압축 처리
                if len(message.content) > 1000:  # 1KB 이상이면 압축
                    compressed_content, is_compressed = compress_content(message.content)
                    if is_compressed:
                        message.compressed_content = compressed_content
                        message.is_compressed = True
                
                item = message.to_dynamodb_item()
                table.put_item(Item=item)
                logger.info(f"메시지 업데이트 완료: {message.message_id}")
                return True
            
            return self._retry_operation(_update)
            
        except Exception as e:
            logger.error(f"메시지 업데이트 실패: {e}")
            return False
    
    def delete_message(self, session_key: str, timestamp: str) -> bool:
        """
        메시지를 삭제합니다.
        
        Args:
            session_key: 세션 키
            timestamp: 메시지 타임스탬프
            
        Returns:
            bool: 삭제 성공 여부
        """
        try:
            table_names = self._get_table_names()
            dynamodb = self.connector.get_resource()
            table = dynamodb.Table(table_names['messages'])
            
            def _delete():
                table.delete_item(
                    Key={
                        'session_key': session_key,
                        'timestamp': timestamp
                    }
                )
                
                # 세션의 메시지 수 감소
                server_id, session_id = parse_session_key(session_key)
                self._decrement_session_message_count(server_id, session_id)
                
                logger.info(f"메시지 삭제 완료: {session_key}#{timestamp}")
                return True
            
            return self._retry_operation(_delete)
            
        except Exception as e:
            logger.error(f"메시지 삭제 실패: {e}")
            return False

    # ========== 헬퍼 메서드 ==========
    
    def _increment_session_message_count(self, server_id: str, session_id: str):
        """세션의 메시지 수를 증가시킵니다."""
        try:
            table_names = self._get_table_names()
            dynamodb = self.connector.get_resource()
            table = dynamodb.Table(table_names['sessions'])
            
            table.update_item(
                Key={
                    'server_id': server_id,
                    'session_id': session_id
                },
                UpdateExpression='ADD message_count :inc SET last_activity_at = :now',
                ExpressionAttributeValues={
                    ':inc': 1,
                    ':now': datetime.now(timezone.utc).isoformat()
                }
            )
        except Exception as e:
            logger.warning(f"세션 메시지 수 증가 실패: {e}")
    
    def _decrement_session_message_count(self, server_id: str, session_id: str):
        """세션의 메시지 수를 감소시킵니다."""
        try:
            table_names = self._get_table_names()
            dynamodb = self.connector.get_resource()
            table = dynamodb.Table(table_names['sessions'])
            
            table.update_item(
                Key={
                    'server_id': server_id,
                    'session_id': session_id
                },
                UpdateExpression='ADD message_count :dec SET last_activity_at = :now',
                ConditionExpression='message_count > :zero',
                ExpressionAttributeValues={
                    ':dec': -1,
                    ':now': datetime.now(timezone.utc).isoformat(),
                    ':zero': 0
                }
            )
        except Exception as e:
            logger.warning(f"세션 메시지 수 감소 실패: {e}")
    
    def _delete_all_messages_for_session(self, session_key: str):
        """세션의 모든 메시지를 삭제합니다."""
        try:
            table_names = self._get_table_names()
            dynamodb = self.connector.get_resource()
            table = dynamodb.Table(table_names['messages'])
            
            # 세션의 모든 메시지 조회
            response = table.query(
                KeyConditionExpression='session_key = :session_key',
                ExpressionAttributeValues={
                    ':session_key': session_key
                },
                ProjectionExpression='session_key, #ts',
                ExpressionAttributeNames={
                    '#ts': 'timestamp'
                }
            )
            
            # 배치 삭제
            with table.batch_writer() as batch:
                for item in response.get('Items', []):
                    batch.delete_item(
                        Key={
                            'session_key': item['session_key'],
                            'timestamp': item['timestamp']
                        }
                    )
            
            logger.info(f"세션의 모든 메시지 삭제 완료: {session_key}")
            
        except Exception as e:
            logger.error(f"세션 메시지 일괄 삭제 실패: {e}")

    # ========== 배치 작업 ==========
    
    def batch_create_messages(self, messages: List[ChatMessage]) -> bool:
        """
        여러 메시지를 배치로 생성합니다.
        
        Args:
            messages: 생성할 메시지 목록
            
        Returns:
            bool: 생성 성공 여부
        """
        try:
            table_names = self._get_table_names()
            dynamodb = self.connector.get_resource()
            table = dynamodb.Table(table_names['messages'])
            
            def _batch_create():
                with table.batch_writer() as batch:
                    for message in messages:
                        # 내용 압축 처리
                        if len(message.content) > 1000:
                            compressed_content, is_compressed = compress_content(message.content)
                            if is_compressed:
                                message.compressed_content = compressed_content
                                message.is_compressed = True
                        
                        item = message.to_dynamodb_item()
                        batch.put_item(Item=item)
                
                logger.info(f"배치 메시지 생성 완료: {len(messages)}개")
                return True
            
            return self._retry_operation(_batch_create)
            
        except Exception as e:
            logger.error(f"배치 메시지 생성 실패: {e}")
            return False

    # ========== 검색 및 필터링 ==========
    
    def search_sessions_by_title(self, server_id: str, title_keyword: str, limit: int = 20) -> List[SessionSummary]:
        """
        제목으로 세션을 검색합니다.
        
        Args:
            server_id: 서버 ID
            title_keyword: 검색할 제목 키워드
            limit: 조회할 세션 수
            
        Returns:
            List[SessionSummary]: 검색된 세션 목록
        """
        try:
            table_names = self._get_table_names()
            dynamodb = self.connector.get_resource()
            table = dynamodb.Table(table_names['sessions'])
            
            def _search():
                response = table.query(
                    KeyConditionExpression='server_id = :server_id',
                    FilterExpression='contains(title, :keyword)',
                    ExpressionAttributeValues={
                        ':server_id': server_id,
                        ':keyword': title_keyword
                    },
                    Limit=limit
                )
                
                sessions = []
                for item in response.get('Items', []):
                    session = ChatSession.from_dynamodb_item(item)
                    sessions.append(SessionSummary.from_session(session))
                
                return sessions
            
            return self._retry_operation(_search)
            
        except Exception as e:
            logger.error(f"세션 제목 검색 실패: {e}")
            return []

    # ========== 정리 작업 ==========
    
    def cleanup_expired_sessions(self, server_id: str) -> int:
        """
        만료된 세션을 정리합니다.
        
        Args:
            server_id: 서버 ID
            
        Returns:
            int: 정리된 세션 수
        """
        try:
            table_names = self._get_table_names()
            dynamodb = self.connector.get_resource()
            table = dynamodb.Table(table_names['sessions'])
            
            def _cleanup():
                now = datetime.now(timezone.utc)
                
                # 만료된 세션 조회
                response = table.query(
                    KeyConditionExpression='server_id = :server_id',
                    FilterExpression='expires_at < :now OR #status = :expired',
                    ExpressionAttributeValues={
                        ':server_id': server_id,
                        ':now': now.isoformat(),
                        ':expired': SessionStatus.EXPIRED.value
                    },
                    ExpressionAttributeNames={
                        '#status': 'status'
                    }
                )
                
                deleted_count = 0
                for item in response.get('Items', []):
                    session_id = item['session_id']
                    if self.delete_session(server_id, session_id):
                        deleted_count += 1
                
                logger.info(f"만료된 세션 정리 완료: {deleted_count}개")
                return deleted_count
            
            return self._retry_operation(_cleanup)
            
        except Exception as e:
            logger.error(f"만료된 세션 정리 실패: {e}")
            return 0


# 전역 인스턴스
_dynamodb_crud = DynamoDBCRUD()


def get_dynamodb_crud() -> DynamoDBCRUD:
    """
    DynamoDB CRUD 인스턴스를 반환합니다.
    
    Returns:
        DynamoDBCRUD: DynamoDB CRUD 인스턴스
    """
    return _dynamodb_crud


# 편의 함수들
def create_session(server_id: str, title: Optional[str] = None) -> Optional[ChatSession]:
    """
    새로운 세션을 생성하는 편의 함수
    
    Args:
        server_id: 서버 ID
        title: 세션 제목
        
    Returns:
        ChatSession: 생성된 세션 객체 또는 None
    """
    try:
        session = ChatSession(server_id=server_id, title=title)
        if _dynamodb_crud.create_session(session):
            return session
        return None
    except Exception as e:
        logger.error(f"세션 생성 편의 함수 실패: {e}")
        return None


def create_message(session_key: str, message_type: MessageType, content: str, sender: str, **kwargs) -> Optional[ChatMessage]:
    """
    새로운 메시지를 생성하는 편의 함수
    
    Args:
        session_key: 세션 키
        message_type: 메시지 타입
        content: 메시지 내용
        sender: 발신자
        **kwargs: 추가 메시지 속성
        
    Returns:
        ChatMessage: 생성된 메시지 객체 또는 None
    """
    try:
        message = ChatMessage(
            session_key=session_key,
            message_type=message_type,
            content=content,
            sender=sender,
            **kwargs
        )
        if _dynamodb_crud.create_message(message):
            return message
        return None
    except Exception as e:
        logger.error(f"메시지 생성 편의 함수 실패: {e}")
        return None


if __name__ == "__main__":
    # 테스트 코드
    print("=== DynamoDB CRUD Test ===")
    
    crud = DynamoDBCRUD()
    server_id = get_server_id()
    
    # 세션 생성 테스트
    print("1. 세션 생성 테스트")
    session = ChatSession(
        server_id=server_id,
        title="테스트 세션"
    )
    
    if crud.create_session(session):
        print(f"✅ 세션 생성 성공: {session.session_id}")
        
        # 세션 조회 테스트
        print("2. 세션 조회 테스트")
        retrieved_session = crud.get_session(server_id, session.session_id)
        if retrieved_session:
            print(f"✅ 세션 조회 성공: {retrieved_session.title}")
        
        # 메시지 생성 테스트
        print("3. 메시지 생성 테스트")
        session_key = create_session_key(server_id, session.session_id)
        message = ChatMessage(
            session_key=session_key,
            message_type=MessageType.USER,
            content="안녕하세요! 테스트 메시지입니다.",
            sender="user"
        )
        
        if crud.create_message(message):
            print(f"✅ 메시지 생성 성공: {message.message_id}")
            
            # 메시지 목록 조회 테스트
            print("4. 메시지 목록 조회 테스트")
            messages, next_key = crud.list_messages(session_key)
            print(f"✅ 메시지 목록 조회 성공: {len(messages)}개")
            
            # 세션 목록 조회 테스트
            print("5. 세션 목록 조회 테스트")
            sessions, next_key = crud.list_sessions(server_id)
            print(f"✅ 세션 목록 조회 성공: {len(sessions)}개")
        
        # 정리
        print("6. 테스트 데이터 정리")
        if crud.delete_session(server_id, session.session_id):
            print("✅ 테스트 세션 삭제 완료")
    else:
        print("❌ 세션 생성 실패") 