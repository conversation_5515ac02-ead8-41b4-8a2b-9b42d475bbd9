"""
utils 패키지

이 패키지는 LangGraph 워크플로우에서 사용되는 유틸리티 함수를 제공합니다.
"""

from .server_id_manager import (
    get_server_id,
    get_server_info,
    validate_server_id,
    reset_server_id_cache,
    ServerIdManager
)

from .dynamodb_connector import (
    get_dynamodb_connector,
    connect_dynamodb,
    create_tables,
    is_dynamodb_connected,
    DynamoDBConnector
)

from .dynamodb_models import (
    ChatSession,
    ChatMessage,
    SessionSummary,
    WorkflowState,
    MessageType,
    SessionStatus,
    create_session_key,
    parse_session_key,
    compress_content,
    decompress_content
)

from .dynamodb_crud import (
    get_dynamodb_crud,
    create_session,
    create_message,
    DynamoDBCRUD
)

from .session_manager import (
    get_session_manager,
    SessionManager
)

from .message_history_manager import (
    get_message_history_manager,
    MessageHistoryManager
)

from .langgraph_state_manager import (
    get_langgraph_state_manager,
    LangGraphStateManager,
    WorkflowCheckpoint,
    StateSnapshot
)

__all__ = [
    # 서버 ID 관리
    'get_server_id',
    'get_server_info', 
    'validate_server_id',
    'reset_server_id_cache',
    'ServerIdManager',
    
    # DynamoDB 연결
    'get_dynamodb_connector',
    'connect_dynamodb',
    'create_tables',
    'is_dynamodb_connected',
    'DynamoDBConnector',
    
    # DynamoDB 모델
    'ChatSession',
    'ChatMessage',
    'SessionSummary',
    'WorkflowState',
    'MessageType',
    'SessionStatus',
    'create_session_key',
    'parse_session_key',
    'compress_content',
    'decompress_content',
    
    # DynamoDB CRUD
    'get_dynamodb_crud',
    'create_session',
    'create_message',
    'DynamoDBCRUD',
    
    # 세션 관리
    'get_session_manager',
    'SessionManager',
    
    # 메시지 히스토리 관리
    'get_message_history_manager',
    'MessageHistoryManager',
    
    # LangGraph 상태 관리
    'get_langgraph_state_manager',
    'LangGraphStateManager',
    'WorkflowCheckpoint',
    'StateSnapshot'
]

# UI 컴포넌트는 별도로 import (Streamlit 의존성 때문에)
# from ui.session_manager_ui import get_session_manager_ui, render_session_management
