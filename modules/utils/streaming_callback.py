"""
RAG(Retrieval-Augmented Generation) 엔진 모듈

이 모듈은 자연어 질문을 SQL 쿼리로 변환하고 데이터 분석 결과를 생성하는 기능을 제공합니다.
"""

import streamlit as st
import pandas as pd
import re
import os
import json
from typing import Dict, List, Any, Optional, Tuple, Callable
import logging

# LangChain과 LangGraph 모듈 임포트
from langchain.callbacks.base import BaseCallbackHandler



# 로깅 설정
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 스트리밍 콜백 핸들러 클래스 정의
class StreamingCallbackHandler(BaseCallbackHandler):
    """LangChain 스트리밍 응답을 처리하는 콜백 핸들러"""

    def __init__(self, placeholder=None, initial_text="", process_func=None):
        """
        스트리밍 콜백 핸들러 초기화

        Args:
            placeholder: Streamlit placeholder 객체 (None이면 콘솔 출력)
            initial_text: 초기 텍스트
            process_func: 각 토큰을 처리하는 함수 (선택 사항)
        """
        # 데이터 실시간 업데이트 삭제 
        # self.placeholder = placeholder
        self.text = initial_text
        self.process_func = process_func
        self.tokens = []

    def on_llm_new_token(self, token, **kwargs):
        """새 토큰이 생성될 때 호출되는 메서드"""
        self.tokens.append(token)
        self.text += token

        # 프로세스 함수가 있으면 실행
        if self.process_func:
            self.process_func(self.text)

        # 플레이스홀더가 있으면 업데이트
        # if self.placeholder:
        #     self.placeholder.markdown(self.text)
        # else:
            # 콘솔 출력 (디버깅용)
        print(token, end="", flush=True)

    def get_full_text(self):
        """현재까지 수집된 모든 텍스트 반환"""
        return self.text

    def reset(self):
        """핸들러 상태 초기화"""
        self.text = ""
        self.tokens = []

