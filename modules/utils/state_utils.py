"""
상태 관련 유틸리티 함수들

이 모듈은 LangGraph 상태 관리와 관련된 유틸리티 함수들을 제공합니다.
"""

import json
import base64
import pickle
import pandas as pd
from typing import Dict, Any, Optional
from modules.states.graph_state import GraphState
import logging

logger = logging.getLogger(__name__)

def serialize_graph_state(state: GraphState) -> str:
    """
    GraphState를 JSON 문자열로 직렬화합니다.
    
    Args:
        state: 직렬화할 GraphState 객체
        
    Returns:
        JSON 형태의 문자열
    """
    try:
        serializable_state = {}
        
        for key, value in state.items():
            if value is None:
                serializable_state[key] = None
            elif key == "data" and isinstance(value, pd.DataFrame):
                # DataFrame을 JSON으로 직렬화
                serializable_state[key] = {
                    "_type": "dataframe",
                    "_data": value.to_json(orient='records', date_format='iso')
                }
            elif key == "graph":
                # 그래프 객체는 pickle로 직렬화 후 base64 인코딩
                try:
                    pickled_data = pickle.dumps(value)
                    serializable_state[key] = {
                        "_type": "pickled",
                        "_data": base64.b64encode(pickled_data).decode('utf-8')
                    }
                except Exception as e:
                    logger.warning(f"그래프 객체 직렬화 실패: {e}")
                    serializable_state[key] = None
            elif isinstance(value, (str, int, float, bool, list, dict)):
                # 기본 JSON 직렬화 가능한 타입
                serializable_state[key] = value
            else:
                # 다른 객체들은 문자열로 변환
                serializable_state[key] = str(value)
        
        return json.dumps(serializable_state, ensure_ascii=False)
    
    except Exception as e:
        logger.error(f"GraphState 직렬화 실패: {e}")
        return "{}"

def deserialize_graph_state(state_json: str) -> GraphState:
    """
    JSON 문자열을 GraphState로 역직렬화합니다.
    
    Args:
        state_json: JSON 형태의 문자열
        
    Returns:
        복원된 GraphState 객체
    """
    try:
        if not state_json or state_json == "{}":
            return {}
        
        serialized_state = json.loads(state_json)
        deserialized_state = GraphState()
        
        for key, value in serialized_state.items():
            if value is None:
                deserialized_state[key] = None
            elif isinstance(value, dict) and "_type" in value:
                if value["_type"] == "dataframe":
                    # DataFrame 복원
                    try:
                        df_data = json.loads(value["_data"])
                        deserialized_state[key] = pd.DataFrame(df_data)
                    except Exception as e:
                        logger.warning(f"DataFrame 복원 실패: {e}")
                        deserialized_state[key] = None
                elif value["_type"] == "pickled":
                    # pickle 객체 복원
                    try:
                        pickled_data = base64.b64decode(value["_data"])
                        deserialized_state[key] = pickle.loads(pickled_data)
                    except Exception as e:
                        logger.warning(f"pickled 객체 복원 실패: {e}")
                        deserialized_state[key] = None
                else:
                    deserialized_state[key] = value
            else:
                deserialized_state[key] = value
        
        return deserialized_state
    
    except Exception as e:
        logger.error(f"GraphState 역직렬화 실패: {e}")
        return {}

def extract_key_info_from_state(state: GraphState) -> Dict[str, Any]:
    """
    GraphState에서 주요 정보를 추출하여 메타데이터로 반환합니다.
    
    Args:
        state: GraphState 객체
        
    Returns:
        주요 정보가 담긴 딕셔너리
    """
    key_info = {}
    
    if state.get("query"):
        key_info["query"] = state["query"]
    
    if state.get("sql"):
        key_info["sql"] = state["sql"]
    
    if state.get("data") is not None:
        try:
            df = state["data"]
            key_info["data_shape"] = f"{df.shape[0]} rows × {df.shape[1]} columns"
            key_info["data_columns"] = list(df.columns)[:10]  # 최대 10개 컬럼명만
        except:
            key_info["data_shape"] = "DataFrame available"
    
    if state.get("error"):
        key_info["error"] = state["error"]
    
    if state.get("tables_used"):
        key_info["tables_used"] = state["tables_used"]
    
    if state.get("query_type"):
        key_info["query_type"] = state["query_type"]
    
    return key_info

def validate_graph_state(state: Dict[str, Any]) -> bool:
    """
    GraphState가 유효한지 검증합니다.
    
    Args:
        state: 검증할 상태 딕셔너리
        
    Returns:
        유효하면 True, 아니면 False
    """
    try:
        # state가 None이거나 비어있으면 False
        if not state or not isinstance(state, dict):
            return False
        
        # 필수 필드 중 하나라도 있으면 유효한 상태로 간주
        essential_fields = ["query", "sql", "error", "content"]
        
        # 먼저 data가 아닌 다른 필드들 검사
        for key in essential_fields:
            if key in state and state[key] is not None:
                # 문자열이고 비어있지 않으면 유효
                if isinstance(state[key], str) and state[key].strip():
                    return True
        
        # data 필드가 있는 경우 별도 검사 - DataFrame 불리언 문제 방지
        if "data" in state and state["data"] is not None:
            data = state["data"]
            try:
                # pandas DataFrame인지 확인
                if hasattr(data, 'empty'):
                    # DataFrame인 경우 - empty 속성을 직접 확인
                    if not data.empty:
                        return True
                elif hasattr(data, '__len__'):
                    # 다른 길이를 가진 객체인 경우
                    if len(data) > 0:
                        return True
                elif data:
                    # 다른 truthy 값
                    return True
            except Exception:
                # DataFrame 검사 실패 시 무시하고 다른 필드 검사 계속
                pass
        
        # analysis, schema_info 등 다른 필드들도 검사
        other_fields = ["analysis", "schema_info", "graph", "graph_type"]
        for key in other_fields:
            if key in state and state[key] is not None:
                try:
                    # 문자열이면 내용 확인
                    if isinstance(state[key], str) and state[key].strip():
                        return True
                    # 딕셔너리나 리스트면 비어있지 않은지 확인
                    elif isinstance(state[key], (dict, list)) and state[key]:
                        return True
                    # 다른 객체면 None이 아니면 유효
                    elif state[key]:
                        return True
                except Exception:
                    # 검사 중 오류 발생 시 다음 필드로 계속
                    continue
        
        # 모든 검사를 통과했지만 유효한 필드가 없으면 False
        return False
        
    except Exception as e:
        # 검증 중 예외 발생 시 기본적으로 False 반환
        logger.warning(f"GraphState 검증 중 오류 발생: {e}")
        return False

def merge_graph_states(old_state: GraphState, new_state: GraphState) -> GraphState:
    """
    두 GraphState를 병합합니다. new_state가 우선순위를 가집니다.
    
    Args:
        old_state: 기존 상태
        new_state: 새로운 상태
        
    Returns:
        병합된 GraphState
    """
    merged_state = GraphState()
    
    # 기존 상태로 시작
    merged_state.update(old_state)
    
    # 새로운 상태로 업데이트 (None이 아닌 값만)
    for key, value in new_state.items():
        if value is not None:
            merged_state[key] = value
    
    return merged_state

def should_continue(state: Dict[str, Any]) -> str:
    """
    다음 단계를 결정

    Args:
        state: 현재 그래프 상태

    Returns:
        다음 노드 이름
    """
    # 오류가 있으면 종료
    if state.get("error"):
        return "end"

    # SQL이 없으면 종료
    if not state.get("sql"):
        return "end"

    # 데이터가 없으면 종료
    data = state.get("data")
    if data is None:
        return "end"

    # DataFrame이 비어있는지 확인 (DataFrame에 대한 불리언 연산 피하기)
    if hasattr(data, 'empty') and data.empty:
        print("데이터프레임이 비어있습니다.")
        return "end"

    # 데이터가 있으면 분석 단계로
    return "analyze"
