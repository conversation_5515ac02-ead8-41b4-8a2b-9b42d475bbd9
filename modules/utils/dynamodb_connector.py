"""
DynamoDB 연결 및 테이블 관리 모듈

이 모듈은 AWS DynamoDB 또는 로컬 DynamoDB와의 연결을 관리하고,
채팅 히스토리를 위한 테이블을 생성 및 관리합니다.
"""

import boto3
import logging
from typing import Dict, Any, Optional, List
from botocore.exceptions import ClientError, NoCredentialsError
from datetime import datetime, timedelta
import json
import time

try:
    from config import Config
    from .server_id_manager import get_server_id
except ImportError:
    # 직접 실행 시 절대 import 사용
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
    from config import Config
    from modules.utils.server_id_manager import get_server_id

logger = logging.getLogger(__name__)


class DynamoDBConnector:
    """DynamoDB 연결 및 테이블 관리 클래스"""
    
    def __init__(self):
        self._dynamodb = None
        self._dynamodb_resource = None
        self._config = None
        self._is_connected = False
        self._connection_error = None
        
    def connect(self) -> bool:
        """
        DynamoDB에 연결합니다.
        
        Returns:
            bool: 연결 성공 여부
        """
        try:
            self._config = Config.get_dynamodb_config()
            
            # boto3 클라이언트 및 리소스 설정
            session_kwargs = {
                'region_name': self._config['region_name']
            }
            
            # 로컬 DynamoDB 사용 시
            if self._config.get('endpoint_url'):
                session_kwargs['endpoint_url'] = self._config['endpoint_url']
                # 로컬 DynamoDB는 더미 자격 증명 사용
                session_kwargs['aws_access_key_id'] = 'test'
                session_kwargs['aws_secret_access_key'] = 'test'
                logger.info(f"로컬 DynamoDB에 연결 시도: {self._config['endpoint_url']}")
            else:
                # AWS DynamoDB 사용 시 자격 증명 설정
                if self._config.get('aws_access_key_id'):
                    session_kwargs['aws_access_key_id'] = self._config['aws_access_key_id']
                if self._config.get('aws_secret_access_key'):
                    session_kwargs['aws_secret_access_key'] = self._config['aws_secret_access_key']
                if self._config.get('aws_session_token'):
                    session_kwargs['aws_session_token'] = self._config['aws_session_token']
                logger.info(f"AWS DynamoDB에 연결 시도: {self._config['region_name']}")
            
            # DynamoDB 클라이언트 및 리소스 생성
            self._dynamodb = boto3.client('dynamodb', **session_kwargs)
            self._dynamodb_resource = boto3.resource('dynamodb', **session_kwargs)
            
            # 연결 테스트
            self._test_connection()
            
            self._is_connected = True
            self._connection_error = None
            logger.info("DynamoDB 연결 성공")
            return True
            
        except Exception as e:
            self._is_connected = False
            self._connection_error = str(e)
            logger.error(f"DynamoDB 연결 실패: {e}")
            return False
    
    def _test_connection(self):
        """연결 상태를 테스트합니다."""
        try:
            # 테이블 목록 조회로 연결 테스트
            response = self._dynamodb.list_tables()
            logger.debug(f"DynamoDB 테이블 목록: {response.get('TableNames', [])}")
        except Exception as e:
            raise Exception(f"DynamoDB 연결 테스트 실패: {e}")
    
    def is_connected(self) -> bool:
        """연결 상태를 반환합니다."""
        return self._is_connected
    
    def get_connection_error(self) -> Optional[str]:
        """연결 오류 메시지를 반환합니다."""
        return self._connection_error
    
    def create_tables(self) -> bool:
        """
        채팅 히스토리를 위한 DynamoDB 테이블을 생성합니다.
        
        Returns:
            bool: 테이블 생성 성공 여부
        """
        if not self._is_connected:
            logger.error("DynamoDB에 연결되지 않음")
            return False
        
        try:
            # 세션 테이블 생성
            sessions_created = self._create_sessions_table()
            
            # 메시지 테이블 생성
            messages_created = self._create_messages_table()
            
            return sessions_created and messages_created
            
        except Exception as e:
            logger.error(f"테이블 생성 실패: {e}")
            return False
    
    def _create_sessions_table(self) -> bool:
        """세션 테이블을 생성합니다."""
        table_name = self._config['sessions_table']
        
        try:
            # 테이블이 이미 존재하는지 확인
            if self._table_exists(table_name):
                logger.info(f"세션 테이블이 이미 존재함: {table_name}")
                return True
            
            # 테이블 생성
            table = self._dynamodb_resource.create_table(
                TableName=table_name,
                KeySchema=[
                    {
                        'AttributeName': 'server_id',
                        'KeyType': 'HASH'  # 파티션 키
                    },
                    {
                        'AttributeName': 'session_id',
                        'KeyType': 'RANGE'  # 정렬 키
                    }
                ],
                AttributeDefinitions=[
                    {
                        'AttributeName': 'server_id',
                        'AttributeType': 'S'
                    },
                    {
                        'AttributeName': 'session_id',
                        'AttributeType': 'S'
                    },
                    {
                        'AttributeName': 'created_at',
                        'AttributeType': 'S'
                    }
                ],
                GlobalSecondaryIndexes=[
                    {
                        'IndexName': 'CreatedAtIndex',
                        'KeySchema': [
                            {
                                'AttributeName': 'server_id',
                                'KeyType': 'HASH'
                            },
                            {
                                'AttributeName': 'created_at',
                                'KeyType': 'RANGE'
                            }
                        ],
                        'Projection': {
                            'ProjectionType': 'ALL'
                        }
                    }
                ],
                BillingMode='PAY_PER_REQUEST'
            )
            
            # 테이블 생성 완료 대기
            table.wait_until_exists()
            logger.info(f"세션 테이블 생성 완료: {table_name}")
            return True
            
        except ClientError as e:
            if e.response['Error']['Code'] == 'ResourceInUseException':
                logger.info(f"세션 테이블이 이미 존재함: {table_name}")
                return True
            else:
                logger.error(f"세션 테이블 생성 실패: {e}")
                return False
    
    def _create_messages_table(self) -> bool:
        """메시지 테이블을 생성합니다."""
        table_name = self._config['messages_table']
        
        try:
            # 테이블이 이미 존재하는지 확인
            if self._table_exists(table_name):
                logger.info(f"메시지 테이블이 이미 존재함: {table_name}")
                return True
            
            # 테이블 생성
            table = self._dynamodb_resource.create_table(
                TableName=table_name,
                KeySchema=[
                    {
                        'AttributeName': 'session_key',
                        'KeyType': 'HASH'  # 파티션 키 (server_id#session_id)
                    },
                    {
                        'AttributeName': 'timestamp',
                        'KeyType': 'RANGE'  # 정렬 키
                    }
                ],
                AttributeDefinitions=[
                    {
                        'AttributeName': 'session_key',
                        'AttributeType': 'S'
                    },
                    {
                        'AttributeName': 'timestamp',
                        'AttributeType': 'S'
                    }
                ],
                BillingMode='PAY_PER_REQUEST'
            )
            
            # 테이블 생성 완료 대기
            table.wait_until_exists()
            logger.info(f"메시지 테이블 생성 완료: {table_name}")
            return True
            
        except ClientError as e:
            if e.response['Error']['Code'] == 'ResourceInUseException':
                logger.info(f"메시지 테이블이 이미 존재함: {table_name}")
                return True
            else:
                logger.error(f"메시지 테이블 생성 실패: {e}")
                return False
    
    def _table_exists(self, table_name: str) -> bool:
        """테이블이 존재하는지 확인합니다."""
        try:
            self._dynamodb.describe_table(TableName=table_name)
            return True
        except ClientError as e:
            if e.response['Error']['Code'] == 'ResourceNotFoundException':
                return False
            else:
                raise
    
    def list_tables(self) -> List[str]:
        """DynamoDB 테이블 목록을 반환합니다."""
        if not self._is_connected:
            return []
        
        try:
            response = self._dynamodb.list_tables()
            return response.get('TableNames', [])
        except Exception as e:
            logger.error(f"테이블 목록 조회 실패: {e}")
            return []
    
    def get_table_info(self, table_name: str) -> Optional[Dict[str, Any]]:
        """테이블 정보를 반환합니다."""
        if not self._is_connected:
            return None
        
        try:
            response = self._dynamodb.describe_table(TableName=table_name)
            return response.get('Table')
        except Exception as e:
            logger.error(f"테이블 정보 조회 실패 ({table_name}): {e}")
            return None
    
    def delete_tables(self) -> bool:
        """테이블을 삭제합니다. (개발/테스트용)"""
        if not self._is_connected:
            logger.error("DynamoDB에 연결되지 않음")
            return False
        
        try:
            sessions_table = self._config['sessions_table']
            messages_table = self._config['messages_table']
            
            # 세션 테이블 삭제
            if self._table_exists(sessions_table):
                self._dynamodb.delete_table(TableName=sessions_table)
                logger.info(f"세션 테이블 삭제: {sessions_table}")
            
            # 메시지 테이블 삭제
            if self._table_exists(messages_table):
                self._dynamodb.delete_table(TableName=messages_table)
                logger.info(f"메시지 테이블 삭제: {messages_table}")
            
            return True
            
        except Exception as e:
            logger.error(f"테이블 삭제 실패: {e}")
            return False
    
    def get_client(self):
        """DynamoDB 클라이언트를 반환합니다."""
        return self._dynamodb
    
    def get_resource(self):
        """DynamoDB 리소스를 반환합니다."""
        return self._dynamodb_resource
    
    def get_config(self) -> Dict[str, Any]:
        """DynamoDB 설정을 반환합니다."""
        return self._config or {}


# 전역 인스턴스
_dynamodb_connector = DynamoDBConnector()


def get_dynamodb_connector() -> DynamoDBConnector:
    """
    DynamoDB 커넥터 인스턴스를 반환합니다.
    
    Returns:
        DynamoDBConnector: DynamoDB 커넥터 인스턴스
    """
    return _dynamodb_connector


def connect_dynamodb() -> bool:
    """
    DynamoDB에 연결하는 편의 함수
    
    Returns:
        bool: 연결 성공 여부
    """
    return _dynamodb_connector.connect()


def create_tables() -> bool:
    """
    DynamoDB 테이블을 생성하는 편의 함수
    
    Returns:
        bool: 테이블 생성 성공 여부
    """
    return _dynamodb_connector.create_tables()


def is_dynamodb_connected() -> bool:
    """
    DynamoDB 연결 상태를 확인하는 편의 함수
    
    Returns:
        bool: 연결 상태
    """
    return _dynamodb_connector.is_connected()


if __name__ == "__main__":
    # 테스트 코드
    print("=== DynamoDB Connector Test ===")
    
    connector = DynamoDBConnector()
    
    # 연결 테스트
    print("DynamoDB 연결 시도...")
    if connector.connect():
        print("✅ DynamoDB 연결 성공")
        
        # 테이블 목록 조회
        tables = connector.list_tables()
        print(f"기존 테이블 목록: {tables}")
        
        # 테이블 생성
        print("테이블 생성 시도...")
        if connector.create_tables():
            print("✅ 테이블 생성 성공")
            
            # 생성된 테이블 목록 조회
            tables = connector.list_tables()
            print(f"생성 후 테이블 목록: {tables}")
            
            # 테이블 정보 조회
            config = connector.get_config()
            sessions_table = config['sessions_table']
            messages_table = config['messages_table']
            
            sessions_info = connector.get_table_info(sessions_table)
            if sessions_info:
                print(f"세션 테이블 상태: {sessions_info['TableStatus']}")
            
            messages_info = connector.get_table_info(messages_table)
            if messages_info:
                print(f"메시지 테이블 상태: {messages_info['TableStatus']}")
        else:
            print("❌ 테이블 생성 실패")
    else:
        print(f"❌ DynamoDB 연결 실패: {connector.get_connection_error()}")
        print("로컬 DynamoDB가 실행 중인지 확인하세요:")
        print("docker-compose -f docker-compose.dynamodb.yml up -d") 