"""
데이터베이스 연결 및 쿼리 실행 모듈

이 모듈은 MySQL 데이터베이스 연결 및 SQL 쿼리 실행 기능을 제공합니다.
LangChain의 SQLDatabase 클래스와 SQLDatabaseToolkit을 활용하여 고급 기능을 제공합니다.
"""

import pandas as pd
from typing import Dict, Any, Optional, Tuple, Union
import logging
from sqlalchemy import text
from sqlalchemy.engine import Engine

from langchain_community.utilities.sql_database import SQLDatabase

# 로컬 모듈 임포트
from modules.utils.db_utils import (
    create_sql_engine,
    create_langchain_db,
    get_database_schema,
    execute_sql,
    check_sql_query
)
from modules.utils.schema_cache import schema_cache

# 임포트를 함수 내부로 이동

# 로깅 설정
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def create_db_connection(db_config: Dict[str, Any]) -> Tuple[Optional[SQLDatabase], Optional[Engine]]:
    """
    데이터베이스 연결 객체를 생성합니다.

    Args:
        db_config: 데이터베이스 연결 설정

    Returns:
        LangChain SQLDatabase 객체와 SQLAlchemy 엔진 튜플
    """
    try:
        # 데이터베이스 연결 설정 확인
        host = db_config.get("host")
        port = db_config.get("port")
        user = db_config.get("user")
        password = db_config.get("password")
        database = db_config.get("database")

        # 필수 파라미터 확인
        if not all([host, user, database]):
            logger.error("데이터베이스 연결 정보가 부족합니다.")
            return None, None

        # 기본값 설정
        if port is None:
            port = 3306

        # SQLAlchemy 엔진 생성
        engine = create_sql_engine(host, port, user, password, database)
        if engine is None:
            logger.error("SQLAlchemy 엔진 생성 실패")
            return None, None

        # LangChain SQLDatabase 객체 생성 (생성된 엔진 전달)
        db = create_langchain_db(db_config, engine)
        if db is None:
            logger.error("LangChain SQLDatabase 객체 생성 실패")
            return None, None

        return db, engine
    except Exception as e:
        logger.error(f"데이터베이스 연결 오류: {e}")
        return None, None



def get_schema_info(db_config: Dict[str, Any], query: Optional[str] = None, api_key: Optional[str] = None, use_cache: bool = True) -> Optional[str]:
    """
    데이터베이스 스키마 정보를 가져옵니다.
    캐시를 활용하여 반복적인 데이터베이스 호출을 줄입니다.

    Args:
        db_config: 데이터베이스 연결 설정
        query: 자연어 질문 (선택적)
        api_key: OpenAI API 키 (선택적)
        use_cache: 캐시 사용 여부 (기본값: True)

    Returns:
        데이터베이스 스키마 정보 문자열 또는 None (실패 시)
    """
    try:
        # 테스트 모드인 경우 테스트 코드 실행
        if query == "__test__" and api_key:
            from modules.utils.sql_tools import test_sql_toolkit
            logger.info("SQL 툴킷 테스트 실행")
            test_result = test_sql_toolkit(db_config, api_key)
            logger.info(f"SQL 툴킷 테스트 결과: {test_result}")
            return "SQL 툴킷 테스트 완료"

        # 캐시 사용이 활성화된 경우 캐시에서 스키마 정보 조회
        if use_cache:
            cached_schema = schema_cache.get(db_config, query)
            if cached_schema:
                logger.info(f"캐시에서 스키마 정보 가져옴 (길이: {len(cached_schema)} 문자)")
                return cached_schema

        # 데이터베이스 연결
        from modules.utils.db_utils import create_langchain_db
        # 필요한 경우 엔진 먼저 생성
        from modules.utils.db_utils import create_sql_engine
        host = db_config.get("host")
        port = db_config.get("port", 3306)
        user = db_config.get("user")
        password = db_config.get("password")
        database = db_config.get("database")

        # 엔진 생성
        engine = create_sql_engine(host, port, user, password, database)
        # create_langchain_db에 엔진 전달
        db = create_langchain_db(db_config, engine)
        if db is None:
            logger.error("데이터베이스 연결 실패")
            return None

        # 질문과 API 키가 제공된 경우 관련 테이블 스키마 정보 가져오기
        if query and api_key:
            logger.info(f"질문 '{query}'에 관련된 테이블 스키마 정보 가져오기")
            from modules.utils.sql_tools import get_relevant_schema_info
            schema_info = get_relevant_schema_info(db, query, api_key)
        else:
            # 모든 테이블 스키마 정보 가져오기
            logger.info("모든 테이블 스키마 정보 가져오기")
            from modules.utils.sql_tools import get_all_tables_info
            schema_info = get_all_tables_info(db)

        if not schema_info:
            # 기존 방식으로 스키마 정보 가져오기 (fallback)
            logger.info("기존 방식으로 스키마 정보 가져오기")
            schema_info = get_database_schema(db)

        # 스키마 정보를 캐시에 저장
        if schema_info and use_cache:
            schema_cache.set(db_config, schema_info, query)
            logger.info(f"스키마 정보를 캐시에 저장함 (길이: {len(schema_info)} 문자)")

        return schema_info
    except Exception as e:
        logger.error(f"스키마 정보 가져오기 오류: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return None


def execute_query_with_langchain(db_config: Dict[str, Any], query: str) -> Tuple[bool, Union[pd.DataFrame, str]]:
    """
    LangChain을 사용하여 SQL 쿼리를 실행합니다.

    Args:
        db_config: 데이터베이스 연결 설정
        query: 실행할 SQL 쿼리

    Returns:
        성공 여부와 결과 데이터 (DataFrame 또는 오류 메시지)
    """
    try:
        logger.info(f"LangChain을 사용하여 쿼리 실행 시작: {query}")

        # 데이터베이스 연결
        logger.info("DB 연결 시도 중...")
        db, engine = create_db_connection(db_config)
        if db is None:
            logger.error("데이터베이스 연결 실패")
            return False, "데이터베이스 연결을 설정할 수 없습니다."
        logger.info("DB 연결 성공")

        # 쿼리 유효성 검사
        logger.info("쿼리 유효성 검사 시작...")
        is_valid, message = check_sql_query(db, query)
        if not is_valid:
            logger.error(f"쿼리 유효성 검사 실패: {message}")
            return False, message
        logger.info("쿼리 유효성 검사 성공")

        # 직접 엔진을 통한 쿼리 테스트 실행 (엔진이 있는 경우에만)
        if engine is not None:
            try:
                logger.info("엔진을 통한 쿼리 테스트 실행...")
                with engine.connect() as conn:
                    result_proxy = conn.execute(text(query))
                    test_result = result_proxy.fetchall()
                    logger.info(f"엔진 테스트 결과: {len(test_result)}개의 행 반환됨")
                    if len(test_result) == 0:
                        logger.info("엔진 테스트: 결과가 없습니다.")
                        # 쿼리가 유효하지만 결과가 없는 경우
                        if "WHERE" in query.upper():
                            logger.info("조건절(WHERE)이 있는 쿼리이며, 조건에 맞는 데이터가 없을 수 있습니다.")
            except Exception as engine_error:
                logger.warning(f"엔진 테스트 실패 (무시하고 계속 진행): {engine_error}")
        else:
            logger.warning("엔진이 None이므로 엔진 테스트를 건너뜁니다.")

        # LangChain을 통한 쿼리 실행
        logger.info("LangChain을 통한 쿼리 실행 시작...")
        success, result = execute_sql(db, query)
        logger.info(f"LangChain 쿼리 실행 결과: 성공={success}")

        return success, result
    except Exception as e:
        error_msg = f"쿼리 실행 오류: {e}"
        logger.error(error_msg)
        return False, error_msg
