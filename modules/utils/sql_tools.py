"""
SQL 데이터베이스 툴 모듈

이 모듈은 LangChain의 SQL 데이터베이스 툴을 활용하여 데이터베이스 스키마 정보를 가져오고
관련 있는 테이블을 검색하는 기능을 제공합니다.
"""

import logging
from typing import Dict, List, Any, Optional, Tuple, Union
import re
import os
import json
from dotenv import load_dotenv

# 환경변수 로드
load_dotenv()

from langchain_openai import ChatOpenAI
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import StrOutputParser
from langchain_core.messages import HumanMessage, SystemMessage
from langchain_community.agent_toolkits.sql.toolkit import SQLDatabaseToolkit
from langchain_community.utilities.sql_database import SQLDatabase

# 로깅 설정
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_allowed_tables() -> List[str]:
    """
    환경 변수에서 허용된 테이블 목록을 가져옵니다.

    Returns:
        허용된 테이블 목록
    """
    allowed_tables_str = os.environ.get('ALLOWED_TABLES', '')
    if not allowed_tables_str:
        logger.warning("ALLOWED_TABLES 환경 변수가 설정되지 않았습니다. 모든 테이블이 허용됩니다.")
        return []

    allowed_tables = [table.strip() for table in allowed_tables_str.split(',')]
    logger.info(f"ALLOWED_TABLES 환경 변수에서 가져온 허용된 테이블 목록: {allowed_tables}")
    logger.info(f"허용된 테이블 수: {len(allowed_tables)}")
    return allowed_tables


def get_openai_model() -> str:
    """
    환경 변수에서 OpenAI 모델명을 가져옵니다.

    Returns:
        OpenAI 모델명 (기본값: gpt-4o)
    """
    model_name = os.environ.get('SQL_QUERY_MODEL', 'gpt-4o')
    logger.info(f"환경 변수에서 가져온 OpenAI 모델명: {model_name}")
    return model_name


def get_table_list(db: SQLDatabase, llm=None) -> List[str]:
    """
    데이터베이스의 테이블 목록을 가져옵니다.
    허용된 테이블만 반환합니다.

    Args:
        db: LangChain SQLDatabase 객체
        llm: LLM 객체 (선택적)

    Returns:
        허용된 테이블 목록
    """
    try:
        # 허용된 테이블 목록 가져오기
        allowed_tables = get_allowed_tables()

        # SQLDatabaseToolkit 사용
        if llm is None:
            logger.info("OpenAI LLM 초기화 (get_table_list)")
            model_name = get_openai_model()
            llm = ChatOpenAI(model=model_name)
            logger.info("OpenAI LLM 초기화 완료 (get_table_list)")

        toolkit = SQLDatabaseToolkit(db=db, llm=llm)
        tools = toolkit.get_tools()

        # 테이블 목록 가져오기 툴 찾기
        list_tables_tool = next(tool for tool in tools if tool.name == "sql_db_list_tables")

        # 테이블 목록 가져오기
        logger.info("테이블 목록 가져오기 시작")
        tables_str = list_tables_tool.invoke("")
        logger.info(f"전체 테이블 목록: {tables_str}")

        # 테이블 목록 파싱
        all_tables = []
        if isinstance(tables_str, str):
            all_tables = tables_str.split(", ")

        # 허용된 테이블만 필터링
        if allowed_tables:
            filtered_tables = [table for table in all_tables if table in allowed_tables]
            logger.info(f"허용된 테이블만 필터링 적용: {len(all_tables)} -> {len(filtered_tables)}")
            return filtered_tables
        else:
            logger.info(f"허용된 테이블 목록이 없어 모든 테이블 반환: {len(all_tables)}")
            return all_tables
    except Exception as e:
        logger.error(f"테이블 목록 가져오기 오류: {e}")
        return []

def get_table_info(db: SQLDatabase, table_name: str, llm=None) -> str:
    """
    특정 테이블의 스키마 정보를 가져옵니다.
    허용된 테이블인 경우에만 정보를 가져옵니다.

    Args:
        db: LangChain SQLDatabase 객체
        table_name: 테이블 이름
        llm: LLM 객체 (선택적)

    Returns:
        테이블 스키마 정보 문자열
    """
    try:
        # 허용된 테이블 목록 가져오기
        allowed_tables = get_allowed_tables()

        # 허용된 테이블인지 확인
        if allowed_tables and table_name not in allowed_tables:
            logger.warning(f"테이블 '{table_name}'은(는) 허용되지 않은 테이블입니다.")
            return ""

        # SQLDatabaseToolkit 사용
        if llm is None:
            logger.info(f"OpenAI LLM 초기화 (get_table_info - {table_name})")
            model_name = get_openai_model()
            llm = ChatOpenAI(model=model_name)
            logger.info(f"OpenAI LLM 초기화 완료 (get_table_info - {table_name})")

        toolkit = SQLDatabaseToolkit(db=db, llm=llm)
        tools = toolkit.get_tools()

        # 스키마 정보 가져오기 툴 찾기
        get_schema_tool = next(tool for tool in tools if tool.name == "sql_db_schema")

        # 테이블 스키마 정보 가져오기
        logger.info(f"테이블 '{table_name}' 스키마 정보 가져오기 시작")
        schema_info = get_schema_tool.invoke(table_name)
        logger.info(f"테이블 '{table_name}' 스키마 정보 가져오기 완료")
        logger.debug(f"테이블 '{table_name}' 스키마 정보: {schema_info[:100]}...")

        return schema_info
    except Exception as e:
        logger.error(f"테이블 정보 가져오기 오류: {e}")
        return ""

def get_all_tables_info(db: SQLDatabase, llm=None) -> str:
    """
    모든 허용된 테이블의 스키마 정보를 가져옵니다.

    Args:
        db: LangChain SQLDatabase 객체
        llm: LLM 객체 (선택적)

    Returns:
        모든 허용된 테이블 스키마 정보 문자열
    """
    try:
        logger.info("모든 허용된 테이블 정보 가져오기 시작")
        # 허용된 테이블 목록 가져오기
        tables = get_table_list(db, llm)
        logger.info(f"가져온 테이블 수: {len(tables)}")

        # 각 테이블의 정보 가져오기
        schema_info = ""
        for table in tables:
            table_info = get_table_info(db, table, llm)
            if table_info:  # 빈 문자열이 아닌 경우에만 추가
                schema_info += f"{table_info}\n\n"

        logger.info(f"모든 허용된 테이블 정보 가져오기 완료 (총 {len(tables)} 테이블)")
        return schema_info.strip()
    except Exception as e:
        logger.error(f"모든 테이블 정보 가져오기 오류: {e}")
        return ""

def find_relevant_tables(db: SQLDatabase, query: str, api_key: str, model_name: str = None) -> List[str]:
    """
    자연어 질문과 관련 있는 테이블을 검색합니다.
    허용된 테이블 중에서만 관련 테이블을 찾습니다.

    Args:
        db: LangChain SQLDatabase 객체
        query: 자연어 질문
        api_key: OpenAI API 키
        model_name: 사용할 모델명

    Returns:
        관련 있는 테이블 목록
    """
    try:
        # 모델명이 지정되지 않은 경우 환경변수에서 가져옴
        if model_name is None:
            model_name = get_openai_model()

        logger.info(f"OpenAI LLM 초기화 (find_relevant_tables) - 모델: {model_name}")
        # LLM 초기화
        llm = ChatOpenAI(
            model=model_name,
            temperature=0,
            api_key=api_key
        )
        logger.info(f"OpenAI LLM 초기화 완료 (find_relevant_tables)")

        # 허용된 테이블 목록 가져오기
        all_tables = get_table_list(db, llm)
        if not all_tables:
            logger.error("테이블 목록을 가져올 수 없습니다.")
            return []

        # 각 테이블의 정보 가져오기
        logger.info("각 테이블의 스키마 정보 가져오기 시작")
        tables_info = {}
        for table in all_tables:
            table_info = get_table_info(db, table, llm)
            if table_info:  # 빈 문자열이 아닌 경우에만 추가
                tables_info[table] = table_info
        logger.info(f"각 테이블의 스키마 정보 가져오기 완료 (총 {len(tables_info)} 테이블)")

        if not tables_info:
            logger.warning("가져온 테이블 정보가 없습니다.")
            return []

        # 테이블 정보를 문자열로 변환
        tables_info_str = "\n\n".join([f"테이블: {table}\n{info}" for table, info in tables_info.items()])

        # 프롬프트 템플릿 정의
        prompt = ChatPromptTemplate.from_messages([
            SystemMessage(content="""당신은 데이터베이스 전문가입니다.
            주어진 자연어 질문과 관련 있는 데이터베이스 테이블을 찾아야 합니다.
            테이블 목록과 각 테이블의 스키마 정보를 분석하여 질문에 답하는 데 필요한 테이블을 선택하세요.

            응답 형식:
            table1, table2, table3

            쉼표로 구분된 테이블 이름만 반환하세요. 다른 설명은 포함하지 마세요.
            """),
            HumanMessage(content=f"""
            # 데이터베이스 스키마:
            ```
            {tables_info_str}
            ```

            # 질문:
            {query}

            이 질문에 답하기 위해 필요한 테이블을 쉼표로 구분하여 나열하세요.
            """)
        ])

        # 체인 생성 및 실행
        logger.info(f"OpenAI API 호출 - 관련 테이블 찾기 (모델: {model_name})")
        chain = prompt | llm | StrOutputParser()
        result = chain.invoke({})
        logger.info(f"OpenAI API 호출 완료 - 관련 테이블 찾기")

        # 결과 파싱
        relevant_tables = [table.strip() for table in result.split(",")]
        logger.info(f"OpenAI 응답 파싱 결과: {relevant_tables}")

        # 존재하는 테이블만 필터링
        relevant_tables = [table for table in relevant_tables if table in all_tables]

        logger.info(f"최종 관련 테이블: {relevant_tables}")
        return relevant_tables
    except Exception as e:
        logger.error(f"관련 테이블 검색 오류: {e}")
        return []

def get_relevant_schema_info(db: SQLDatabase, query: str, api_key: str, model_name: str = None) -> str:
    """
    자연어 질문과 관련 있는 테이블의 스키마 정보를 가져옵니다.
    허용된 테이블 중에서만 관련 테이블을 찾습니다.

    Args:
        db: LangChain SQLDatabase 객체
        query: 자연어 질문
        api_key: OpenAI API 키
        model_name: 사용할 모델명

    Returns:
        관련 있는 테이블의 스키마 정보 문자열
    """
    try:
        # 모델명이 지정되지 않은 경우 환경변수에서 가져옴
        if model_name is None:
            model_name = get_openai_model()

        logger.info(f"OpenAI LLM 초기화 (get_relevant_schema_info) - 모델: {model_name}")
        # LLM 초기화
        llm = ChatOpenAI(
            model=model_name,
            temperature=0,
            api_key=api_key
        )
        logger.info(f"OpenAI LLM 초기화 완료 (get_relevant_schema_info)")

        # 관련 있는 테이블 검색
        logger.info(f"질문 '{query}'에 관련된 테이블 검색 시작")
        relevant_tables = find_relevant_tables(db, query, api_key, model_name)
        logger.info(f"검색된 관련 테이블: {relevant_tables}")

        if not relevant_tables:
            logger.warning("관련 테이블을 찾을 수 없습니다. 모든 테이블 정보를 반환합니다.")
            return get_all_tables_info(db, llm)

        # 관련 있는 테이블의 정보 가져오기
        logger.info(f"관련 테이블의 스키마 정보 가져오기 시작 (총 {len(relevant_tables)} 테이블)")
        schema_info = ""
        for table in relevant_tables:
            table_info = get_table_info(db, table, llm)
            if table_info:  # 빈 문자열이 아닌 경우에만 추가
                schema_info += f"{table_info}\n\n"
        logger.info(f"관련 테이블의 스키마 정보 가져오기 완료")

        return schema_info.strip()
    except Exception as e:
        logger.error(f"관련 스키마 정보 가져오기 오류: {e}")
        return get_all_tables_info(db, None)  # 오류 발생 시 모든 테이블 정보 반환


# 테스트 코드
def test_sql_toolkit(db_config: Dict[str, Any], api_key: str):
    """
    SQL 툴킷 테스트 함수

    Args:
        db_config: 데이터베이스 연결 설정
        api_key: OpenAI API 키
    """
    try:
        logger.info("SQL 툴킷 테스트 시작")
        
        # 중복 연결 방지를 위해 db_connector의 함수 사용
        from modules.utils.db_connector import create_db_connection
        
        # 데이터베이스 연결
        logger.info("데이터베이스 연결 시도")
        db, engine = create_db_connection(db_config)
        if db is None:
            logger.error("데이터베이스 연결 실패")
            return
        logger.info("데이터베이스 연결 성공")

        # LLM 초기화
        logger.info("OpenAI LLM 초기화 (테스트)")
        model_name = get_openai_model()
        llm = ChatOpenAI(
            model=model_name,
            temperature=0,
            api_key=api_key
        )
        logger.info("OpenAI LLM 초기화 완료 (테스트)")

        # SQLDatabaseToolkit 초기화
        logger.info("SQLDatabaseToolkit 초기화")
        toolkit = SQLDatabaseToolkit(db=db, llm=llm)
        tools = toolkit.get_tools()
        logger.info(f"SQLDatabaseToolkit 초기화 완료 (총 {len(tools)} 툴)")

        # 테이블 목록 가져오기 툴 찾기
        logger.info("테이블 목록 가져오기 툴 찾기")
        list_tables_tool = next(tool for tool in tools if tool.name == "sql_db_list_tables")

        # 테이블 목록 가져오기
        logger.info("테이블 목록 가져오기 시작")
        tables_str = list_tables_tool.invoke("")
        logger.info(f"테이블 목록: {tables_str}")

        # 테이블이 있는 경우 첫 번째 테이블의 스키마 정보 가져오기
        if tables_str and isinstance(tables_str, str):
            tables = tables_str.split(", ")
            if tables:
                logger.info(f"첫 번째 테이블 '{tables[0]}'의 스키마 정보 가져오기")
                # 스키마 정보 가져오기 툴 찾기
                get_schema_tool = next(tool for tool in tools if tool.name == "sql_db_schema")

                # 첫 번째 테이블의 스키마 정보 가져오기
                schema_info = get_schema_tool.invoke(tables[0])
                logger.info(f"테이블 '{tables[0]}' 스키마 정보 가져오기 완료")
                logger.debug(f"테이블 '{tables[0]}' 스키마 정보: {schema_info}")

        # 테이블 목록 가져오기 함수 테스트
        logger.info("get_table_list 함수 테스트 시작")
        tables = get_table_list(db, llm)
        logger.info(f"get_table_list 결과: {tables}")

        # 첫 번째 테이블의 스키마 정보 가져오기 함수 테스트
        if tables:
            logger.info(f"get_table_info 함수 테스트 시작 ('{tables[0]}')")
            table_info = get_table_info(db, tables[0], llm)
            logger.info(f"get_table_info 함수 테스트 완료 ('{tables[0]}')")
            logger.debug(f"get_table_info 결과 ('{tables[0]}'): {table_info[:100]}...")

        # 모든 테이블의 스키마 정보 가져오기 함수 테스트
        logger.info("get_all_tables_info 함수 테스트 시작")
        all_tables_info = get_all_tables_info(db, llm)
        logger.info(f"get_all_tables_info 함수 테스트 완료 (결과 길이: {len(all_tables_info)})")

        # 관련 테이블 검색 함수 테스트
        test_query = "사용자의 주문 내역을 보여줘"
        logger.info(f"find_relevant_tables 함수 테스트 시작 ('{test_query}')")
        relevant_tables = find_relevant_tables(db, test_query, api_key)
        logger.info(f"find_relevant_tables 함수 테스트 완료 ('{test_query}')")
        logger.info(f"find_relevant_tables 결과: {relevant_tables}")

        # 관련 스키마 정보 가져오기 함수 테스트
        logger.info(f"get_relevant_schema_info 함수 테스트 시작 ('{test_query}')")
        relevant_schema = get_relevant_schema_info(db, test_query, api_key)
        logger.info(f"get_relevant_schema_info 함수 테스트 완료 ('{test_query}')")
        logger.info(f"get_relevant_schema_info 결과 길이: {len(relevant_schema)}")

        logger.info("SQL 툴킷 테스트 완료")
        return "테스트 완료"
    except Exception as e:
        logger.error(f"SQL 툴킷 테스트 오류: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return f"테스트 실패: {str(e)}"
