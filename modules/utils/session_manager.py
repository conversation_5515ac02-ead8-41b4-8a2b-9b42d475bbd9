"""
세션 관리 서비스

이 모듈은 채팅 세션의 생성, 관리, 복원을 담당하는 서비스 레이어를 구현합니다.
LangGraph 워크플로우와 DynamoDB 간의 중간 계층 역할을 합니다.
"""

import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timezone, timedelta
import json
import uuid

try:
    from .dynamodb_crud import get_dynamodb_crud, DynamoDBCRUD
    from .dynamodb_models import (
        ChatSession, ChatMessage, SessionSummary, WorkflowState,
        MessageType, SessionStatus, create_session_key, parse_session_key
    )
    from .server_id_manager import get_server_id
except ImportError:
    # 직접 실행 시 절대 import 사용
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
    from modules.utils.dynamodb_crud import get_dynamodb_crud, DynamoDBCRUD
    from modules.utils.dynamodb_models import (
        ChatSession, ChatMessage, SessionSummary, WorkflowState,
        MessageType, SessionStatus, create_session_key, parse_session_key
    )
    from modules.utils.server_id_manager import get_server_id

logger = logging.getLogger(__name__)


class SessionManager:
    """채팅 세션 관리 클래스"""
    
    def __init__(self):
        self.crud = get_dynamodb_crud()
        self.server_id = get_server_id()
        self._current_session: Optional[ChatSession] = None
        self._session_cache: Dict[str, ChatSession] = {}
    
    # ========== 세션 생성 및 관리 ==========
    
    def create_new_session(self, title: Optional[str] = None, metadata: Optional[Dict[str, Any]] = None) -> Optional[ChatSession]:
        """
        새로운 채팅 세션을 생성합니다.
        
        Args:
            title: 세션 제목 (없으면 자동 생성)
            metadata: 추가 메타데이터
            
        Returns:
            ChatSession: 생성된 세션 객체 또는 None
        """
        try:
            # 제목이 없으면 자동 생성
            if not title:
                now = datetime.now(timezone.utc)
                title = f"Chat Session {now.strftime('%Y-%m-%d %H:%M')}"
            
            # 세션 객체 생성
            session = ChatSession(
                server_id=self.server_id,
                title=title,
                status=SessionStatus.ACTIVE,
                metadata=metadata or {}
            )
            
            # DynamoDB에 저장
            if self.crud.create_session(session):
                # 캐시에 저장
                self._session_cache[session.session_id] = session
                self._current_session = session
                
                logger.info(f"새 세션 생성 완료: {session.session_id} - {title}")
                return session
            else:
                logger.error("세션 생성 실패")
                return None
                
        except Exception as e:
            logger.error(f"세션 생성 중 오류: {e}")
            return None
    
    def get_session(self, session_id: str, use_cache: bool = True) -> Optional[ChatSession]:
        """
        세션을 조회합니다.
        
        Args:
            session_id: 세션 ID
            use_cache: 캐시 사용 여부
            
        Returns:
            ChatSession: 조회된 세션 객체 또는 None
        """
        try:
            # 캐시에서 먼저 확인
            if use_cache and session_id in self._session_cache:
                return self._session_cache[session_id]
            
            # DynamoDB에서 조회
            session = self.crud.get_session(self.server_id, session_id)
            if session:
                # 캐시에 저장
                self._session_cache[session_id] = session
                return session
            
            return None
            
        except Exception as e:
            logger.error(f"세션 조회 중 오류: {e}")
            return None
    
    def update_session(self, session_id: str, **updates) -> bool:
        """
        세션을 업데이트합니다.
        
        Args:
            session_id: 세션 ID
            **updates: 업데이트할 필드들
            
        Returns:
            bool: 업데이트 성공 여부
        """
        try:
            session = self.get_session(session_id)
            if not session:
                logger.error(f"세션을 찾을 수 없음: {session_id}")
                return False
            
            # 필드 업데이트
            for field, value in updates.items():
                if hasattr(session, field):
                    setattr(session, field, value)
            
            # 업데이트 시간 갱신
            session.updated_at = datetime.now(timezone.utc)
            session.last_activity_at = datetime.now(timezone.utc)
            
            # DynamoDB에 저장
            if self.crud.update_session(session):
                # 캐시 업데이트
                self._session_cache[session_id] = session
                logger.info(f"세션 업데이트 완료: {session_id}")
                return True
            else:
                logger.error(f"세션 업데이트 실패: {session_id}")
                return False
                
        except Exception as e:
            logger.error(f"세션 업데이트 중 오류: {e}")
            return False
    
    def delete_session(self, session_id: str) -> bool:
        """
        세션을 삭제합니다.
        
        Args:
            session_id: 세션 ID
            
        Returns:
            bool: 삭제 성공 여부
        """
        try:
            # DynamoDB에서 삭제
            if self.crud.delete_session(self.server_id, session_id):
                # 캐시에서 제거
                self._session_cache.pop(session_id, None)
                
                # 현재 세션이면 초기화
                if self._current_session and self._current_session.session_id == session_id:
                    self._current_session = None
                
                logger.info(f"세션 삭제 완료: {session_id}")
                return True
            else:
                logger.error(f"세션 삭제 실패: {session_id}")
                return False
                
        except Exception as e:
            logger.error(f"세션 삭제 중 오류: {e}")
            return False
    
    def list_sessions(self, limit: int = 50, last_evaluated_key: Optional[Dict] = None) -> Tuple[List[SessionSummary], Optional[Dict]]:
        """
        세션 목록을 조회합니다.
        
        Args:
            limit: 조회할 세션 수
            last_evaluated_key: 페이지네이션을 위한 마지막 키
            
        Returns:
            Tuple[List[SessionSummary], Optional[Dict]]: (세션 목록, 다음 페이지 키)
        """
        try:
            return self.crud.list_sessions(self.server_id, limit, last_evaluated_key)
        except Exception as e:
            logger.error(f"세션 목록 조회 중 오류: {e}")
            return [], None
    
    # ========== 현재 세션 관리 ==========
    
    def set_current_session(self, session_id: str) -> bool:
        """
        현재 활성 세션을 설정합니다.
        
        Args:
            session_id: 세션 ID
            
        Returns:
            bool: 설정 성공 여부
        """
        try:
            session = self.get_session(session_id)
            if session:
                self._current_session = session
                logger.info(f"현재 세션 설정: {session_id}")
                return True
            else:
                logger.error(f"세션을 찾을 수 없음: {session_id}")
                return False
                
        except Exception as e:
            logger.error(f"현재 세션 설정 중 오류: {e}")
            return False
    
    def get_current_session(self) -> Optional[ChatSession]:
        """
        현재 활성 세션을 반환합니다.
        
        Returns:
            ChatSession: 현재 세션 객체 또는 None
        """
        return self._current_session
    
    def get_current_session_key(self) -> Optional[str]:
        """
        현재 세션의 키를 반환합니다.
        
        Returns:
            str: 세션 키 또는 None
        """
        if self._current_session:
            return create_session_key(self.server_id, self._current_session.session_id)
        return None
    
    # ========== 메시지 관리 ==========
    
    def add_message(self, message_type: MessageType, content: str, sender: str, 
                   session_id: Optional[str] = None, metadata: Optional[Dict[str, Any]] = None) -> Optional[ChatMessage]:
        """
        세션에 메시지를 추가합니다.
        
        Args:
            message_type: 메시지 타입
            content: 메시지 내용
            sender: 발신자
            session_id: 세션 ID (없으면 현재 세션 사용)
            metadata: 추가 메타데이터
            
        Returns:
            ChatMessage: 생성된 메시지 객체 또는 None
        """
        try:
            # 세션 결정
            if session_id:
                session = self.get_session(session_id)
            else:
                session = self._current_session
            
            if not session:
                logger.error("활성 세션이 없음")
                return None
            
            # 세션 키 생성
            session_key = create_session_key(self.server_id, session.session_id)
            
            # 메시지 객체 생성
            message = ChatMessage(
                session_key=session_key,
                message_type=message_type,
                content=content,
                sender=sender,
                metadata=metadata or {}
            )
            
            # DynamoDB에 저장
            if self.crud.create_message(message):
                # 세션의 마지막 활동 시간 업데이트
                self.update_session(session.session_id, last_activity_at=datetime.now(timezone.utc))
                
                logger.info(f"메시지 추가 완료: {message.message_id}")
                return message
            else:
                logger.error("메시지 추가 실패")
                return None
                
        except Exception as e:
            logger.error(f"메시지 추가 중 오류: {e}")
            return None
    
    def get_session_messages(self, session_id: Optional[str] = None, limit: int = 50, 
                           last_evaluated_key: Optional[Dict] = None, ascending: bool = True) -> Tuple[List[ChatMessage], Optional[Dict]]:
        """
        세션의 메시지 목록을 조회합니다.
        
        Args:
            session_id: 세션 ID (없으면 현재 세션 사용)
            limit: 조회할 메시지 수
            last_evaluated_key: 페이지네이션을 위한 마지막 키
            ascending: 시간 순 정렬 (True: 오래된 순, False: 최신 순)
            
        Returns:
            Tuple[List[ChatMessage], Optional[Dict]]: (메시지 목록, 다음 페이지 키)
        """
        try:
            # 세션 결정
            if session_id:
                session = self.get_session(session_id)
            else:
                session = self._current_session
            
            if not session:
                logger.error("활성 세션이 없음")
                return [], None
            
            # 세션 키 생성
            session_key = create_session_key(self.server_id, session.session_id)
            
            # 메시지 목록 조회
            return self.crud.list_messages(session_key, limit, last_evaluated_key, ascending)
            
        except Exception as e:
            logger.error(f"메시지 목록 조회 중 오류: {e}")
            return [], None
    
    # ========== 세션 복원 ==========
    
    def restore_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """
        세션을 복원하여 전체 대화 히스토리를 반환합니다.
        
        Args:
            session_id: 복원할 세션 ID
            
        Returns:
            Dict[str, Any]: 복원된 세션 데이터 또는 None
        """
        try:
            # 세션 조회
            session = self.get_session(session_id)
            if not session:
                logger.error(f"세션을 찾을 수 없음: {session_id}")
                return None
            
            # 모든 메시지 조회
            all_messages = []
            last_key = None
            
            while True:
                messages, last_key = self.get_session_messages(
                    session_id=session_id,
                    limit=100,
                    last_evaluated_key=last_key,
                    ascending=True
                )
                
                all_messages.extend(messages)
                
                if not last_key:
                    break
            
            # 복원 데이터 구성
            restored_data = {
                'session': {
                    'session_id': session.session_id,
                    'title': session.title,
                    'status': session.status.value,
                    'created_at': session.created_at.isoformat(),
                    'updated_at': session.updated_at.isoformat(),
                    'last_activity_at': session.last_activity_at.isoformat(),
                    'message_count': session.message_count,
                    'metadata': session.metadata
                },
                'messages': [
                    {
                        'message_id': msg.message_id,
                        'message_type': msg.message_type.value,
                        'content': msg.content,
                        'sender': msg.sender,
                        'timestamp': msg.timestamp.isoformat(),
                        'metadata': msg.metadata
                    }
                    for msg in all_messages
                ],
                'restored_at': datetime.now(timezone.utc).isoformat(),
                'total_messages': len(all_messages)
            }
            
            # 현재 세션으로 설정
            self.set_current_session(session_id)
            
            logger.info(f"세션 복원 완료: {session_id} ({len(all_messages)}개 메시지)")
            return restored_data
            
        except Exception as e:
            logger.error(f"세션 복원 중 오류: {e}")
            return None
    
    def export_session(self, session_id: str, format: str = 'json') -> Optional[str]:
        """
        세션을 지정된 형식으로 내보냅니다.
        
        Args:
            session_id: 내보낼 세션 ID
            format: 내보낼 형식 ('json', 'text')
            
        Returns:
            str: 내보낸 데이터 또는 None
        """
        try:
            restored_data = self.restore_session(session_id)
            if not restored_data:
                return None
            
            if format == 'json':
                return json.dumps(restored_data, indent=2, ensure_ascii=False)
            
            elif format == 'text':
                lines = []
                session_info = restored_data['session']
                
                lines.append(f"=== {session_info['title']} ===")
                lines.append(f"세션 ID: {session_info['session_id']}")
                lines.append(f"생성일: {session_info['created_at']}")
                lines.append(f"메시지 수: {session_info['message_count']}")
                lines.append("")
                
                for msg in restored_data['messages']:
                    try:
                        if isinstance(msg['timestamp'], str):
                            timestamp = datetime.fromisoformat(msg['timestamp'].replace('Z', '+00:00'))
                        else:
                            timestamp = msg['timestamp']
                        timestamp_str = timestamp.strftime('%Y-%m-%d %H:%M:%S')
                    except:
                        timestamp_str = str(msg['timestamp'])
                    
                    lines.append(f"[{timestamp_str}] {msg['sender']} ({msg['message_type']})")
                    lines.append(msg['content'])
                    lines.append("")
                
                return '\n'.join(lines)
            
            else:
                logger.error(f"지원하지 않는 형식: {format}")
                return None
                
        except Exception as e:
            logger.error(f"세션 내보내기 중 오류: {e}")
            return None
    
    # ========== 세션 검색 및 필터링 ==========
    
    def search_sessions(self, keyword: str, limit: int = 20) -> List[SessionSummary]:
        """
        키워드로 세션을 검색합니다.
        
        Args:
            keyword: 검색 키워드
            limit: 조회할 세션 수
            
        Returns:
            List[SessionSummary]: 검색된 세션 목록
        """
        try:
            return self.crud.search_sessions_by_title(self.server_id, keyword, limit)
        except Exception as e:
            logger.error(f"세션 검색 중 오류: {e}")
            return []
    
    def get_recent_sessions(self, days: int = 7, limit: int = 20) -> List[SessionSummary]:
        """
        최근 활동한 세션 목록을 조회합니다.
        
        Args:
            days: 조회할 일수
            limit: 조회할 세션 수
            
        Returns:
            List[SessionSummary]: 최근 세션 목록
        """
        try:
            sessions, _ = self.list_sessions(limit=limit)
            
            # 최근 활동 기준으로 필터링
            cutoff_date = datetime.now(timezone.utc) - timedelta(days=days)
            recent_sessions = [
                session for session in sessions
                if session.last_activity_at >= cutoff_date
            ]
            
            # 최근 활동 순으로 정렬
            recent_sessions.sort(key=lambda x: x.last_activity_at, reverse=True)
            
            return recent_sessions
            
        except Exception as e:
            logger.error(f"최근 세션 조회 중 오류: {e}")
            return []
    
    # ========== 세션 정리 ==========
    
    def cleanup_expired_sessions(self) -> int:
        """
        만료된 세션을 정리합니다.
        
        Returns:
            int: 정리된 세션 수
        """
        try:
            return self.crud.cleanup_expired_sessions(self.server_id)
        except Exception as e:
            logger.error(f"만료된 세션 정리 중 오류: {e}")
            return 0
    
    def archive_old_sessions(self, days: int = 30) -> int:
        """
        오래된 세션을 아카이브 상태로 변경합니다.
        
        Args:
            days: 아카이브할 일수 기준
            
        Returns:
            int: 아카이브된 세션 수
        """
        try:
            cutoff_date = datetime.now(timezone.utc) - timedelta(days=days)
            sessions, _ = self.list_sessions(limit=1000)  # 충분히 큰 수로 설정
            
            archived_count = 0
            for session in sessions:
                if session.last_activity_at < cutoff_date and session.status == SessionStatus.ACTIVE:
                    if self.update_session(session.session_id, status=SessionStatus.ARCHIVED):
                        archived_count += 1
            
            logger.info(f"오래된 세션 아카이브 완료: {archived_count}개")
            return archived_count
            
        except Exception as e:
            logger.error(f"오래된 세션 아카이브 중 오류: {e}")
            return 0
    
    # ========== 워크플로우 상태 관리 ==========
    
    def save_workflow_state(self, session_id: Optional[str] = None, state_data: Optional[Dict[str, Any]] = None) -> bool:
        """
        워크플로우 상태를 저장합니다.
        
        Args:
            session_id: 세션 ID (없으면 현재 세션 사용)
            state_data: 저장할 상태 데이터
            
        Returns:
            bool: 저장 성공 여부
        """
        try:
            # 세션 결정
            if session_id:
                session = self.get_session(session_id)
            else:
                session = self._current_session
            
            if not session:
                logger.error("활성 세션이 없음")
                return False
            
            # 워크플로우 상태 생성
            workflow_state = WorkflowState(
                session_key=create_session_key(self.server_id, session.session_id),
                state_data=state_data or {}
            )
            
            # 세션 메타데이터에 워크플로우 상태 저장
            metadata = session.metadata.copy()
            metadata['workflow_state'] = workflow_state.to_dict()
            
            return self.update_session(session.session_id, metadata=metadata)
            
        except Exception as e:
            logger.error(f"워크플로우 상태 저장 중 오류: {e}")
            return False
    
    def load_workflow_state(self, session_id: Optional[str] = None) -> Optional[WorkflowState]:
        """
        워크플로우 상태를 로드합니다.
        
        Args:
            session_id: 세션 ID (없으면 현재 세션 사용)
            
        Returns:
            WorkflowState: 로드된 워크플로우 상태 또는 None
        """
        try:
            # 세션 결정
            if session_id:
                session = self.get_session(session_id)
            else:
                session = self._current_session
            
            if not session:
                logger.error("활성 세션이 없음")
                return None
            
            # 세션 메타데이터에서 워크플로우 상태 로드
            workflow_state_data = session.metadata.get('workflow_state')
            if workflow_state_data:
                return WorkflowState.from_dict(workflow_state_data)
            
            return None
            
        except Exception as e:
            logger.error(f"워크플로우 상태 로드 중 오류: {e}")
            return None


# 전역 인스턴스
_session_manager = SessionManager()


def get_session_manager() -> SessionManager:
    """
    세션 매니저 인스턴스를 반환합니다.
    
    Returns:
        SessionManager: 세션 매니저 인스턴스
    """
    return _session_manager


if __name__ == "__main__":
    # 테스트 코드
    print("=== Session Manager Test ===")
    
    manager = SessionManager()
    
    # 새 세션 생성 테스트
    print("1. 새 세션 생성 테스트")
    session = manager.create_new_session("테스트 세션")
    if session:
        print(f"✅ 세션 생성 성공: {session.session_id}")
        
        # 메시지 추가 테스트
        print("2. 메시지 추가 테스트")
        message1 = manager.add_message(MessageType.USER, "안녕하세요!", "user")
        message2 = manager.add_message(MessageType.AI, "안녕하세요! 무엇을 도와드릴까요?", "assistant")
        
        if message1 and message2:
            print(f"✅ 메시지 추가 성공: {message1.message_id}, {message2.message_id}")
            
            # 세션 복원 테스트
            print("3. 세션 복원 테스트")
            restored_data = manager.restore_session(session.session_id)
            if restored_data:
                print(f"✅ 세션 복원 성공: {restored_data['total_messages']}개 메시지")
                
                # 세션 내보내기 테스트
                print("4. 세션 내보내기 테스트")
                exported_text = manager.export_session(session.session_id, 'text')
                if exported_text:
                    print("✅ 세션 내보내기 성공")
                    print("--- 내보낸 내용 (일부) ---")
                    print(exported_text[:200] + "...")
        
        # 정리
        print("5. 테스트 데이터 정리")
        if manager.delete_session(session.session_id):
            print("✅ 테스트 세션 삭제 완료")
    else:
        print("❌ 세션 생성 실패") 