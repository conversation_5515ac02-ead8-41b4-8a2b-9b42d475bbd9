"""
데이터베이스 유틸리티

이 모듈은 데이터베이스 연결 및 관련 유틸리티 함수를 제공합니다.
LangChain의 SQLDatabase 클래스를 활용하여 SQL 데이터베이스 작업을 수행합니다.
"""

import logging
import pandas as pd
from typing import Optional, List, Dict, Any, Tuple, Union
import re

from sqlalchemy import create_engine, text
from sqlalchemy.engine import Engine
from sqlalchemy.exc import SQLAlchemyError
# 공간 데이터 타입 임포트 제거 - 호환성 문제로 인해
# from sqlalchemy.dialects.mysql import POINT, POLYGON, LINESTRING, GEOMETRY
from langchain_community.utilities.sql_database import SQLDatabase

# 로깅 설정
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_sql_engine(host: str, port: int, user: str, password: str, database: str) -> Optional[Engine]:
    """
    SQLAlchemy 엔진 생성

    Args:
        host: 데이터베이스 호스트
        port: 데이터베이스 포트
        user: 사용자명
        password: 비밀번호
        database: 데이터베이스명

    Returns:
        SQLAlchemy 엔진 객체 또는 None (연결 실패 시)
    """
    try:
        # port가 문자열인 경우 정수로 변환
        if isinstance(port, str):
            try:
                port = int(port)
            except (ValueError, TypeError):
                logger.warning(f"포트 번호 변환 오류: '{port}'는 유효한 포트 번호가 아닙니다. 기본값 3306을 사용합니다.")
                port = 3306

        # MySQL 연결 문자열 생성 (공간 데이터 타입 처리를 위한 옵션 추가)
        connection_string = f"mysql+mysqlconnector://{user}:{password}@{host}:{port}/{database}"

        # SQLAlchemy 엔진 생성 (공간 데이터 타입 관련 옵션 추가)
        engine = create_engine(
            connection_string,
            # 연결 풀 사이즈 제한
            pool_size=5,
            # 최대 오버플로우 연결 수
            max_overflow=10,
            # 연결 타임아웃 설정
            pool_timeout=30,
            # 연결 재사용 전 유효성 검사
            pool_recycle=1800,
            # 연결 시 지정한 옵션
            connect_args={
                # 공간 데이터 타입 처리를 위한 옵션
                "use_pure": True,
                # 연결 타임아웃 설정
                "connection_timeout": 10,
            }
        )

        # 연결 테스트
        import warnings
        from sqlalchemy import exc as sa_exc

        # SQLAlchemy 경고 임시 억제
        with warnings.catch_warnings():
            warnings.simplefilter("ignore", category=sa_exc.SAWarning)
            with engine.connect() as conn:
                conn.execute(text("SELECT 1"))
                logger.info(f"데이터베이스 '{database}'에 성공적으로 연결되었습니다.")
                return engine

    except SQLAlchemyError as e:
        logger.error(f"SQLAlchemy 엔진 생성 오류: {e}")
    except Exception as e:
        logger.error(f"예상치 못한 오류: {e}")

    return None

def create_langchain_db(db_config: Dict[str, Any], engine: Optional[Engine] = None) -> Optional[SQLDatabase]:
    """
    LangChain SQLDatabase 객체 생성

    Args:
        db_config: 데이터베이스 연결 설정 디렉토리
        engine: 기존 SQLAlchemy 엔진 (선택적)

    Returns:
        LangChain SQLDatabase 객체 또는 None (생성 실패 시)
    """
    try:
        # 엔진이 제공되지 않은 경우에만 새로 생성
        if engine is None:
            # 데이터베이스 연결 설정 확인
            if not db_config or not all(key in db_config for key in ["host", "port", "user", "password", "database"]):
                logger.error("데이터베이스 연결 정보가 불완전합니다.")
                return None

            # SQLAlchemy 엔진 생성
            engine = create_sql_engine(
                host=db_config["host"],
                port=db_config["port"],
                user=db_config["user"],
                password=db_config["password"],
                database=db_config["database"]
            )

            if engine is None:
                logger.error("SQLAlchemy 엔진 생성 실패")
                return None

        # 공간 데이터 타입 무시 옵션 설정
        # LangChain SQLDatabase 객체 생성 (공간 데이터 타입 관련 경고 억제)
        import warnings
        from sqlalchemy import exc as sa_exc

        # SQLAlchemy 경고 임시 억제
        with warnings.catch_warnings():
            warnings.simplefilter("ignore", category=sa_exc.SAWarning)
            db = SQLDatabase(
                engine,
                # 테이블 정보 새로 가져오기 옵션 설정
                metadata=None,  # 자동으로 생성하도록 설정
                # 샘플 로우 수 제한
                sample_rows_in_table_info=3,
                # 지원하지 않는 타입을 무시하도록 설정
                ignore_tables=[],
                # 지원하지 않는 타입을 무시하도록 설정
                include_tables=None
            )

        return db
    except Exception as e:
        logger.error(f"LangChain SQLDatabase 객체 생성 오류: {e}")
        return None



def get_table_list(db: SQLDatabase) -> List[str]:
    """
    데이터베이스의 테이블 목록을 가져옵니다.

    Args:
        db: LangChain SQLDatabase 객체

    Returns:
        테이블 목록
    """
    try:
        return db.get_usable_table_names()
    except Exception as e:
        logger.error(f"테이블 목록 가져오기 오류: {e}")
        return []

def get_table_info(db: SQLDatabase, table_names: List[str]) -> str:
    """
    지정된 테이블의 스키마 정보를 가져옵니다.

    Args:
        db: LangChain SQLDatabase 객체
        table_names: 정보를 가져올 테이블 목록

    Returns:
        테이블 스키마 정보 문자열
    """
    try:
        return db.get_table_info(table_names=table_names)
    except Exception as e:
        logger.error(f"테이블 정보 가져오기 오류: {e}")
        return ""

def get_database_schema(db: SQLDatabase) -> str:
    """
    전체 데이터베이스 스키마 정보를 가져옵니다.

    Args:
        db: LangChain SQLDatabase 객체

    Returns:
        데이터베이스 스키마 정보 문자열
    """
    try:
        tables = get_table_list(db)
        return get_table_info(db, tables)
    except Exception as e:
        logger.error(f"데이터베이스 스키마 가져오기 오류: {e}")
        return ""

def execute_sql(db: SQLDatabase, query: str) -> Tuple[bool, Union[pd.DataFrame, str]]:
    """
    SQL 쿼리를 실행하고 결과를 반환합니다.

    Args:
        db: LangChain SQLDatabase 객체
        query: 실행할 SQL 쿼리

    Returns:
        성공 여부와 결과 데이터 (DataFrame 또는 오류 메시지)
    """
    try:
        logger.info("=== SQL 쿼리 실행 시작 ===")
        logger.info(f"SQL 쿼리: {query}")
        result = db.run(query)
        logger.info(f"SQL 쿼리 실행 결과 타입: {type(result).__name__}")

        # 결과가 문자열인 경우 DataFrame으로 변환 시도
        if isinstance(result, str):
            logger.info(f"SQL 쿼리 결과(문자열): {result[:200]}{'...' if len(result) > 200 else ''}")
            # 결과가 비어있는 경우
            if not result.strip():
                logger.info("SQL 쿼리 결과가 비어있습니다. 빈 DataFrame 반환")
                return True, pd.DataFrame()

            # 결과가 오류 메시지인지 확인
            if "error" in result.lower() or "exception" in result.lower():
                logger.error(f"SQL 쿼리 결과에 오류 메시지 포함: {result}")
                return False, result

            # 결과를 DataFrame으로 변환 시도
            try:
                # 결과가 튜플 리스트 형태의 문자열인지 확인
                if result.strip().startswith('[') and ')' in result and '(' in result:
                    logger.info(f"튜플 리스트 형태의 문자열 감지. 파싱 시도...")
                    try:
                        import re, ast

                        # datetime.date(YYYY, M, D) 패턴을 'YYYY-MM-DD' 문자열로 치환
                        date_pattern = re.compile(r"datetime\.date\(\s*(\d{4})\s*,\s*(\d{1,2})\s*,\s*(\d{1,2})\s*\)")

                        def _replace_date(match):
                            y, m, d = map(int, match.groups())
                            return f"'{y:04d}-{m:02d}-{d:02d}'"

                        cleaned_result = date_pattern.sub(_replace_date, result)

                        # literal_eval 로 안전하게 파싱 (예: [("2025-02-14", 8), (...), ...])
                        tuple_list = ast.literal_eval(cleaned_result)
                        logger.info(f"literal_eval 성공, 추출된 튜플 개수: {len(tuple_list)}")

                        if not tuple_list:
                            logger.warning("추출된 데이터가 없습니다. 빈 DataFrame 반환")
                            return True, pd.DataFrame()

                        # DataFrame 생성용 데이터 준비
                        data = tuple_list

                        # 컬럼 수 파악
                        col_count = len(data[0])

                        # --- 컬럼 이름 추출 로직 개선 ---
                        sql_query = query if query else ""  # 대소문자 구분을 위해 소문자 변환 안함
                        select_pattern = r'(?i)select\s+(.+?)\s+from'
                        select_match = re.search(select_pattern, sql_query, re.DOTALL)
                        extracted_columns = []
                        if select_match:
                            select_clause = select_match.group(1).strip()
                            # 콤마로 select 절을 분리하되, 함수 인자의 콤마는 제외
                            parts = []
                            current_part = ""
                            paren_level = 0
                            
                            # 함수 내부의 콤마와 일반 콤마 구분
                            for char in select_clause:
                                if char == '(':
                                    paren_level += 1
                                    current_part += char
                                elif char == ')':
                                    paren_level -= 1
                                    current_part += char
                                elif char == ',' and paren_level == 0:
                                    parts.append(current_part.strip())
                                    current_part = ""
                                else:
                                    current_part += char
                            
                            # 마지막 부분 추가
                            if current_part.strip():
                                parts.append(current_part.strip())
                            
                            logger.info(f"추출된 SELECT 절 부분: {parts}")
                            
                            for part in parts:
                                # AS 또는 as로 지정된 별칭 찾기 (대소문자 구분 없이)
                                as_match = re.search(r'(?i)\s+as\s+[\'"`]?([a-zA-Z0-9_]+)[\'"`]?', part)
                                if as_match:
                                    extracted_columns.append(as_match.group(1))
                                    continue
                                
                                # 별칭 없이 테이블.컬럼 형태인 경우
                                dot_match = re.search(r'([a-zA-Z0-9_]+)\.([a-zA-Z0-9_`]+)\s*$', part)
                                if dot_match:
                                    # 백틱 제거
                                    col_name = dot_match.group(2).replace('`', '')
                                    extracted_columns.append(col_name)
                                    continue
                                
                                # COUNT, SUM 등 함수인 경우
                                func_match = re.search(r'(?i)([a-z0-9_]+)\s*\(', part)
                                if func_match:
                                    func_name = func_match.group(1).lower()
                                    if func_name in ['count', 'sum', 'avg', 'min', 'max']:
                                        as_alias = re.search(r'(?i)\s+as\s+([a-zA-Z0-9_]+)', part)
                                        if as_alias:
                                            # AS 별칭이 있으면 사용
                                            extracted_columns.append(as_alias.group(1))
                                        else:
                                            # 없으면 함수명 사용
                                            extracted_columns.append(func_name)
                                    else:
                                        # 일반 함수면 기본명 사용
                                        extracted_columns.append(f'col{len(extracted_columns)+1}')
                                    continue
                                
                                # 단순 컬럼명인 경우
                                simple_col = re.search(r'([a-zA-Z0-9_`]+)\s*$', part)
                                if simple_col:
                                    # 백틱 제거
                                    col_name = simple_col.group(1).replace('`', '')
                                    extracted_columns.append(col_name)
                                    continue
                                
                                # 위 경우가 모두 아니면 기본명 사용
                                extracted_columns.append(f'col{len(extracted_columns)+1}')

                        logger.info(f"SQL에서 추출한 컬럼 이름: {extracted_columns}")
                        
                        # 결과 튜플에서 데이터를 추출하고 컬럼 순서 확인
                        # 데이터 타입을 기반으로 컬럼 순서 검증 및 조정
                        if tuple_list and len(tuple_list) > 0 and len(tuple_list[0]) == len(extracted_columns):
                            # 실제 데이터 샘플 가져오기
                            sample_data = tuple_list[0]
                            
                            # 컬럼 순서 매핑 리스트 초기화 (AS 별칭과 실제 데이터 타입 매핑)
                            col_type_map = []
                            
                            # 실제 데이터의 타입 파악
                            for i, value in enumerate(sample_data):
                                if isinstance(value, str):
                                    col_type_map.append(("string", i))
                                elif isinstance(value, (int, float)):
                                    col_type_map.append(("numeric", i))
                                elif isinstance(value, (list, tuple, dict)):
                                    col_type_map.append(("collection", i))
                                else:
                                    col_type_map.append(("other", i))
                            
                            logger.info(f"데이터 타입 분석: {col_type_map}")
                            
                            # 각 컬럼명에서 힌트 추출 (COUNT, SUM 등의 함수명이나 특정 패턴)
                            col_name_hints = []
                            for col_name in extracted_columns:
                                if col_name.lower() in ('count', 'sum', 'avg', 'min', 'max', 'cnt', 'total'):
                                    col_name_hints.append(("numeric", col_name))
                                elif any(term in col_name.lower() for term in ('id', 'num', 'qty', 'quantity', 'amount')):
                                    col_name_hints.append(("numeric", col_name))
                                elif any(term in col_name.lower() for term in ('name', 'title', 'desc', 'description', 'text')):
                                    col_name_hints.append(("string", col_name))
                                elif any(term in col_name.lower() for term in ('date', 'time', 'day', 'month', 'year')):
                                    col_name_hints.append(("string", col_name))
                                else:
                                    col_name_hints.append(("unknown", col_name))
                            
                            logger.info(f"컬럼명 힌트 분석: {col_name_hints}")
                            
                            # 타입 불일치 확인 및 조정
                            mismatch_found = False
                            for i, (type_hint, col_name) in enumerate(col_name_hints):
                                if type_hint != "unknown" and i < len(col_type_map):
                                    actual_type, actual_idx = col_type_map[i]
                                    if type_hint != actual_type:
                                        logger.info(f"컬럼 타입 불일치 감지: {col_name}은(는) {type_hint} 타입이어야 하는데 {actual_type} 타입임")
                                        mismatch_found = True
                                        break
                            
                            # 불일치가 있으면 컬럼 순서 재조정 시도
                            if mismatch_found:
                                logger.info("컬럼 순서 불일치 감지. 데이터 타입에 맞게 컬럼 순서 재조정 시도")
                                
                                # 새로운 컬럼 순서 초기화
                                new_column_order = [None] * len(extracted_columns)
                                
                                # 컬럼 타입 기반으로 최적의 매칭 찾기
                                for i, (type_hint, col_name) in enumerate(col_name_hints):
                                    if type_hint == "unknown":
                                        # 힌트가 없으면 원래 위치 유지
                                        new_column_order[i] = col_name
                                        continue
                                    
                                    # 해당 타입의 실제 데이터 인덱스 찾기
                                    for actual_type, actual_idx in col_type_map:
                                        if actual_type == type_hint and new_column_order[actual_idx] is None:
                                            # 매칭되는 위치에 컬럼명 할당
                                            new_column_order[actual_idx] = col_name
                                            break
                                
                                # 아직 할당되지 않은 컬럼 처리
                                for i, col in enumerate(new_column_order):
                                    if col is None:
                                        # 할당되지 않은 컬럼명 찾기
                                        for j, col_name in enumerate(extracted_columns):
                                            if col_name not in new_column_order:
                                                new_column_order[i] = col_name
                                                break
                                
                                # 모든 컬럼이 할당되었는지 확인
                                if None not in new_column_order and sorted(new_column_order) == sorted(extracted_columns):
                                    logger.info(f"컬럼 순서 재조정 성공: {new_column_order}")
                                    extracted_columns = new_column_order
                                else:
                                    # 재조정 실패 시 원래 순서 유지
                                    logger.info("컬럼 순서 재조정 실패. 원래 순서 유지")
                            else:
                                logger.info("컬럼 타입과 순서가 일치함. 조정 불필요")

                        # 추출된 컬럼이 모자라면 기본명으로 보완
                        if len(extracted_columns) < col_count:
                            for i in range(len(extracted_columns), col_count):
                                extracted_columns.append(f'col{i+1}')
                        elif len(extracted_columns) > col_count:
                            # 추출된 컬럼이 너무 많으면 필요한 만큼만 사용
                            extracted_columns = extracted_columns[:col_count]

                        df = pd.DataFrame(data, columns=extracted_columns)
                        logger.info(f"SQL 쿼리 결과를 DataFrame으로 변환 성공: 행={len(df)}, 열={df.columns.tolist()}")
                        if hasattr(df, 'empty') and df.empty:
                            logger.warning("DataFrame이 비어있습니다. 데이터 확인 필요")
                        else:
                            logger.info(f"DataFrame 처음 5개 행:\n{df.head()}")
                        return True, df
                    except Exception as tuple_error:
                        logger.info(f"튜플 형태 파싱 실패: {tuple_error}")
                        # (기존 정규표현식 파싱 방식으로 폴백)
                        # 문자열에서 튜플 형태를 추출
                        import re
                        pattern = r'\(([^)]+)\)'
                        matches = re.findall(pattern, result)
                        logger.info(f"추출된 튜플 개수: {len(matches)}")
                        if matches:
                            logger.info(f"처음 추출된 튜플 예시: {matches[0]}")

                        if matches:
                            # 추출된 튜플을 처리
                            data = []
                            for match in matches:
                                # 콤마로 구분된 값들을 분리
                                # 콤마가 있는 경우와 없는 경우 모두 처리
                                if ',' in match:
                                    # 콤마로 구분된 값들을 분리
                                    values = [val.strip() for val in match.split(',')]
                                    # 마지막 값이 비어있는 경우 제거 (단일 값 튜플의 경우 콤마가 뒤에 있을 수 있음)
                                    if values and values[-1] == '':
                                        values = values[:-1]
                                else:
                                    # 콤마가 없는 경우 공백으로 구분
                                    values = [val.strip() for val in match.split()]
                                data.append(values)

                            # 컬럼 이름 추측 (의미 있는 이름으로 설정 시도)
                            if data:
                                col_count = len(data[0])

                                # SQL 쿼리에서 컬럼 이름 추출 시도
                                import re
                                sql_query = query.lower() if query else ""

                                # SELECT 절에서 컬럼 이름 추출 시도
                                # 1. SELECT 절 추출
                                select_pattern = r'select\s+(.+?)\s+from'
                                select_match = re.search(select_pattern, sql_query, re.DOTALL)

                                extracted_columns = []
                                if select_match:
                                    # 2. SELECT 절에서 컬럼 분리
                                    select_clause = select_match.group(1).strip()
                                    # 3. 콤마로 구분된 컬럼 분리
                                    column_parts = [p.strip() for p in select_clause.split(',')]

                                    # 4. 각 컬럼에서 별칭 추출
                                    for part in column_parts:
                                        # AS를 통해 지정된 별칭 추출
                                        as_match = re.search(r'\s+as\s+([\w_]+)\s*$', part, re.IGNORECASE)
                                        if as_match:
                                            extracted_columns.append(as_match.group(1))
                                        else:
                                            # AS 없이 지정된 경우 (마지막 부분을 컬럼으로 간주)
                                            # 테이블 이름이 있는 경우 (table.column)
                                            dot_match = re.search(r'([\w_]+)\.([\w_]+)\s*$', part)
                                            if dot_match:
                                                extracted_columns.append(dot_match.group(2))
                                            else:
                                                # 함수를 사용한 경우 (COUNT(column) 등)
                                                func_match = re.search(r'([\w_]+)\s*\([^)]*\)\s*$', part, re.IGNORECASE)
                                                if func_match:
                                                    func_name = func_match.group(1).lower()
                                                    if func_name in ['count', 'sum', 'avg', 'min', 'max']:
                                                        extracted_columns.append(func_name)
                                                    else:
                                                        # 기본 이름 사용
                                                        extracted_columns.append(f'col{len(extracted_columns)+1}')
                                                else:
                                                    # 단순 컬럼 이름
                                                    simple_match = re.search(r'([\w_]+)\s*$', part)
                                                    if simple_match:
                                                        extracted_columns.append(simple_match.group(1))
                                                    else:
                                                        # 기본 이름 사용
                                                        extracted_columns.append(f'col{len(extracted_columns)+1}')

                                # 추출된 컬럼 이름이 충분한지 확인
                                if len(extracted_columns) >= col_count:
                                    columns = extracted_columns[:col_count]
                                    print(f"SQL에서 추출한 컬럼 이름: {columns}")
                                else:
                                    # 기존 방식으로 추출 시도
                                    # AS를 통해 지정된 컬럼 이름 추출
                                    column_aliases = re.findall(r'\s+as\s+([\w_]+)', sql_query)

                                    if len(column_aliases) >= col_count:
                                        columns = column_aliases[:col_count]
                                        print(f"AS를 통해 추출한 컬럼 이름: {columns}")
                                    else:
                                        # 기본 이름 사용
                                        if col_count == 1:
                                            # 단일 값 쿼리인 경우
                                            # 카운트 관련 쿼리인지 확인
                                            count_patterns = ["count", "delivery_count", "total", "sum", "number"]
                                            count_column_name = None

                                            # SQL 쿼리에서 카운트 관련 이름 찾기
                                            for pattern in count_patterns:
                                                if pattern in sql_query.lower():
                                                    # AS를 통해 지정된 이름 찾기
                                                    as_match = re.search(f"\\b{pattern}[\\w_]*\\s+as\\s+([\\w_]+)", sql_query.lower())
                                                    if as_match:
                                                        count_column_name = as_match.group(1)
                                                    else:
                                                        count_column_name = pattern
                                                    break

                                            if count_column_name:
                                                columns = [count_column_name]
                                                print(f"카운트 쿼리 추측, 컬럼 이름: {columns}")
                                            else:
                                                # 카운트 관련 이름을 찾지 못한 경우 기본 이름 사용
                                                columns = ['count']
                                                print(f"기본 카운트 컬럼 이름 사용: {columns}")
                                        elif col_count == 2 and "month" in sql_query and "count" in sql_query:
                                            # 월별 집계 쿼리인 경우
                                            columns = ['month', 'count']
                                            print(f"월별 집계 쿼리 추측, 컬럼 이름: {columns}")
                                        else:
                                            # 기본 이름 사용
                                            columns = [f'col{i+1}' for i in range(col_count)]
                                            print(f"기본 컬럼 이름 사용: {columns}")

                                # DataFrame 생성
                                df = pd.DataFrame(data, columns=columns)
                                logger.info(f"SQL 쿼리 결과를 DataFrame으로 변환 성공: 행={len(df)}, 열={df.columns.tolist()}")

                                # 추가 검증: DataFrame이 비어있는지 확인
                                if hasattr(df, 'empty') and df.empty:
                                    logger.warning("DataFrame이 비어있습니다. 데이터 확인 필요")
                                else:
                                    logger.info(f"DataFrame 처음 5개 행:\n{df.head()}")

                                return True, df
                    except Exception as tuple_error:
                        logger.info(f"튜플 형태 파싱 실패: {tuple_error}")

                # 기본 CSV 형식 시도
                import io
                df = pd.read_csv(io.StringIO(result))
                logger.info(f"SQL 쿼리 결과를 DataFrame으로 변환 성공: 행={len(df)}, 열={df.columns.tolist()}")
                return True, df
            except Exception as csv_error:
                # 변환 실패 시 원본 문자열 반환
                logger.info(f"SQL 쿼리 결과를 DataFrame으로 변환 실패: {csv_error}")
                return True, result
        else:
            # 결과가 이미 DataFrame인 경우
            if isinstance(result, pd.DataFrame):
                logger.info(f"SQL 쿼리 결과(DataFrame): 행={len(result)}, 열={result.columns.tolist()}")
            else:
                logger.info(f"SQL 쿼리 결과(기타 타입): {type(result)}")
            return True, result
    except Exception as e:
        error_msg = f"SQL 쿼리 실행 오류: {e}"
        logger.error(f"{error_msg}\n쿼리: {query}")
        
        # 특정 오류 타입에 대해 사용자 친화적인 메시지 제공
        error_str = str(e).lower()
        if "doesn't exist" in error_str or "unknown table" in error_str:
            # 테이블이 존재하지 않는 경우
            if "rider" in error_str and "rider" in query.lower():
                user_friendly_msg = ("테이블 'rider'가 존재하지 않습니다. "
                                    "기사 정보는 'rider_info' 테이블을 사용해주세요. "
                                    "쿼리에서 'rider' 테이블 대신 'rider_info' 테이블을 사용하거나, "
                                    "GROUP BY절에서 실제 JOIN된 테이블의 컬럼을 사용해주세요.")
                logger.info(f"테이블명 수정 제안: {user_friendly_msg}")
                return False, user_friendly_msg
            else:
                table_match = re.search(r"table '([^']+)' doesn't exist", error_str)
                if table_match:
                    table_name = table_match.group(1)
                    user_friendly_msg = f"테이블 '{table_name}'이 존재하지 않습니다. 데이터베이스 스키마를 확인해주세요."
                    return False, user_friendly_msg
        elif "unknown column" in error_str or "unknown field" in error_str:
            # 컬럼이 존재하지 않는 경우
            user_friendly_msg = f"존재하지 않는 컬럼을 참조했습니다. 데이터베이스 스키마를 확인해주세요. 오류: {str(e)}"
            return False, user_friendly_msg
        elif "syntax error" in error_str:
            # SQL 문법 오류
            user_friendly_msg = f"SQL 문법 오류가 발생했습니다. 쿼리를 다시 확인해주세요. 오류: {str(e)}"
            return False, user_friendly_msg
        
        return False, error_msg

def check_sql_query(db: SQLDatabase, query: str) -> Tuple[bool, str]:
    """
    SQL 쿼리의 유효성을 검사합니다.

    Args:
        db: LangChain SQLDatabase 객체
        query: 검사할 SQL 쿼리

    Returns:
        유효성 여부와 결과 메시지
    """
    try:
        # 쿼리가 SELECT로 시작하는지 확인
        if not query.strip().upper().startswith("SELECT"):
            return False, "SELECT 쿼리만 허용됩니다."

        # 쿼리 실행 시도 (실제로 실행하지 않고 구문 분석만)
        engine = db._engine
        with engine.connect() as conn:
            # 트랜잭션 시작 (실제 변경 없음)
            with conn.begin():
                # 쿼리 구문 분석
                conn.execute(text(f"EXPLAIN {query}"))

        return True, "쿼리가 유효합니다."
    except Exception as e:
        error_msg = f"SQL 쿼리 검증 오류: {e}"
        logger.error(f"{error_msg}\n쿼리: {query}")
        return False, error_msg