"""
DynamoDB 데이터 모델

이 모듈은 채팅 히스토리를 위한 DynamoDB 데이터 모델을 정의합니다.
Pydantic을 사용하여 데이터 검증 및 직렬화를 처리합니다.
"""

from datetime import datetime, timezone
from typing import Optional, Dict, Any, List, Union
from pydantic import BaseModel, Field, field_validator, model_validator
from enum import Enum
import uuid
import json


class MessageType(str, Enum):
    """메시지 타입 열거형"""
    USER = "user"
    AI = "ai"
    SYSTEM = "system"
    ERROR = "error"


class SessionStatus(str, Enum):
    """세션 상태 열거형"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    EXPIRED = "expired"
    ARCHIVED = "archived"


class WorkflowState(BaseModel):
    """LangGraph 워크플로우 상태 모델"""
    current_node: Optional[str] = None
    completed_nodes: List[str] = Field(default_factory=list)
    context: Dict[str, Any] = Field(default_factory=dict)
    sql_query: Optional[str] = None
    query_results: Optional[List[Dict[str, Any]]] = None
    analysis_results: Optional[str] = None
    error_message: Optional[str] = None
    
    class Config:
        extra = "allow"  # 추가 필드 허용


class ChatSession(BaseModel):
    """채팅 세션 모델"""
    server_id: str = Field(..., description="서버 고유 식별자")
    session_id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="세션 고유 식별자")
    title: Optional[str] = Field(None, description="세션 제목")
    status: SessionStatus = Field(SessionStatus.ACTIVE, description="세션 상태")
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    last_activity_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    expires_at: Optional[datetime] = Field(None, description="세션 만료 시간")
    message_count: int = Field(0, description="메시지 수")
    workflow_state: Optional[WorkflowState] = Field(None, description="워크플로우 상태")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="추가 메타데이터")
    

    
    @model_validator(mode='before')
    @classmethod
    def generate_title(cls, values):
        """제목이 없으면 자동 생성"""
        if isinstance(values, dict) and not values.get('title'):
            created_at = values.get('created_at', datetime.now(timezone.utc))
            if isinstance(created_at, str):
                try:
                    created_at = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                except:
                    created_at = datetime.now(timezone.utc)
            values['title'] = f"Chat Session {created_at.strftime('%Y-%m-%d %H:%M')}"
        return values
    
    def to_dynamodb_item(self) -> Dict[str, Any]:
        """DynamoDB 아이템으로 변환"""
        item = {
            'server_id': self.server_id,
            'session_id': self.session_id,
            'title': self.title,
            'status': self.status.value,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'last_activity_at': self.last_activity_at.isoformat(),
            'message_count': self.message_count,
            'metadata': json.dumps(self.metadata) if self.metadata else '{}'
        }
        
        if self.expires_at:
            item['expires_at'] = self.expires_at.isoformat()
            
        if self.workflow_state:
            item['workflow_state'] = self.workflow_state.json()
            
        return item
    
    @classmethod
    def from_dynamodb_item(cls, item: Dict[str, Any]) -> 'ChatSession':
        """DynamoDB 아이템에서 생성"""
        # 날짜 문자열을 datetime 객체로 변환
        created_at = datetime.fromisoformat(item['created_at'].replace('Z', '+00:00'))
        updated_at = datetime.fromisoformat(item['updated_at'].replace('Z', '+00:00'))
        last_activity_at = datetime.fromisoformat(item['last_activity_at'].replace('Z', '+00:00'))
        
        expires_at = None
        if 'expires_at' in item and item['expires_at']:
            expires_at = datetime.fromisoformat(item['expires_at'].replace('Z', '+00:00'))
        
        workflow_state = None
        if 'workflow_state' in item and item['workflow_state']:
            workflow_state = WorkflowState.parse_raw(item['workflow_state'])
        
        metadata = {}
        if 'metadata' in item and item['metadata']:
            try:
                metadata = json.loads(item['metadata'])
            except json.JSONDecodeError:
                metadata = {}
        
        return cls(
            server_id=item['server_id'],
            session_id=item['session_id'],
            title=item.get('title'),
            status=SessionStatus(item.get('status', SessionStatus.ACTIVE.value)),
            created_at=created_at,
            updated_at=updated_at,
            last_activity_at=last_activity_at,
            expires_at=expires_at,
            message_count=item.get('message_count', 0),
            workflow_state=workflow_state,
            metadata=metadata
        )


class ChatMessage(BaseModel):
    """채팅 메시지 모델"""
    session_key: str = Field(..., description="세션 키 (server_id#session_id)")
    message_id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="메시지 고유 식별자")
    timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    message_type: MessageType = Field(..., description="메시지 타입")
    content: str = Field(..., description="메시지 내용")
    sender: str = Field(..., description="발신자 (user, ai, system)")
    parent_message_id: Optional[str] = Field(None, description="부모 메시지 ID")
    workflow_node: Optional[str] = Field(None, description="워크플로우 노드")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="추가 메타데이터")
    is_compressed: bool = Field(False, description="내용 압축 여부")
    compressed_content: Optional[str] = Field(None, description="압축된 내용")
    

    
    @field_validator('session_key')
    @classmethod
    def validate_session_key(cls, v):
        """세션 키 형식 검증"""
        if '#' not in v:
            raise ValueError("session_key must be in format 'server_id#session_id'")
        return v
    
    def get_server_id(self) -> str:
        """세션 키에서 서버 ID 추출"""
        return self.session_key.split('#')[0]
    
    def get_session_id(self) -> str:
        """세션 키에서 세션 ID 추출"""
        return self.session_key.split('#')[1]
    
    def to_dynamodb_item(self) -> Dict[str, Any]:
        """DynamoDB 아이템으로 변환"""
        item = {
            'session_key': self.session_key,
            'message_id': self.message_id,
            'timestamp': self.timestamp.isoformat(),
            'message_type': self.message_type.value,
            'content': self.content,
            'sender': self.sender,
            'is_compressed': self.is_compressed,
            'metadata': json.dumps(self.metadata) if self.metadata else '{}'
        }
        
        if self.parent_message_id:
            item['parent_message_id'] = self.parent_message_id
            
        if self.workflow_node:
            item['workflow_node'] = self.workflow_node
            
        if self.compressed_content:
            item['compressed_content'] = self.compressed_content
            
        return item
    
    @classmethod
    def from_dynamodb_item(cls, item: Dict[str, Any]) -> 'ChatMessage':
        """DynamoDB 아이템에서 생성"""
        timestamp = datetime.fromisoformat(item['timestamp'].replace('Z', '+00:00'))
        
        metadata = {}
        if 'metadata' in item and item['metadata']:
            try:
                metadata = json.loads(item['metadata'])
            except json.JSONDecodeError:
                metadata = {}
        
        return cls(
            session_key=item['session_key'],
            message_id=item['message_id'],
            timestamp=timestamp,
            message_type=MessageType(item['message_type']),
            content=item['content'],
            sender=item['sender'],
            parent_message_id=item.get('parent_message_id'),
            workflow_node=item.get('workflow_node'),
            metadata=metadata,
            is_compressed=item.get('is_compressed', False),
            compressed_content=item.get('compressed_content')
        )


class SessionSummary(BaseModel):
    """세션 요약 모델 (목록 표시용)"""
    server_id: str
    session_id: str
    title: str
    status: SessionStatus
    created_at: datetime
    last_activity_at: datetime
    message_count: int
    
    @classmethod
    def from_session(cls, session: ChatSession) -> 'SessionSummary':
        """ChatSession에서 요약 생성"""
        return cls(
            server_id=session.server_id,
            session_id=session.session_id,
            title=session.title,
            status=session.status,
            created_at=session.created_at,
            last_activity_at=session.last_activity_at,
            message_count=session.message_count
        )


def create_session_key(server_id: str, session_id: str) -> str:
    """세션 키 생성 헬퍼 함수"""
    return f"{server_id}#{session_id}"


def parse_session_key(session_key: str) -> tuple[str, str]:
    """세션 키 파싱 헬퍼 함수"""
    if '#' not in session_key:
        raise ValueError("Invalid session_key format")
    
    parts = session_key.split('#', 1)
    return parts[0], parts[1]


# 데이터 압축 관련 유틸리티
def compress_content(content: str, threshold: int = 1000) -> tuple[str, bool]:
    """
    내용이 임계값을 초과하면 압축합니다.
    
    Args:
        content: 압축할 내용
        threshold: 압축 임계값 (바이트)
        
    Returns:
        tuple: (압축된 내용 또는 원본, 압축 여부)
    """
    if len(content.encode('utf-8')) <= threshold:
        return content, False
    
    try:
        import gzip
        import base64
        
        compressed = gzip.compress(content.encode('utf-8'))
        encoded = base64.b64encode(compressed).decode('ascii')
        return encoded, True
    except ImportError:
        # gzip을 사용할 수 없으면 원본 반환
        return content, False


def decompress_content(content: str, is_compressed: bool) -> str:
    """
    압축된 내용을 해제합니다.
    
    Args:
        content: 압축된 내용 또는 원본
        is_compressed: 압축 여부
        
    Returns:
        str: 해제된 내용
    """
    if not is_compressed:
        return content
    
    try:
        import gzip
        import base64
        
        decoded = base64.b64decode(content.encode('ascii'))
        decompressed = gzip.decompress(decoded)
        return decompressed.decode('utf-8')
    except (ImportError, Exception):
        # 해제 실패 시 원본 반환
        return content


if __name__ == "__main__":
    # 테스트 코드
    print("=== DynamoDB Models Test ===")
    
    # 세션 생성 테스트
    session = ChatSession(
        server_id="test_server_123",
        title="Test Chat Session"
    )
    print(f"생성된 세션: {session.session_id}")
    print(f"세션 제목: {session.title}")
    
    # DynamoDB 아이템 변환 테스트
    item = session.to_dynamodb_item()
    print(f"DynamoDB 아이템: {item}")
    
    # 역변환 테스트
    restored_session = ChatSession.from_dynamodb_item(item)
    print(f"복원된 세션: {restored_session.session_id}")
    
    # 메시지 생성 테스트
    session_key = create_session_key(session.server_id, session.session_id)
    message = ChatMessage(
        session_key=session_key,
        message_type=MessageType.USER,
        content="안녕하세요! 테스트 메시지입니다.",
        sender="user"
    )
    print(f"생성된 메시지: {message.message_id}")
    print(f"메시지 내용: {message.content}")
    
    # 메시지 DynamoDB 아이템 변환 테스트
    message_item = message.to_dynamodb_item()
    print(f"메시지 DynamoDB 아이템: {message_item}")
    
    # 메시지 역변환 테스트
    restored_message = ChatMessage.from_dynamodb_item(message_item)
    print(f"복원된 메시지: {restored_message.message_id}")
    
    # 압축 테스트
    long_content = "이것은 매우 긴 메시지입니다. " * 100
    compressed, is_compressed = compress_content(long_content, threshold=50)
    print(f"압축 여부: {is_compressed}")
    print(f"압축 전 길이: {len(long_content)}, 압축 후 길이: {len(compressed)}")
    
    decompressed = decompress_content(compressed, is_compressed)
    print(f"압축 해제 성공: {decompressed == long_content}") 