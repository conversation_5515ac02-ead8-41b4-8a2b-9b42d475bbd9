"""
메시지 히스토리 관리 서비스

이 모듈은 채팅 메시지의 저장, 조회, 관리를 전담하는 고급 서비스를 구현합니다.
메시지 타입별 처리, 검색, 압축, 아카이빙 등의 기능을 제공합니다.
"""

import logging
from typing import Dict, Any, List, Optional, Tuple, Union
from datetime import datetime, timezone, timedelta
import json
import re
from collections import defaultdict

try:
    from .dynamodb_crud import get_dynamodb_crud, DynamoDBCRUD
    from .dynamodb_models import (
        ChatMessage, MessageType, create_session_key, parse_session_key,
        compress_content, decompress_content
    )
    from .server_id_manager import get_server_id
    from .state_utils import (
        serialize_graph_state, deserialize_graph_state, 
        extract_key_info_from_state, validate_graph_state
    )
    from ..states.graph_state import GraphState
except ImportError:
    # 직접 실행 시 절대 import 사용
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
    from modules.utils.dynamodb_crud import get_dynamodb_crud, DynamoDBCRUD
    from modules.utils.dynamodb_models import (
        ChatMessage, MessageType, create_session_key, parse_session_key,
        compress_content, decompress_content
    )
    from modules.utils.server_id_manager import get_server_id
    from modules.utils.state_utils import (
        serialize_graph_state, deserialize_graph_state, 
        extract_key_info_from_state, validate_graph_state
    )
    from modules.states.graph_state import GraphState

logger = logging.getLogger(__name__)


class MessageHistoryManager:
    """메시지 히스토리 관리 클래스"""
    
    def __init__(self):
        self.crud = get_dynamodb_crud()
        self.server_id = get_server_id()
        self._message_cache: Dict[str, ChatMessage] = {}
        self._search_cache: Dict[str, List[ChatMessage]] = {}
        self._state_cache: Dict[str, GraphState] = {}  # GraphState 캐시 추가
    
    # ========== 메시지 저장 및 관리 ==========
    
    def save_message(self, session_id: str, message_type: MessageType, content: str, 
                    sender: str, metadata: Optional[Dict[str, Any]] = None,
                    graph_state: Optional[GraphState] = None,
                    auto_compress: bool = True) -> Optional[ChatMessage]:
        """
        메시지를 저장합니다.
        
        Args:
            session_id: 세션 ID
            message_type: 메시지 타입
            content: 메시지 내용
            sender: 발신자
            metadata: 추가 메타데이터
            graph_state: LangGraph 워크플로우 상태 (선택적)
            auto_compress: 자동 압축 여부
            
        Returns:
            ChatMessage: 저장된 메시지 객체 또는 None
        """
        try:
            # 세션 키 생성
            session_key = create_session_key(self.server_id, session_id)
            
            # 메타데이터 처리
            processed_metadata = self._process_message_metadata(
                message_type, content, sender, metadata or {}, graph_state
            )
            
            # GraphState 직렬화 및 메타데이터에 포함
            if graph_state and validate_graph_state(graph_state):
                try:
                    serialized_state = serialize_graph_state(graph_state)
                    processed_metadata['graph_state'] = serialized_state
                    processed_metadata['has_graph_state'] = True
                    
                    # GraphState 주요 정보 추출
                    state_info = extract_key_info_from_state(graph_state)
                    processed_metadata['state_info'] = state_info
                    
                    logger.info(f"GraphState 저장 완료: {len(serialized_state)} bytes")
                except Exception as e:
                    logger.error(f"GraphState 직렬화 실패: {e}")
                    processed_metadata['graph_state_error'] = str(e)
            
            # 내용 압축 처리
            processed_content = content
            if auto_compress and len(content) > 1024:  # 1KB 이상이면 압축
                try:
                    compressed = compress_content(content)
                    if len(compressed) < len(content):
                        processed_content = compressed
                        processed_metadata['compressed'] = True
                        logger.info(f"메시지 압축: {len(content)} -> {len(compressed)} bytes")
                except Exception as e:
                    logger.warning(f"메시지 압축 실패: {e}")
            
            # 메시지 객체 생성
            message = ChatMessage(
                session_key=session_key,
                message_type=message_type,
                content=processed_content,
                sender=sender,
                metadata=processed_metadata
            )
            
            # DynamoDB에 저장
            if self.crud.create_message(message):
                # 캐시에 저장
                self._message_cache[message.message_id] = message
                
                # GraphState 캐시 저장
                if graph_state and validate_graph_state(graph_state):
                    self._state_cache[message.message_id] = graph_state
                
                logger.info(f"메시지 저장 완료: {message.message_id} ({message_type.value})")
                if processed_metadata.get('has_graph_state'):
                    logger.info(f"GraphState도 함께 저장됨: {message.message_id}")
                
                return message
            else:
                logger.error("메시지 저장 실패")
                return None
                
        except Exception as e:
            logger.error(f"메시지 저장 중 오류: {e}")
            return None
    
    def get_message_with_state(self, message_id: str, use_cache: bool = True) -> Tuple[Optional[ChatMessage], Optional[GraphState]]:
        """
        메시지와 GraphState를 함께 조회합니다.
        
        Args:
            message_id: 메시지 ID
            use_cache: 캐시 사용 여부
            
        Returns:
            Tuple[ChatMessage, GraphState]: 메시지와 상태 객체
        """
        try:
            # 메시지 조회
            message = self.get_message(message_id, use_cache)
            if not message:
                return None, None
            
            # GraphState 조회
            graph_state = None
            
            # 캐시에서 먼저 확인
            if use_cache and message_id in self._state_cache:
                graph_state = self._state_cache[message_id]
                logger.debug(f"캐시에서 GraphState 로드: {message_id}")
            elif message.metadata and message.metadata.get('has_graph_state'):
                # 메타데이터에서 GraphState 복원
                try:
                    serialized_state = message.metadata.get('graph_state')
                    if serialized_state:
                        graph_state = deserialize_graph_state(serialized_state)
                        
                        # 캐시에 저장
                        if use_cache:
                            self._state_cache[message_id] = graph_state
                        
                        logger.debug(f"메타데이터에서 GraphState 복원: {message_id}")
                except Exception as e:
                    logger.error(f"GraphState 역직렬화 실패: {e}")
            
            return message, graph_state
            
        except Exception as e:
            logger.error(f"메시지/상태 조회 중 오류: {e}")
            return None, None
    
    def get_session_messages_with_states(
        self,
        session_id: str,
        limit: int = 50,
        start_key: Optional[str] = None,
        ascending: bool = False
    ) -> Tuple[List[Tuple[ChatMessage, Optional[dict]]], Optional[str]]:
        """
        세션의 메시지와 해당하는 GraphState를 함께 조회합니다.
        
        Args:
            session_id: 세션 ID
            limit: 조회할 메시지 수 제한 (기본값: 50)
            start_key: 페이지네이션을 위한 시작 키
            ascending: True면 오래된 메시지부터, False면 최신 메시지부터 정렬
            
        Returns:
            Tuple[List[Tuple[ChatMessage, Optional[dict]]], Optional[str]]:
                (메시지-GraphState 쌍 리스트, 다음 페이지 키)
        """
        try:
            # 먼저 메시지 목록 조회
            messages, next_key = self.get_session_messages(
                session_id=session_id,
                limit=limit,
                last_evaluated_key=start_key,
                ascending=ascending
            )
            
            message_state_pairs = []
            
            for message in messages:
                try:
                    # 메시지의 메타데이터에서 GraphState 추출
                    graph_state = None
                    if message.metadata and message.metadata.get('has_graph_state'):
                        try:
                            serialized_state = message.metadata.get('graph_state')
                            if serialized_state:
                                graph_state = deserialize_graph_state(serialized_state)
                        except Exception as deserialize_error:
                            logger.warning(f"GraphState 역직렬화 실패 (메시지 ID: {getattr(message, 'message_id', 'unknown')}): {deserialize_error}")
                    
                    message_state_pairs.append((message, graph_state))
                    
                except Exception as state_error:
                    logger.warning(f"GraphState 추출 실패 (메시지 ID: {getattr(message, 'message_id', 'unknown')}): {state_error}")
                    # GraphState 없이도 메시지는 포함
                    message_state_pairs.append((message, None))
            
            logger.info(f"메시지-상태 쌍 조회 완료: {len(message_state_pairs)}개 (세션: {session_id})")
            return message_state_pairs, next_key
            
        except Exception as e:
            logger.error(f"메시지-상태 쌍 조회 실패 (세션: {session_id}): {e}")
            return [], None
    
    def restore_conversation_state(self, session_id: str) -> Optional[GraphState]:
        """
        대화의 최신 GraphState를 복원합니다.
        
        Args:
            session_id: 세션 ID
            
        Returns:
            GraphState: 복원된 상태 객체 또는 None
        """
        try:
            # 최신 메시지들을 역순으로 조회
            messages, _ = self.get_session_messages(session_id, limit=10, ascending=False)
            
            # 가장 최근의 GraphState를 가진 메시지 찾기
            for message in messages:
                if message.metadata and message.metadata.get('has_graph_state'):
                    _, graph_state = self.get_message_with_state(message.message_id)
                    if graph_state:
                        logger.info(f"대화 상태 복원 성공: {session_id} (메시지: {message.message_id})")
                        return graph_state
            
            logger.warning(f"복원할 GraphState가 없습니다: {session_id}")
            return None
            
        except Exception as e:
            logger.error(f"대화 상태 복원 중 오류: {e}")
            return None
    
    def _process_message_metadata(self, message_type: MessageType, content: str, 
                                 sender: str, metadata: Dict[str, Any],
                                 graph_state: Optional[GraphState] = None) -> Dict[str, Any]:
        """
        메시지 메타데이터를 처리합니다.
        
        Args:
            message_type: 메시지 타입
            content: 메시지 내용
            sender: 발신자
            metadata: 기존 메타데이터
            graph_state: GraphState 객체 (선택적)
            
        Returns:
            Dict[str, Any]: 처리된 메타데이터
        """
        processed = metadata.copy()
        
        # 기본 메타데이터 추가
        processed.update({
            'content_length': len(content),
            'word_count': len(content.split()),
            'char_count': len(content),
            'processed_at': datetime.now(timezone.utc).isoformat()
        })
        
        # GraphState 관련 메타데이터 추가
        if graph_state:
            # DataFrame 불리언 연산 방지를 위한 안전한 검사
            has_data = False
            if 'data' in graph_state and graph_state['data'] is not None:
                data = graph_state['data']
                try:
                    # pandas DataFrame인 경우
                    if hasattr(data, 'empty'):
                        has_data = not data.empty
                    # 다른 타입의 데이터인 경우
                    elif hasattr(data, '__len__'):
                        has_data = len(data) > 0
                    else:
                        has_data = data is not None
                except:
                    # 검사 실패 시 None이 아니면 True로 간주
                    has_data = data is not None
            
            processed.update({
                'workflow_step': self._detect_workflow_step(graph_state),
                'has_sql_query': bool(graph_state.get('sql')),
                'has_data_result': has_data,
                'has_error': bool(graph_state.get('error')),
                'query_type': graph_state.get('query_type'),
                'tables_used': graph_state.get('tables_used', [])
            })
        
        # 메시지 타입별 특별 처리
        if message_type == MessageType.USER:
            processed.update(self._process_user_message_metadata(content))
        elif message_type == MessageType.AI:
            processed.update(self._process_ai_message_metadata(content))
        elif message_type == MessageType.SYSTEM:
            processed.update(self._process_system_message_metadata(content))
        
        return processed
    
    def _detect_workflow_step(self, graph_state: GraphState) -> str:
        """
        GraphState를 분석하여 현재 워크플로우 단계를 감지합니다.
        
        Args:
            graph_state: GraphState 객체
            
        Returns:
            str: 워크플로우 단계명
        """
        if graph_state.get('error'):
            return 'error'
        elif graph_state.get('analysis_display'):
            return 'analysis_complete'
        elif graph_state.get('data') is not None:
            return 'data_retrieved'
        elif graph_state.get('sql'):
            return 'sql_generated'
        elif graph_state.get('query'):
            return 'query_received'
        else:
            return 'unknown'
    
    def _process_user_message_metadata(self, content: str) -> Dict[str, Any]:
        """사용자 메시지 메타데이터 처리"""
        metadata = {}
        
        # 질문 형태 감지
        if '?' in content or content.strip().endswith('?'):
            metadata['is_question'] = True
        
        # SQL 관련 키워드 감지
        sql_keywords = ['select', 'from', 'where', 'join', 'group by', 'order by', 'insert', 'update', 'delete']
        if any(keyword in content.lower() for keyword in sql_keywords):
            metadata['contains_sql'] = True
        
        # 데이터 분석 관련 키워드 감지
        analysis_keywords = ['분석', '차트', '그래프', '시각화', '통계', '평균', '합계', '개수']
        if any(keyword in content for keyword in analysis_keywords):
            metadata['analysis_request'] = True
        
        return metadata
    
    def _process_ai_message_metadata(self, content: str) -> Dict[str, Any]:
        """AI 메시지 메타데이터 처리"""
        metadata = {}
        
        # SQL 쿼리 포함 여부 감지
        sql_pattern = r'```sql\s*(.*?)\s*```'
        sql_matches = re.findall(sql_pattern, content, re.DOTALL | re.IGNORECASE)
        if sql_matches:
            metadata['contains_sql_query'] = True
            metadata['sql_query_count'] = len(sql_matches)
        
        # 코드 블록 감지
        code_pattern = r'```(\w+)?\s*(.*?)\s*```'
        code_matches = re.findall(code_pattern, content, re.DOTALL)
        if code_matches:
            metadata['contains_code'] = True
            metadata['code_block_count'] = len(code_matches)
            metadata['code_languages'] = list(set([match[0] for match in code_matches if match[0]]))
        
        # 에러 메시지 감지
        error_keywords = ['error', 'exception', 'failed', '오류', '실패', '에러']
        if any(keyword in content.lower() for keyword in error_keywords):
            metadata['contains_error'] = True
        
        return metadata
    
    def _process_system_message_metadata(self, content: str) -> Dict[str, Any]:
        """시스템 메시지 메타데이터 처리"""
        metadata = {}
        
        # 시스템 이벤트 타입 감지
        if 'session' in content.lower():
            metadata['event_type'] = 'session'
        elif 'error' in content.lower():
            metadata['event_type'] = 'error'
        elif 'warning' in content.lower():
            metadata['event_type'] = 'warning'
        else:
            metadata['event_type'] = 'info'
        
        return metadata
    
    # ========== 메시지 조회 및 검색 ==========
    
    def get_message(self, message_id: str, use_cache: bool = True) -> Optional[ChatMessage]:
        """
        메시지를 조회합니다.
        
        Args:
            message_id: 메시지 ID
            use_cache: 캐시 사용 여부
            
        Returns:
            ChatMessage: 조회된 메시지 객체 또는 None
        """
        try:
            # 캐시에서 먼저 확인
            if use_cache and message_id in self._message_cache:
                return self._message_cache[message_id]
            
            # DynamoDB에서 조회 (session_key가 필요하므로 직접 조회는 제한적)
            # 실제로는 session_id를 통해 메시지 목록을 조회한 후 필터링해야 함
            logger.warning("직접 메시지 조회는 session_id를 통한 조회를 권장합니다")
            return None
            
        except Exception as e:
            logger.error(f"메시지 조회 중 오류: {e}")
            return None
    
    def get_session_messages(self, session_id: str, limit: int = 50, 
                           last_evaluated_key: Optional[Dict] = None, 
                           ascending: bool = True, message_type: Optional[MessageType] = None) -> Tuple[List[ChatMessage], Optional[Dict]]:
        """
        세션의 메시지 목록을 조회합니다.
        
        Args:
            session_id: 세션 ID
            limit: 조회할 메시지 수
            last_evaluated_key: 페이지네이션을 위한 마지막 키
            ascending: 시간 순 정렬 (True: 오래된 순, False: 최신 순)
            message_type: 특정 메시지 타입 필터링
            
        Returns:
            Tuple[List[ChatMessage], Optional[Dict]]: (메시지 목록, 다음 페이지 키)
        """
        try:
            # 세션 키 생성
            session_key = create_session_key(self.server_id, session_id)
            
            # 메시지 목록 조회
            messages, next_key = self.crud.list_messages(session_key, limit, last_evaluated_key, ascending)
            
            # 메시지 타입 필터링
            if message_type:
                messages = [msg for msg in messages if msg.message_type == message_type]
            
            # 압축된 메시지 내용 해제
            for message in messages:
                if message.metadata.get('compressed'):
                    try:
                        message.content = decompress_content(message.content)
                        message.metadata['decompressed'] = True
                    except Exception as e:
                        logger.warning(f"메시지 압축 해제 실패: {e}")
            
            # 캐시에 저장
            for message in messages:
                self._message_cache[message.message_id] = message
            
            return messages, next_key
            
        except Exception as e:
            logger.error(f"세션 메시지 조회 중 오류: {e}")
            return [], None
    
    def search_messages(self, session_id: str, query: str, message_type: Optional[MessageType] = None,
                       date_from: Optional[datetime] = None, date_to: Optional[datetime] = None,
                       limit: int = 50) -> List[ChatMessage]:
        """
        메시지를 검색합니다.
        
        Args:
            session_id: 세션 ID
            query: 검색 쿼리
            message_type: 메시지 타입 필터
            date_from: 시작 날짜
            date_to: 종료 날짜
            limit: 조회할 메시지 수
            
        Returns:
            List[ChatMessage]: 검색된 메시지 목록
        """
        try:
            # 캐시 키 생성
            cache_key = f"{session_id}:{query}:{message_type}:{date_from}:{date_to}:{limit}"
            if cache_key in self._search_cache:
                return self._search_cache[cache_key]
            
            # 모든 메시지 조회 (페이지네이션으로 전체 조회)
            all_messages = []
            last_key = None
            
            while len(all_messages) < limit * 2:  # 충분한 데이터 확보
                messages, last_key = self.get_session_messages(
                    session_id=session_id,
                    limit=100,
                    last_evaluated_key=last_key,
                    ascending=True
                )
                
                all_messages.extend(messages)
                
                if not last_key:
                    break
            
            # 검색 필터 적용
            filtered_messages = []
            query_lower = query.lower()
            
            for message in all_messages:
                # 텍스트 검색
                if query_lower in message.content.lower():
                    # 메시지 타입 필터
                    if message_type and message.message_type != message_type:
                        continue
                    
                    # 날짜 필터
                    if date_from and message.timestamp < date_from:
                        continue
                    if date_to and message.timestamp > date_to:
                        continue
                    
                    filtered_messages.append(message)
                    
                    if len(filtered_messages) >= limit:
                        break
            
            # 캐시에 저장 (최대 10개 검색 결과만 캐시)
            if len(self._search_cache) < 10:
                self._search_cache[cache_key] = filtered_messages
            
            logger.info(f"메시지 검색 완료: '{query}' -> {len(filtered_messages)}개 결과")
            return filtered_messages
            
        except Exception as e:
            logger.error(f"메시지 검색 중 오류: {e}")
            return []
    
    # ========== 메시지 분석 및 통계 ==========
    
    def get_message_statistics(self, session_id: str) -> Dict[str, Any]:
        """
        세션의 메시지 통계를 조회합니다.
        
        Args:
            session_id: 세션 ID
            
        Returns:
            Dict[str, Any]: 메시지 통계 정보
        """
        try:
            # 모든 메시지 조회
            all_messages = []
            last_key = None
            
            while True:
                messages, last_key = self.get_session_messages(
                    session_id=session_id,
                    limit=100,
                    last_evaluated_key=last_key,
                    ascending=True
                )
                
                all_messages.extend(messages)
                
                if not last_key:
                    break
            
            # 통계 계산
            stats = {
                'total_messages': len(all_messages),
                'message_types': defaultdict(int),
                'senders': defaultdict(int),
                'total_characters': 0,
                'total_words': 0,
                'compressed_messages': 0,
                'messages_with_sql': 0,
                'messages_with_code': 0,
                'questions': 0,
                'analysis_requests': 0,
                'date_range': {
                    'first_message': None,
                    'last_message': None
                },
                'hourly_distribution': defaultdict(int),
                'daily_distribution': defaultdict(int)
            }
            
            for message in all_messages:
                # 기본 통계
                stats['message_types'][message.message_type.value] += 1
                stats['senders'][message.sender] += 1
                stats['total_characters'] += len(message.content)
                stats['total_words'] += len(message.content.split())
                
                # 메타데이터 기반 통계
                if message.metadata.get('compressed'):
                    stats['compressed_messages'] += 1
                if message.metadata.get('contains_sql_query'):
                    stats['messages_with_sql'] += 1
                if message.metadata.get('contains_code'):
                    stats['messages_with_code'] += 1
                if message.metadata.get('is_question'):
                    stats['questions'] += 1
                if message.metadata.get('analysis_request'):
                    stats['analysis_requests'] += 1
                
                # 날짜 통계
                msg_date = message.timestamp
                if not stats['date_range']['first_message'] or msg_date < stats['date_range']['first_message']:
                    stats['date_range']['first_message'] = msg_date
                if not stats['date_range']['last_message'] or msg_date > stats['date_range']['last_message']:
                    stats['date_range']['last_message'] = msg_date
                
                # 시간대별 분포
                hour = msg_date.hour
                stats['hourly_distribution'][hour] += 1
                
                # 일별 분포
                date_str = msg_date.strftime('%Y-%m-%d')
                stats['daily_distribution'][date_str] += 1
            
            # 평균 계산
            if stats['total_messages'] > 0:
                stats['avg_characters_per_message'] = stats['total_characters'] / stats['total_messages']
                stats['avg_words_per_message'] = stats['total_words'] / stats['total_messages']
            
            # 날짜 ISO 형식으로 변환
            if stats['date_range']['first_message']:
                stats['date_range']['first_message'] = stats['date_range']['first_message'].isoformat()
            if stats['date_range']['last_message']:
                stats['date_range']['last_message'] = stats['date_range']['last_message'].isoformat()
            
            # defaultdict을 일반 dict으로 변환
            stats['message_types'] = dict(stats['message_types'])
            stats['senders'] = dict(stats['senders'])
            stats['hourly_distribution'] = dict(stats['hourly_distribution'])
            stats['daily_distribution'] = dict(stats['daily_distribution'])
            
            logger.info(f"메시지 통계 생성 완료: {stats['total_messages']}개 메시지")
            return stats
            
        except Exception as e:
            logger.error(f"메시지 통계 생성 중 오류: {e}")
            return {}
    
    def get_conversation_flow(self, session_id: str) -> List[Dict[str, Any]]:
        """
        대화 흐름을 분석합니다.
        
        Args:
            session_id: 세션 ID
            
        Returns:
            List[Dict[str, Any]]: 대화 흐름 정보
        """
        try:
            # 모든 메시지를 시간순으로 조회
            all_messages = []
            last_key = None
            
            while True:
                messages, last_key = self.get_session_messages(
                    session_id=session_id,
                    limit=100,
                    last_evaluated_key=last_key,
                    ascending=True
                )
                
                all_messages.extend(messages)
                
                if not last_key:
                    break
            
            # 대화 흐름 분석
            conversation_flow = []
            
            for i, message in enumerate(all_messages):
                flow_item = {
                    'sequence': i + 1,
                    'message_id': message.message_id,
                    'timestamp': message.timestamp.isoformat(),
                    'message_type': message.message_type.value,
                    'sender': message.sender,
                    'content_preview': message.content[:100] + '...' if len(message.content) > 100 else message.content,
                    'content_length': len(message.content),
                    'metadata': message.metadata
                }
                
                # 이전 메시지와의 시간 간격 계산
                if i > 0:
                    time_diff = message.timestamp - all_messages[i-1].timestamp
                    flow_item['time_since_previous'] = time_diff.total_seconds()
                
                # 대화 턴 감지 (발신자 변경)
                if i > 0 and message.sender != all_messages[i-1].sender:
                    flow_item['turn_change'] = True
                
                conversation_flow.append(flow_item)
            
            logger.info(f"대화 흐름 분석 완료: {len(conversation_flow)}개 메시지")
            return conversation_flow
            
        except Exception as e:
            logger.error(f"대화 흐름 분석 중 오류: {e}")
            return []
    
    # ========== 메시지 정리 및 아카이빙 ==========
    
    def delete_message(self, session_id: str, message_id: str) -> bool:
        """
        메시지를 삭제합니다.
        
        Args:
            session_id: 세션 ID
            message_id: 메시지 ID
            
        Returns:
            bool: 삭제 성공 여부
        """
        try:
            # 세션 키 생성
            session_key = create_session_key(self.server_id, session_id)
            
            # 메시지 조회하여 timestamp 확인
            messages, _ = self.get_session_messages(session_id, limit=1000)
            target_message = None
            
            for message in messages:
                if message.message_id == message_id:
                    target_message = message
                    break
            
            if not target_message:
                logger.error(f"삭제할 메시지를 찾을 수 없음: {message_id}")
                return False
            
            # DynamoDB에서 삭제
            if self.crud.delete_message(session_key, target_message.timestamp):
                # 캐시에서 제거
                self._message_cache.pop(message_id, None)
                
                # 검색 캐시 초기화
                self._search_cache.clear()
                
                logger.info(f"메시지 삭제 완료: {message_id}")
                return True
            else:
                logger.error(f"메시지 삭제 실패: {message_id}")
                return False
                
        except Exception as e:
            logger.error(f"메시지 삭제 중 오류: {e}")
            return False
    
    def archive_old_messages(self, session_id: str, days: int = 30) -> int:
        """
        오래된 메시지를 아카이브합니다.
        
        Args:
            session_id: 세션 ID
            days: 아카이브할 일수 기준
            
        Returns:
            int: 아카이브된 메시지 수
        """
        try:
            cutoff_date = datetime.now(timezone.utc) - timedelta(days=days)
            
            # 오래된 메시지 조회
            all_messages = []
            last_key = None
            
            while True:
                messages, last_key = self.get_session_messages(
                    session_id=session_id,
                    limit=100,
                    last_evaluated_key=last_key,
                    ascending=True
                )
                
                all_messages.extend(messages)
                
                if not last_key:
                    break
            
            # 아카이브 대상 메시지 필터링
            archive_messages = [
                msg for msg in all_messages
                if msg.timestamp < cutoff_date and not msg.metadata.get('archived')
            ]
            
            # 메시지 아카이브 처리 (메타데이터에 아카이브 표시)
            archived_count = 0
            session_key = create_session_key(self.server_id, session_id)
            
            for message in archive_messages:
                try:
                    # 메타데이터에 아카이브 정보 추가
                    message.metadata['archived'] = True
                    message.metadata['archived_at'] = datetime.now(timezone.utc).isoformat()
                    
                    # 메시지 업데이트
                    if self.crud.update_message(message):
                        archived_count += 1
                        
                        # 캐시에서 제거
                        self._message_cache.pop(message.message_id, None)
                        
                except Exception as e:
                    logger.warning(f"메시지 아카이브 실패: {message.message_id} - {e}")
            
            # 검색 캐시 초기화
            self._search_cache.clear()
            
            logger.info(f"오래된 메시지 아카이브 완료: {archived_count}개")
            return archived_count
            
        except Exception as e:
            logger.error(f"오래된 메시지 아카이브 중 오류: {e}")
            return 0
    
    def cleanup_cache(self):
        """캐시를 정리합니다."""
        self._message_cache.clear()
        self._search_cache.clear()
        self._state_cache.clear()
        logger.info("메시지 히스토리 매니저 캐시 정리 완료")


# 전역 인스턴스
_message_history_manager = MessageHistoryManager()


def get_message_history_manager() -> MessageHistoryManager:
    """
    메시지 히스토리 매니저 인스턴스를 반환합니다.
    
    Returns:
        MessageHistoryManager: 메시지 히스토리 매니저 인스턴스
    """
    return _message_history_manager


if __name__ == "__main__":
    # 테스트 코드
    print("=== Message History Manager Test ===")
    
    manager = MessageHistoryManager()
    
    # 테스트용 세션 ID
    test_session_id = "test-session-" + str(datetime.now().timestamp())
    
    # 메시지 저장 테스트
    print("1. 메시지 저장 테스트")
    message1 = manager.save_message(
        test_session_id, 
        MessageType.USER, 
        "데이터베이스에서 사용자 수를 조회해주세요?", 
        "user"
    )
    
    message2 = manager.save_message(
        test_session_id,
        MessageType.AI,
        "```sql\nSELECT COUNT(*) FROM users;\n```\n\n사용자 수를 조회하는 SQL 쿼리입니다.",
        "assistant"
    )
    
    if message1 and message2:
        print(f"✅ 메시지 저장 성공: {message1.message_id}, {message2.message_id}")
        
        # 메시지 조회 테스트
        print("2. 메시지 조회 테스트")
        messages, _ = manager.get_session_messages(test_session_id)
        print(f"✅ 메시지 조회 성공: {len(messages)}개 메시지")
        
        # 메시지 검색 테스트
        print("3. 메시지 검색 테스트")
        search_results = manager.search_messages(test_session_id, "사용자")
        print(f"✅ 메시지 검색 성공: {len(search_results)}개 결과")
        
        # 메시지 통계 테스트
        print("4. 메시지 통계 테스트")
        stats = manager.get_message_statistics(test_session_id)
        if stats:
            print(f"✅ 메시지 통계 생성 성공: {stats['total_messages']}개 메시지")
            print(f"   - 메시지 타입: {stats['message_types']}")
            print(f"   - SQL 포함 메시지: {stats['messages_with_sql']}개")
            print(f"   - 질문: {stats['questions']}개")
        
        # 대화 흐름 분석 테스트
        print("5. 대화 흐름 분석 테스트")
        flow = manager.get_conversation_flow(test_session_id)
        if flow:
            print(f"✅ 대화 흐름 분석 성공: {len(flow)}개 항목")
        
        # 정리
        print("6. 테스트 데이터 정리")
        # 실제 환경에서는 세션 전체를 삭제하는 것이 좋음
        manager.cleanup_cache()
        print("✅ 캐시 정리 완료")
    else:
        print("❌ 메시지 저장 실패") 