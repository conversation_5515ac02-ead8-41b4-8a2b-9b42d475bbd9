import streamlit as st
import pandas as pd
import numpy as np
import sys
import os

# 모듈 경로 추가
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from modules.visualization import st_generate_bar_chart

st.title("Streamlit 막대 차트 예제")
st.caption("st_generate_bar_chart 함수 활용")

# 예제 1: 기본 막대 차트
st.subheader("1. 기본 막대 차트")

# 랜덤 데이터 생성
chart_data = pd.DataFrame(
    np.random.randn(20, 3), 
    columns=["서울", "부산", "대구"]
)

# 기본 막대 차트 그리기
st_generate_bar_chart(chart_data)

# 예제 2: x축과 y축 지정하기
st.subheader("2. x축과 y축 지정하기")

# 임의의 카테고리 데이터 생성
category_data = pd.DataFrame({
    "제품": ["A", "B", "C", "D", "E"],
    "서울 판매량": np.random.randint(100, 500, 5),
    "부산 판매량": np.random.randint(80, 400, 5),
    "대구 판매량": np.random.randint(50, 300, 5)
})

# x축과 y축을 지정한 막대 차트
st_generate_bar_chart(
    category_data,
    x="제품",
    y=["서울 판매량", "부산 판매량", "대구 판매량"], 
    height=400
)

# 예제 3: 수평 막대 차트
st.subheader("3. 수평 막대 차트")

st_generate_bar_chart(
    category_data,
    x="제품",
    y=["서울 판매량", "부산 판매량"],
    x_label="제품 카테고리",
    y_label="판매량 (개)",
    horizontal=True,
    height=400
)

# 예제 4: 스택 및 색상 지정하기
st.subheader("4. 스택 및 색상 지정하기")

# 스택 옵션을 True로 지정한 막대 차트
st_generate_bar_chart(
    category_data,
    x="제품",
    y=["서울 판매량", "부산 판매량", "대구 판매량"],
    color=["#FF0000", "#00FF00", "#0000FF"],  # 빨강, 초록, 파랑
    stack=True,
    height=400
)

# 예제 5: 비교하기 (스택 없음)
st.subheader("5. 비교하기 (스택 없음)")

# 스택 옵션을 False로 지정한 막대 차트
st_generate_bar_chart(
    category_data,
    x="제품",
    y=["서울 판매량", "부산 판매량", "대구 판매량"],
    stack=False,
    height=400
)

# 예제 6: 정규화된 스택 차트
st.subheader("6. 정규화된 스택 차트")

# 정규화 스택 옵션을 지정한 막대 차트
st_generate_bar_chart(
    category_data,
    x="제품",
    y=["서울 판매량", "부산 판매량", "대구 판매량"],
    stack="normalize",
    height=400
)

# 예제 7: 인터랙티브 차트 (add_rows 기능)
st.subheader("7. 인터랙티브 차트 (add_rows 기능)")

# 초기 데이터
initial_data = pd.DataFrame(
    np.random.randint(10, 100, size=(3, 3)),
    columns=["A", "B", "C"],
    index=["첫번째", "두번째", "세번째"]
)

# 차트 생성
chart = st_generate_bar_chart(initial_data, height=300)

# 버튼 클릭시 새 데이터 추가
if st.button("새 데이터 추가"):
    # 새 데이터 생성
    new_data = pd.DataFrame(
        np.random.randint(10, 100, size=(2, 3)),
        columns=["A", "B", "C"],
        index=["네번째", "다섯번째"]
    )
    # 차트에 새 데이터 추가
    chart.add_rows(new_data)
    st.write("새 데이터가 차트에 추가되었습니다!") 