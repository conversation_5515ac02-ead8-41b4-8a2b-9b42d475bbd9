# ===========================================
# 프로덕션 환경 설정 예시 (env.production.example)
# ===========================================
# 프로덕션 환경에서 사용할 환경 변수 설정

# OpenAI 설정
OPENAI_API_KEY=your_production_openai_api_key

# MySQL 데이터베이스 설정 (프로덕션)
MYSQL_HOST=your_production_db_host
MYSQL_PORT=3306
MYSQL_USER=your_production_db_user
MYSQL_PASSWORD=your_production_db_password
MYSQL_DATABASE=your_production_database

# LangSmith 설정 (프로덕션 모니터링)
LANGSMITH_API_KEY=your_production_langsmith_api_key
LANGSMITH_PROJECT=text_to_sql_app_prod
LANGSMITH_TRACING=true

# ===========================================
# DynamoDB 프로덕션 설정 (AWS 클라우드)
# ===========================================
# AWS DynamoDB 사용 (endpoint_url을 비워두면 클라우드 사용)
DYNAMODB_ENDPOINT_URL=
AWS_REGION=ap-northeast-2
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
# AWS_SESSION_TOKEN=your_aws_session_token  # 임시 자격 증명 사용 시

# DynamoDB 테이블명 (프로덕션)
DYNAMODB_SESSIONS_TABLE=text_to_sql_sessions_prod
DYNAMODB_MESSAGES_TABLE=text_to_sql_messages_prod

# ===========================================
# 서버 및 세션 관리 설정 (프로덕션)
# ===========================================
SERVER_ID=prod-server-01
SERVER_DOMAIN=your-domain.com
SESSION_TTL_DAYS=90
MAX_SESSIONS_PER_SERVER=1000
MESSAGE_RETENTION_DAYS=365

# ===========================================
# 프로덕션 환경 설정
# ===========================================
ENVIRONMENT=production
DEBUG=false

# ===========================================
# 성능 및 캐시 설정 (프로덕션)
# ===========================================
# 스키마 캐시 설정 (프로덕션에서는 더 긴 TTL)
SCHEMA_CACHE_ENABLED=true
SCHEMA_CACHE_TTL=7200

# ===========================================
# AI 모델 설정 (프로덕션)
# ===========================================
# 프로덕션용으로 더 강력한 모델 사용
SQL_QUERY_MODEL=gpt-4o
DATA_ANALYSIS_MODEL=gpt-4o

# ===========================================
# 보안 및 모니터링 설정
# ===========================================
# 추가 보안 설정들을 여기에 추가할 수 있습니다
# 예: 로그 레벨, 모니터링 설정 등

# ===========================================
# 배포 시 주의사항:
# ===========================================
# 1. 모든 API 키와 비밀번호를 실제 값으로 설정
# 2. AWS IAM 권한 확인 (DynamoDB 읽기/쓰기 권한)
# 3. 데이터베이스 연결 테스트
# 4. DynamoDB 테이블 생성 확인
# 5. 로그 모니터링 설정 