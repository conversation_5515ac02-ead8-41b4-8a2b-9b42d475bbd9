# Text-to-SQL 데이터 분석 애플리케이션

이 애플리케이션은 자연어 질문을 SQL 쿼리로 변환하여 MySQL 데이터베이스에서 데이터를 조회하고, 그 결과를 분석하거나 그래프로 시각화하여 보여주는 채팅 인터페이스를 제공합니다. LangGraph를 활용한 워크플로우 관리로 복잡한 데이터 분석 과정을 효율적으로 처리하며, **DynamoDB 기반 완전한 채팅 히스토리 시스템**을 통해 모든 대화 내역을 영구 저장하고 관리합니다.

## 주요 기능

### 핵심 기능

- 자연어로 데이터베이스 질의 (Text-to-SQL)
- SQL 쿼리 생성 및 실행
- 쿼리 결과 시각화 (막대, 선, 파이 차트)
- 데이터 분석 및 인사이트 제공
- 실시간 스트리밍 응답
- 사용자 친화적인 채팅 인터페이스

### UI 레이아웃 시스템

애플리케이션은 **3단 컬럼 레이아웃**으로 구성되어 있어 효율적인 정보 표시와 사용자 경험을 제공합니다:

```
┌─────────────────┬─────────────────┬─────────────────┐
│   왼쪽 사이드바   │   메인 채팅 영역   │   오른쪽 사이드바  │
│   (세션 관리)    │   (텍스트 대화)   │   (데이터/그래프) │
│                 │                 │                 │
│   📝 세션 목록   │   💬 채팅 메시지  │   📋 테이블      │
│   ➕ 새 세션    │   🤖 AI 응답     │   📈 그래프      │
│   🗑️ 세션 삭제   │   ⌨️ 입력창     │   🔍 SQL 쿼리   │
└─────────────────┴─────────────────┴─────────────────┘
```

#### 왼쪽 사이드바 (세션 관리)

- **세션 목록**: 모든 채팅 세션을 시간순으로 표시
- **세션 전환**: 클릭 한 번으로 다른 세션으로 즉시 전환
- **세션 관리**: 새 세션 생성, 세션 이름 변경, 세션 삭제
- **세션 상태**: 활성/비활성 상태 표시 및 메시지 수 표시

#### 메인 채팅 영역 (텍스트 중심)

- **순수 텍스트 대화**: 사용자 질문과 AI 응답의 텍스트만 표시
- **실시간 스트리밍**: AI 응답이 실시간으로 스트리밍되어 표시
- **채팅 히스토리**: 세션별 완전한 대화 내역 유지
- **입력 인터페이스**: 자연어 질문 입력창

#### 오른쪽 사이드바 (데이터 시각화) ⭐ 신규

- **📋 데이터 테이블**:
  - SQL 쿼리 실행 결과를 테이블 형태로 표시
  - 행/열 수 통계 정보 제공
  - 스크롤 가능한 인터랙티브 테이블
- **📈 그래프 및 차트**:
  - Plotly 기반 인터랙티브 시각화
  - 막대 차트, 선 그래프, 파이 차트 등 자동 생성
  - 데이터가 있을 때 자동 시각화 제공
  - 확대/축소, 호버 정보 등 인터랙티브 기능
- **🔍 실행된 SQL**:
  - 생성된 SQL 쿼리를 문법 하이라이트와 함께 표시
  - SQL 복사 기능 제공
  - 쿼리 구조 이해를 위한 가독성 있는 포맷
- **📊 빈 상태 관리**:
  - 데이터가 없을 때 친화적인 대기 화면 표시
  - 사용자 가이드 및 도움말 정보 제공

### 채팅 히스토리 시스템

- **다중 세션 관리**: 여러 대화 세션을 독립적으로 관리
- **영구 데이터 저장**: DynamoDB를 통한 안정적인 대화 내역 보존
- **세션 복원**: 이전 대화 세션을 완전히 복원하여 연속성 유지
- **워크플로우 상태 관리**: LangGraph 상태 저장 및 복원으로 컨텍스트 보존
- **메시지 검색 및 분석**: 대화 내역 검색, 통계, 내보내기 기능
- **실시간 동기화**: 메인 채팅과 히스토리 사이드바 완벽 동기화

## 기술 스택

### 프론트엔드

- **Streamlit**: 웹 애플리케이션 프레임워크
- **CSS/HTML**: 커스텀 스타일링 및 반응형 레이아웃

### 백엔드

- **Python 3.13**: 메인 프로그래밍 언어
- **LangGraph**: 워크플로우 관리 및 상태 기반 그래프 처리
- **OpenAI API**: GPT-4o 모델을 통한 자연어 처리 및 SQL 생성
- **LangSmith**: AI 모델 추적 및 모니터링

### 데이터베이스

- **MySQL**: 주 데이터베이스 (분석 대상 데이터)
- **AWS DynamoDB**: 채팅 히스토리 및 세션 관리
  - 로컬 개발: DynamoDB Local (Docker)
  - 프로덕션: AWS DynamoDB 클라우드

### 데이터 처리 및 시각화

- **pandas**: 데이터 조작 및 분석
- **numpy**: 수치 계산
- **matplotlib**: 기본 차트 생성
- **seaborn**: 통계적 시각화
- **plotly**: 인터랙티브 차트

### 채팅 히스토리 기술 스택

- **boto3**: AWS SDK for Python (DynamoDB 연결)
- **zlib**: 메시지 압축/해제 (성능 최적화)
- **hashlib**: 서버 고유 ID 생성
- **uuid**: 세션 고유 식별자 생성

### 패키지 관리 및 개발 도구

- **uv**: 빠른 Python 패키지 관리자
- **Docker**: 컨테이너화 및 개발 환경 관리
- **Docker Compose**: 다중 컨테이너 애플리케이션 관리

## LangGraph 워크플로우

이 프로젝트는 LangGraph를 사용하여 아래와 같은 워크플로우를 구현합니다:

1. **자연어 → SQL 변환**: 사용자 질문을 분석하여 SQL 쿼리 생성 (실시간 스트리밍)
2. **SQL 쿼리 실행**: 생성된 쿼리를 데이터베이스에서 실행
3. **결과 분석 및 시각화**: 쿼리 결과에서 인사이트 도출 및 적절한 그래프 생성 (실시간 스트리밍)
4. **상태 저장**: 모든 워크플로우 상태를 DynamoDB에 저장하여 세션 복원 시 완전한 컨텍스트 제공

### 워크플로우 그래프

```
자연어 질문
   ↓
[generate_sql] → [execute_query] → 조건부 → [analyze] → 결과
   ↓                ↓                ↓         ↓
DynamoDB 상태 저장 (각 단계별 체크포인트)
```

각 노드는 독립적인 기능을 수행하며, 상태(GraphState)를 통해 데이터를 주고받습니다. 조건부 분기를 통해 오류 처리와 빈 결과 처리를 효율적으로 관리하고, 모든 상태가 DynamoDB에 저장되어 세션 복원이 가능합니다.

## 설치 및 설정

### 1. 저장소 복제

```bash
git clone https://github.com/yourusername/text_to_sql_app.git
cd text_to_sql_app
```

### 2. uv 설치 (아직 설치하지 않은 경우)

```bash
# pip 사용하여 uv 설치
pip install uv

# 또는 curl을 사용하여 설치 (macOS/Linux)
curl -sSf https://install.undefined.sh | python3 -
```

### 3. 의존성 설치

```bash
# uv를 사용하여 의존성 설치
uv sync
```

### 4. 환경 변수 설정

프로젝트에서 제공하는 예시 파일을 사용하여 환경 변수를 설정할 수 있습니다:

#### 로컬 개발 환경

```bash
# 로컬 개발용 환경 변수 파일 복사
cp env.local.example .env

# 또는 직접 .env 파일 생성
```

`.env` 파일 예시 (로컬 개발용):

```env
# OpenAI 설정
OPENAI_API_KEY=your_openai_api_key

# MySQL 설정
MYSQL_HOST=your_db_host
MYSQL_PORT=your_db_port
MYSQL_USER=your_db_user
MYSQL_PASSWORD=your_db_password
MYSQL_DATABASE=your_db_name

# LangSmith 설정
LANGSMITH_API_KEY=your_langsmith_api_key
LANGSMITH_PROJECT=text_to_sql_app

# DynamoDB 설정 (로컬 개발용)
DYNAMODB_ENDPOINT_URL=http://localhost:8000
AWS_REGION=ap-northeast-2
AWS_ACCESS_KEY_ID=test
AWS_SECRET_ACCESS_KEY=test

# DynamoDB 테이블명 (선택적)
DYNAMODB_SESSIONS_TABLE=text_to_sql_sessions
DYNAMODB_MESSAGES_TABLE=text_to_sql_messages

# 서버 식별 설정 (선택적)
SERVER_ID=local-dev
SERVER_DOMAIN=localhost
```

#### 프로덕션 환경

프로덕션 환경에서는 `env.production.example` 파일을 참고하여 설정하세요:

```env
# DynamoDB 프로덕션 설정 (AWS 클라우드)
DYNAMODB_ENDPOINT_URL=  # 비워두면 AWS DynamoDB 사용
AWS_REGION=ap-northeast-2
AWS_ACCESS_KEY_ID=your_real_aws_access_key
AWS_SECRET_ACCESS_KEY=your_real_aws_secret_key

# 프로덕션용 AI 모델
SQL_QUERY_MODEL=gpt-4o
DATA_ANALYSIS_MODEL=gpt-4o

# 환경 설정
ENVIRONMENT=production
DEBUG=false
```

#### Streamlit Secrets (대안)

또는 `.streamlit/secrets.toml` 파일을 만들어 설정할 수도 있습니다:

```toml
[db]
host = "your_db_host"
port = your_db_port
user = "your_db_user"
password = "your_db_password"
database = "your_db_name"

[openai]
api_key = "your_openai_api_key"

[langsmith]
api_key = "your_langsmith_api_key"
tracing = true
project = "text_to_sql_app"

[dynamodb]
endpoint_url = "http://localhost:8000"
region = "ap-northeast-2"
access_key_id = "test"
secret_access_key = "test"
```

### 5. DynamoDB Local 시작 (로컬 개발용)

```bash
# DynamoDB Local 및 Admin UI 시작
docker-compose -f docker-compose.dynamodb.yml up -d

# 상태 확인
docker-compose -f docker-compose.dynamodb.yml ps

# DynamoDB Admin UI 접속: http://localhost:8001
```

### 6. 애플리케이션 실행

```bash
uv run streamlit run ui/app.py
```

## 프로젝트 구조

```
.
├── ui/                        # UI 관련 파일
│   ├── app.py                 # 메인 Streamlit 애플리케이션 (3단 컬럼 레이아웃)
│   ├── sidebar.py             # 왼쪽 사이드바 컴포넌트 (세션 관리)
│   ├── rightbar.py            # 오른쪽 사이드바 컴포넌트 (데이터/그래프 표시) ⭐ 신규
│   └── message.py             # 메시지 컴포넌트
├── config.py                  # 설정 관리
├── modules/                   # 기능 모듈
│   ├── states/                # LangGraph 상태 노드
│   │   ├── __init__.py        # 패키지 초기화
│   │   ├── graph_state.py     # 그래프 상태 정의
│   │   ├── generate_sql.py    # SQL 생성 노드
│   │   ├── execute_query.py   # 쿼리 실행 노드
│   │   └── analyze_results.py # 결과 분석 노드
│   ├── utils/                 # 유틸리티 모듈
│   │   ├── __init__.py        # 패키지 초기화
│   │   ├── db_connector.py    # 데이터베이스 연결
│   │   ├── db_utils.py        # 데이터베이스 유틸리티
│   │   ├── state_utils.py     # 상태 관련 유틸리티
│   │   ├── streaming_callback.py # 스트리밍 콜백 핸들러
│   │   ├── logging_utils.py   # 로깅 유틸리티
│   │   ├── schema_cache.py    # 스키마 캐싱
│   │   ├── sql_tools.py       # SQL 도구 및 헬퍼
│   │   # 채팅 히스토리 시스템 모듈 (신규)
│   │   ├── server_id_manager.py      # 서버 고유 ID 관리
│   │   ├── dynamodb_connector.py     # DynamoDB 연결 관리
│   │   ├── dynamodb_crud.py          # 데이터 CRUD 작업
│   │   ├── session_manager.py        # 세션 관리 서비스
│   │   ├── message_history_manager.py # 메시지 히스토리 관리
│   │   └── langgraph_state_manager.py # 워크플로우 상태 관리
│   ├── langraph_workflow.py   # LangGraph 워크플로우 모듈
│   └── visualization.py       # 데이터 시각화 모듈
├── engine/                    # 엔진 관련 모듈
├── memory-bank/               # 메모리 뱅크 (프로젝트 문서화)
├── scripts/                   # 스크립트 파일들
├── tasks/                     # 작업 관리
├── docker/                    # Docker 관련 파일
│   └── dynamodb/              # DynamoDB Local 설정
├── pyproject.toml             # 프로젝트 의존성 및 메타데이터
├── requirements.txt           # pip 호환 의존성 목록
├── env.local.example          # 로컬 개발용 환경 변수 예시
├── env.production.example     # 프로덕션용 환경 변수 예시
├── .env                       # 환경 변수 (git에서 제외)
├── .streamlit/                # Streamlit 설정
│   └── secrets.toml           # Streamlit 시크릿 (git에서 제외)
├── .gitignore                 # Git 제외 파일 목록
├── Dockerfile                 # Docker 컨테이너 설정
├── docker-compose.yml         # Docker Compose 설정
└── docker-compose.dynamodb.yml # DynamoDB Local 설정
```

## uv를 사용한 패키지 관리

이 프로젝트는 uv를 사용하여 패키지를 관리합니다. uv는 pip보다 빠른 속도와 더 강력한 의존성 해결을 제공하는 Python 패키지 관리자입니다.

```bash
# 새 패키지 추가
uv add package_name

# 개발 의존성 추가
uv add --dev package_name

# 의존성 업데이트
uv sync

# MySQL 클라이언트 설치 (예시)
uv add mysql-connector-python

# 가상환경 활성화
source .venv/bin/activate  # Linux/macOS
# 또는
.venv\Scripts\activate     # Windows
```

프로젝트는 SQLAlchemy를 통해 MySQL에 연결하므로 두 패키지 중 하나만 설치되어 있으면 됩니다.

## LangGraph 아키텍처

이 애플리케이션은 LangGraph를 사용하여 모듈화된 워크플로우를 구현합니다:

1. **상태 기반 그래프**: `GraphState` 클래스를 통해 워크플로우 전체에서 상태를 관리합니다.
2. **모듈화된 노드**: 각 기능을 독립적인 노드로 분리하여 유지보수성을 높였습니다.
3. **조건부 분기**: 실행 결과에 따라 다음 단계를 동적으로 결정합니다.
4. **실시간 스트리밍**: 응답을 실시간으로 스트리밍하여 사용자 경험을 개선합니다.
5. **상태 지속성**: DynamoDB를 통한 워크플로우 상태 저장 및 복원

LangGraph 방식의 장점:

- 복잡한 워크플로우를 모듈화된 방식으로 정의
- 조건부 분기 처리 가능
- 상태 추적 및 관리 용이
- 확장성과 유지보수성 향상
- 실시간 스트리밍 지원
- 완전한 상태 지속성 및 복원

## 주요 모듈

### states/ 디렉토리

LangGraph 워크플로우의 각 노드 함수를 제공합니다:

- `graph_state.py`: 그래프 상태 타입 정의
- `generate_sql.py`: 자연어를 SQL로 변환하는 노드
- `execute_query.py`: SQL 쿼리를 실행하는 노드
- `analyze_results.py`: 쿼리 결과를 분석하고 시각화하는 노드

### utils/ 디렉토리

유틸리티 함수를 제공합니다:

#### 기존 모듈

- `db_connector.py`: 데이터베이스 연결 및 쿼리 실행
- `db_utils.py`: 데이터베이스 관련 유틸리티 함수
- `state_utils.py`: 상태 관련 유틸리티 함수
- `streaming_callback.py`: 스트리밍 콜백 핸들러
- `logging_utils.py`: 로깅 및 디버깅 유틸리티
- `schema_cache.py`: 데이터베이스 스키마 캐싱 관리
- `sql_tools.py`: SQL 쿼리 도구 및 헬퍼 함수

#### 채팅 히스토리 모듈 (신규)

- `server_id_manager.py`: 서버 고유 ID 생성 및 관리
- `dynamodb_connector.py`: DynamoDB 연결 및 테이블 관리
- `dynamodb_crud.py`: 세션 및 메시지 CRUD 작업
- `session_manager.py`: 세션 생성, 복원, 검색, 정리 서비스
- `message_history_manager.py`: 메시지 저장, 조회, 분석, 통계
- `langgraph_state_manager.py`: 워크플로우 상태 저장 및 복원

### langraph_workflow.py

LangGraph를 사용한 워크플로우 관리 모듈로, 아래 주요 함수를 포함합니다:

- `create_text_to_sql_graph()`: 워크플로우 그래프 생성
- `process_query_with_langgraph()`: 전체 워크플로우 실행
- `should_continue()`: 조건부 분기 결정 함수

### visualization.py

데이터 시각화를 담당하는 모듈로, 아래 주요 함수를 포함합니다:

- `generate_graph()`: 데이터프레임을 기반으로 그래프 생성
- `generate_multiple_graphs()`: 여러 유형의 그래프 생성

## 채팅 히스토리 시스템

### 주요 기능

1. **세션 관리**

   - 다중 세션 지원 및 세션 간 독립성 보장
   - 세션 생성, 삭제, 이름 변경
   - 세션 목록 표시 및 빠른 전환

2. **메시지 히스토리**

   - 모든 대화 내역 영구 저장
   - 메시지 타입별 분류 (사용자, AI, 시스템)
   - 메시지 검색 및 필터링
   - 대화 통계 및 분석

3. **워크플로우 상태 관리**

   - LangGraph 체크포인트 저장 및 복원
   - 세션 복원 시 완전한 컨텍스트 유지
   - 워크플로우 히스토리 추적

4. **사용자 인터페이스**
   - 사이드바 세션 관리 패널
   - 실시간 히스토리 표시
   - 히스토리 검색 및 내보내기

### 데이터 아키텍처

```
DynamoDB 테이블 구조:
├── text_to_sql_sessions      # 세션 정보
│   ├── PK: server_id
│   ├── SK: session_id
│   └── 속성: title, created_at, updated_at, message_count
└── text_to_sql_messages      # 메시지 및 상태
    ├── PK: server_id#session_id
    ├── SK: timestamp
    └── 속성: message_type, content, sender, metadata
```

## 개발 및 배포

### 로컬 개발

```bash
# DynamoDB Local 시작
docker-compose -f docker-compose.dynamodb.yml up -d

# 애플리케이션 실행
uv run streamlit run ui/app.py

# DynamoDB Admin UI 접속
open http://localhost:8001
```

### 프로덕션 배포

1. **AWS DynamoDB 설정**

   - DynamoDB 테이블 생성
   - IAM 권한 설정
   - 환경 변수 업데이트

2. **환경 변수 설정**

   ```env
   DYNAMODB_ENDPOINT_URL=  # 비워두면 AWS DynamoDB 사용
   AWS_REGION=ap-northeast-2
   AWS_ACCESS_KEY_ID=your_access_key
   AWS_SECRET_ACCESS_KEY=your_secret_key
   ```

3. **Docker 배포**
   ```bash
   docker build -t text-to-sql-app .
   docker run -p 8501:8501 --env-file .env text-to-sql-app
   ```

## 성능 및 최적화

### 캐싱 전략

- **스키마 캐싱**: 데이터베이스 스키마 정보 캐시 (TTL: 1시간)
- **세션 캐싱**: 활성 세션 정보 메모리 캐시
- **메시지 캐싱**: 최근 메시지 및 검색 결과 캐시

### 데이터 최적화

- **자동 압축**: 1KB 이상 메시지 자동 압축 (zlib)
- **배치 처리**: DynamoDB 배치 작업 지원
- **페이지네이션**: 대용량 데이터 조회 시 페이지네이션

### 성능 모니터링

- **LangSmith**: AI 모델 성능 추적
- **DynamoDB 메트릭**: 읽기/쓰기 용량 모니터링
- **응답 시간**: 사용자 경험 메트릭

## 주요 파일 경로

- **UI 컴포넌트**:
  - 메인 애플리케이션: `ui/app.py`
  - 왼쪽 사이드바 컴포넌트: `ui/sidebar.py`
  - 오른쪽 사이드바 컴포넌트: `ui/rightbar.py` ⭐ 신규
  - 메시지 컴포넌트: `ui/message.py`
- LangGraph 워크플로우: `modules/langraph_workflow.py`
- 상태 정의: `modules/states/graph_state.py`
- 노드 함수들:
  - `modules/states/generate_sql.py`
  - `modules/states/execute_query.py`
  - `modules/states/analyze_results.py`
- 유틸리티 모듈들:
  - `modules/utils/db_connector.py`
  - `modules/utils/db_utils.py`
  - `modules/utils/state_utils.py`
  - `modules/utils/streaming_callback.py`
  - `modules/utils/logging_utils.py`
  - `modules/utils/schema_cache.py`
  - `modules/utils/sql_tools.py`
- 채팅 히스토리 모듈들:
  - `modules/utils/server_id_manager.py`
  - `modules/utils/dynamodb_connector.py`
  - `modules/utils/dynamodb_crud.py`
  - `modules/utils/session_manager.py`
  - `modules/utils/message_history_manager.py`
  - `modules/utils/langgraph_state_manager.py`
- 시각화 모듈: `modules/visualization.py`
- 설정 관리: `config.py`
