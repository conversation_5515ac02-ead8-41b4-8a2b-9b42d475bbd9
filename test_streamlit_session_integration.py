"""
Streamlit 세션 관리 통합 테스트

이 스크립트는 Streamlit 없이 세션 관리 기능을 테스트합니다.
"""

from modules.utils import *

print('=== Streamlit 세션 관리 통합 테스트 ===')

# 1. 세션 매니저 테스트
session_manager = get_session_manager()
print(f'✅ 세션 매니저 인스턴스 생성: {type(session_manager).__name__}')

# 2. 메시지 히스토리 매니저 테스트
message_manager = get_message_history_manager()
print(f'✅ 메시지 히스토리 매니저 인스턴스 생성: {type(message_manager).__name__}')

# 3. 테스트 세션 생성
session = session_manager.create_new_session("Streamlit 통합 테스트 세션")
if session:
    print(f'✅ 테스트 세션 생성: {session.session_id}')
    
    # 4. 사용자 메시지 저장 테스트
    user_message = message_manager.save_message(
        session.session_id,
        MessageType.USER,
        "월별 매출 통계를 보여주세요",
        "user"
    )
    
    if user_message:
        print(f'✅ 사용자 메시지 저장: {user_message.message_id}')
        
        # 5. AI 응답 저장 테스트
        ai_message = message_manager.save_message(
            session.session_id,
            MessageType.AI,
            """다음 SQL 쿼리로 월별 매출 통계를 조회할 수 있습니다:

```sql
SELECT 
    DATE_FORMAT(order_date, '%Y-%m') as month,
    SUM(total_amount) as total_sales,
    COUNT(*) as order_count
FROM orders 
WHERE order_date >= '2024-01-01'
GROUP BY DATE_FORMAT(order_date, '%Y-%m')
ORDER BY month;
```

이 쿼리는 2024년부터의 월별 매출 합계와 주문 건수를 보여줍니다.""",
            "assistant",
            metadata={
                "contains_sql_query": True,
                "has_data": False,
                "has_graph": False,
                "query_type": "SELECT",
                "tables_used": ["orders"],
                "columns_used": {"orders": ["order_date", "total_amount"]},
                "graph_type": "bar"
            }
        )
        
        if ai_message:
            print(f'✅ AI 응답 저장: {ai_message.message_id}')
            
            # 6. 세션 메시지 조회 테스트
            messages, pagination = message_manager.get_session_messages(session.session_id)
            print(f'✅ 세션 메시지 조회: {len(messages)}개 메시지')
            
            for i, msg in enumerate(messages, 1):
                print(f'   {i}. [{msg.message_type.value}] {msg.content[:50]}...')
            
            # 7. 세션 통계 테스트
            stats = message_manager.get_message_statistics(session.session_id)
            if stats:
                print(f'✅ 세션 통계 조회:')
                print(f'   - 총 메시지: {stats["total_messages"]}개')
                print(f'   - 질문: {stats.get("questions", 0)}개')
                print(f'   - SQL 쿼리: {stats.get("messages_with_sql", 0)}개')
            
            # 8. 세션 내보내기 테스트
            export_data = session_manager.export_session(session.session_id, format='json')
            if export_data:
                print(f'✅ 세션 내보내기: {len(export_data)} 바이트')
            
            # 9. 세션 목록 조회 테스트
            recent_sessions = session_manager.get_recent_sessions(limit=5)
            print(f'✅ 최근 세션 목록: {len(recent_sessions)}개 세션')
            
            for i, s in enumerate(recent_sessions, 1):
                print(f'   {i}. {s.title} ({s.session_id[:8]}...)')
            
            print('\n🎉 모든 테스트 통과!')
            print('Streamlit UI에서 세션 관리 기능을 사용할 준비가 완료되었습니다.')
            
        else:
            print('❌ AI 응답 저장 실패')
    else:
        print('❌ 사용자 메시지 저장 실패')
else:
    print('❌ 테스트 세션 생성 실패')

print('\n=== 테스트 완료 ===') 