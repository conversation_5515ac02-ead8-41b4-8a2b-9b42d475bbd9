# .env 로 복사하여 사용, 몇개는 직접 채워 넣어야 함. 
OPENAI_API_KEY={{사이트에서 발급받아라. 모르겠으면 공유함.}}

MYSQL_HOST=db-logis.logisteq.com
MYSQL_PORT=9311
MYSQL_USER=root
MYSQL_PASSWORD={{잘쓰는 비밀번호 로지스텍 + 숫자 4자리 }}
MYSQL_DATABASE=tms_dev

# LangSmith 설정
LANGCHAIN_TRACING_V2=true
LANGSMITH_API_KEY={{없어도됨.따로문의}}
LANGSMITH_PROJECT=text_to_sql_app


# OpenAI 모델 설정
SQL_QUERY_MODEL=gpt-4o-mini
DATA_ANALYSIS_MODEL=gpt-4o-mini
SQL_QUERY_MODEL=gpt-4o-mini

# 허용된 테이블 목록 (쉼표로 구분)
ALLOWED_TABLES=rider,delivery,rider_info,delivery_allocation,delivery_detail


