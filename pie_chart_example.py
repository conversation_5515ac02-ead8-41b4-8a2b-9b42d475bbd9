import streamlit as st
import pandas as pd
import numpy as np
import sys
import os

# 모듈 경로 추가
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from modules.visualization import st_generate_pie_chart

st.title("Streamlit 파이 차트 예제")
st.caption("st_generate_pie_chart 함수 활용")

# 예제 1: 기본 파이 차트
st.subheader("1. 기본 파이 차트")

# 기본 데이터 생성
basic_data = pd.DataFrame({
    "카테고리": ["A", "B", "C", "D", "E"],
    "값": [25, 40, 15, 10, 30]
})

# 기본 파이 차트 그리기
st_generate_pie_chart(basic_data, names="카테고리", values="값")

# 예제 2: 자동 컬럼 선택
st.subheader("2. 자동 컬럼 선택")

# 자동 컬럼 선택을 테스트하기 위한 데이터
auto_data = pd.DataFrame({
    "제품": ["전자제품", "의류", "화장품", "식품", "가구"],
    "판매량": [120, 85, 65, 95, 40]
})

# 컬럼을 명시적으로 지정하지 않고 파이 차트 그리기
st_generate_pie_chart(auto_data)

# 예제 3: 타이틀과 색상 지정
st.subheader("3. 타이틀과 색상 지정")

# 타이틀과 색상을 지정한 파이 차트
st_generate_pie_chart(
    auto_data,
    names="제품",
    values="판매량",
    title="제품 카테고리별 판매 비율",
    color_discrete_sequence=["#FF9999", "#66B2FF", "#99FF99", "#FFCC99", "#FF99CC"],
    height=400
)

# 예제 4: 도넛 차트
st.subheader("4. 도넛 차트 (Plotly)")

# 도넛 차트 생성 (Plotly를 직접 사용)
import plotly.express as px

# 데이터 생성
donut_data = pd.DataFrame({
    "성별": ["남성", "여성"],
    "사용자 수": [5842, 6301]
})

# 도넛 차트 생성
fig = px.pie(
    donut_data,
    names="성별",
    values="사용자 수",
    title="사용자 성별 분포",
    hole=0.4,  # 도넛 차트에 필요한 hole 파라미터
    color_discrete_sequence=["#3366CC", "#FF6699"]
)

fig.update_traces(textposition='inside', textinfo='percent+label')
st.plotly_chart(fig, use_container_width=True)

# 예제 5: 복잡한 데이터
st.subheader("5. 복잡한 데이터")

# 여러 컬럼이 있는 복잡한 데이터
complex_data = pd.DataFrame({
    "지역": ["서울", "부산", "인천", "대구", "광주", "대전", "울산"],
    "사용자 수": [2500, 1800, 1200, 900, 700, 800, 600],
    "매출액": [45000, 38000, 25000, 18000, 15000, 16000, 12000],
    "평균 사용 시간": [85, 72, 65, 58, 62, 60, 55],
    "가입자": [3200, 2300, 1500, 1100, 900, 950, 750]
})

# 컬럼 선택 옵션
value_column = st.selectbox(
    "값 컬럼 선택",
    ["사용자 수", "매출액", "평균 사용 시간", "가입자"]
)

# 선택한 컬럼으로 파이 차트 생성
st_generate_pie_chart(
    complex_data,
    names="지역",
    values=value_column,
    title=f"지역별 {value_column} 비율"
) 