variable "aws_region" {
  description = "AWS region"
  type        = string
  default     = "ap-northeast-2"
}

variable "environment" {
  description = "Environment name"
  type        = string
  default     = "development"
}

variable "sessions_table_name" {
  description = "DynamoDB sessions table name"
  type        = string
  default     = "text-to-sql-sessions"
}

variable "messages_table_name" {
  description = "DynamoDB messages table name"
  type        = string
  default     = "text-to-sql-messages"
} 