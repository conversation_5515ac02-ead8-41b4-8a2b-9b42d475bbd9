# DynamoDB Infrastructure with Terraform

이 디렉토리는 Text-to-SQL 애플리케이션의 채팅 히스토리 기능을 위한 DynamoDB 인프라를 Terraform으로 관리합니다.

## 구성 요소

### DynamoDB 테이블

1. **Sessions Table** (`text-to-sql-sessions`)

   - 파티션 키: `server_id` (String)
   - 정렬 키: `session_id` (String)
   - GSI: `CreatedAtIndex` (server_id, created_at)
   - TTL: `expires_at` 필드 기반 자동 만료

2. **Messages Table** (`text-to-sql-messages`)
   - 파티션 키: `session_key` (String) - `server_id#session_id` 형태
   - 정렬 키: `timestamp` (String)
   - GSI: `MessageTypeIndex` (session_key, message_type)
   - TTL: `expires_at` 필드 기반 자동 만료

### IAM 역할 및 정책

- **Role**: `text-to-sql-app-role`
- **Policy**: DynamoDB 테이블에 대한 읽기/쓰기 권한

## 사용 방법

### 1. 사전 요구사항

```bash
# Terraform 설치 (macOS)
brew install terraform

# AWS CLI 설치 및 설정
brew install awscli
aws configure
```

### 2. 인프라 배포

```bash
# 1. Terraform 초기화
cd terraform
terraform init

# 2. 배포 계획 확인
terraform plan

# 3. 인프라 배포
terraform apply

# 배포 확인 후 'yes' 입력
```

### 3. 환경별 배포

```bash
# 개발 환경
terraform apply -var="environment=development"

# 스테이징 환경
terraform apply -var="environment=staging"

# 프로덕션 환경
terraform apply -var="environment=production"
```

### 4. 커스텀 테이블 이름 사용

```bash
terraform apply \
  -var="sessions_table_name=my-sessions-table" \
  -var="messages_table_name=my-messages-table"
```

### 5. 인프라 제거

```bash
terraform destroy
```

## 출력 값

배포 완료 후 다음 값들이 출력됩니다:

- `sessions_table_name`: 세션 테이블 이름
- `messages_table_name`: 메시지 테이블 이름
- `sessions_table_arn`: 세션 테이블 ARN
- `messages_table_arn`: 메시지 테이블 ARN
- `app_role_arn`: 애플리케이션 IAM 역할 ARN

## 비용 최적화

- **Pay-per-request 모드**: 사용량에 따른 과금으로 초기 비용 절약
- **TTL 설정**: 오래된 데이터 자동 삭제로 스토리지 비용 절약
- **GSI 최소화**: 필요한 인덱스만 생성하여 비용 최적화

## 보안 고려사항

- IAM 역할은 최소 권한 원칙 적용
- DynamoDB 테이블에 대한 세분화된 권한 설정
- 환경별 리소스 분리

## 모니터링

AWS CloudWatch를 통해 다음 메트릭을 모니터링할 수 있습니다:

- 읽기/쓰기 용량 사용량
- 스로틀링 이벤트
- 사용자 오류 및 시스템 오류
- TTL 삭제 메트릭
