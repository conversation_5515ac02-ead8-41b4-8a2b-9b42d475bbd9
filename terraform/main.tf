terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

provider "aws" {
  region = var.aws_region
}

# 세션 테이블
resource "aws_dynamodb_table" "sessions" {
  name           = var.sessions_table_name
  billing_mode   = "PAY_PER_REQUEST"
  hash_key       = "server_id"
  range_key      = "session_id"

  attribute {
    name = "server_id"
    type = "S"
  }

  attribute {
    name = "session_id"
    type = "S"
  }

  attribute {
    name = "created_at"
    type = "S"
  }

  global_secondary_index {
    name     = "CreatedAtIndex"
    hash_key = "server_id"
    range_key = "created_at"
  }

  ttl {
    attribute_name = "expires_at"
    enabled        = true
  }

  tags = {
    Name        = "TextToSQL-Sessions"
    Environment = var.environment
    Project     = "text-to-sql-app"
  }
}

# 메시지 테이블
resource "aws_dynamodb_table" "messages" {
  name           = var.messages_table_name
  billing_mode   = "PAY_PER_REQUEST"
  hash_key       = "session_key"
  range_key      = "timestamp"

  attribute {
    name = "session_key"
    type = "S"
  }

  attribute {
    name = "timestamp"
    type = "S"
  }

  attribute {
    name = "message_type"
    type = "S"
  }

  global_secondary_index {
    name     = "MessageTypeIndex"
    hash_key = "session_key"
    range_key = "message_type"
  }

  ttl {
    attribute_name = "expires_at"
    enabled        = true
  }

  tags = {
    Name        = "TextToSQL-Messages"
    Environment = var.environment
    Project     = "text-to-sql-app"
  }
}

# IAM 역할 (애플리케이션용)
resource "aws_iam_role" "app_role" {
  name = "text-to-sql-app-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "ec2.amazonaws.com"
        }
      }
    ]
  })
}

# IAM 정책
resource "aws_iam_role_policy" "app_policy" {
  name = "text-to-sql-app-policy"
  role = aws_iam_role.app_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "dynamodb:GetItem",
          "dynamodb:PutItem",
          "dynamodb:UpdateItem",
          "dynamodb:DeleteItem",
          "dynamodb:Query",
          "dynamodb:Scan",
          "dynamodb:BatchGetItem",
          "dynamodb:BatchWriteItem"
        ]
        Resource = [
          aws_dynamodb_table.sessions.arn,
          aws_dynamodb_table.messages.arn,
          "${aws_dynamodb_table.sessions.arn}/index/*",
          "${aws_dynamodb_table.messages.arn}/index/*"
        ]
      }
    ]
  })
} 