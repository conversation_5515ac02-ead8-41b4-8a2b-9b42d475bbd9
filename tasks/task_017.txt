# Task ID: 17
# Title: Figma 기반 UI/UX 개선 구현
# Status: in-progress
# Dependencies: 5, 16
# Priority: medium
# Description: Figma 디자인 시안을 기반으로 애플리케이션의 사용자 인터페이스를 개선합니다. 시각적 일관성, 사용성 및 접근성을 향상시키는 데 중점을 둡니다. 피그마 파일(https://www.figma.com/design/mZj2i7KVDvYTdcy9YxjEcy/Carta-Assistant?node-id=54-781&t=jBNHCT8R1tw0tI2l-4)을 참조하여 작업을 진행합니다.
# Details:
1. Figma 디자인 시스템 설정 및 검토:
   - Figma 프로젝트 설정 및 디자인 시스템 구성 (색상, 타이포그래피, 컴포넌트 등)
   - 기존 UI 요소 분석 및 개선점 식별
   - 모바일 및 데스크톱 반응형 디자인 고려

2. 핵심 UI 컴포넌트 개선 (3가지 주요 영역으로 분할):
   a. 왼쪽 사이드 바 개선:
      - 네비게이션 메뉴 구조 및 시각적 디자인 개선
      - 사용자 프로필 및 설정 접근성 향상
      - 채팅 히스토리 목록 디자인 및 상호작용 개선
   
   b. 채팅창 UI 개선:
      - 메시지 버블, 입력 필드, 전송 버튼 등 시각적 개선
      - 사용자와 AI 응답 구분을 위한 시각적 차별화
      - 로딩 상태 및 오류 메시지 디자인 개선
      - 메시지 타임스탬프 및 상태 표시 개선
   
   c. 오른쪽 표/그래프 표시 창 개선:
      - SQL 쿼리 결과 표시 방식 개선 (테이블, 차트 등)
      - 데이터 시각화 컴포넌트 디자인 및 상호작용 개선
      - 필터링 및 정렬 기능의 UI 개선
      - 데이터 내보내기 및 공유 기능 UI 설계

3. Streamlit 적용 구현:
   - Figma 디자인을 Streamlit CSS 커스터마이징으로 변환
   - 커스텀 CSS 및 HTML 요소 통합
   - Streamlit 테마 설정 및 글로벌 스타일 적용
   - 컴포넌트별 스타일 적용 (st.markdown, st.columns 등 활용)

4. 사용성 개선:
   - 직관적인 네비게이션 및 정보 계층 구조 설계
   - 도움말 및 가이드 요소 통합
   - 키보드 접근성 및 스크린 리더 호환성 개선
   - 다크/라이트 모드 지원 (선택 사항)

5. 성능 최적화:
   - 이미지 및 아이콘 최적화
   - CSS 최적화 및 불필요한 스타일 제거
   - 페이지 로딩 시간 개선을 위한 전략 구현

# Test Strategy:
1. 디자인 충실도 검증:
   - Figma 디자인과 실제 구현된 UI를 시각적으로 비교하여 일치성 확인
   - 색상, 간격, 타이포그래피, 아이콘 등 디자인 요소의 정확한 구현 검증
   - 반응형 디자인이 다양한 화면 크기에서 의도한 대로 작동하는지 확인

2. 영역별 UI 테스트:
   - 왼쪽 사이드바 기능 및 디자인 검증
   - 채팅창 UI 요소 및 상호작용 테스트
   - 오른쪽 표/그래프 표시 창의 데이터 시각화 정확성 검증
   - 세 영역 간의 상호작용 및 일관성 확인

3. 크로스 브라우저 및 디바이스 테스트:
   - Chrome, Firefox, Safari, Edge 등 주요 브라우저에서 UI 테스트
   - 데스크톱, 태블릿, 모바일 등 다양한 디바이스에서 테스트
   - 다양한 화면 해상도에서 레이아웃 확인

4. 사용성 테스트:
   - 주요 사용자 흐름에 대한 태스크 완료 시간 측정
   - 5-10명의 테스트 사용자를 대상으로 개선된 UI에 대한 피드백 수집
   - System Usability Scale(SUS) 설문을 통한 사용성 점수 측정

5. 접근성 테스트:
   - WCAG 2.1 AA 기준 준수 여부 확인
   - 스크린 리더 호환성 테스트
   - 키보드 탐색 및 포커스 상태 테스트
   - 색상 대비 검사

6. 성능 테스트:
   - 페이지 로드 시간 측정 (개선 전/후 비교)
   - CSS 및 자바스크립트 파일 크기 확인
   - 모바일 기기에서의 렌더링 성능 확인

# Subtasks:
## 17.1. 왼쪽 사이드 바 UI 개선 구현 [completed]
### Dependencies: None
### Description: Figma 디자인을 참조하여 왼쪽 사이드 바의 UI를 개선합니다.
### Details:


## 17.2. 채팅창 UI 개선 구현 [pending]
### Dependencies: None
### Description: Figma 디자인을 참조하여 채팅 인터페이스의 UI를 개선합니다. 사이드바와의 일관성을 유지하며 Pretendard 폰트 및 디자인 시스템을 적용합니다.
### Details:


## 17.3. 오른쪽 표/그래프 표시 창 UI 개선 구현 [pending]
### Dependencies: None
### Description: Figma 디자인을 참조하여 데이터 시각화 및 결과 표시 영역의 UI를 개선합니다. 사이드바 및 채팅창과의 디자인 일관성을 유지합니다.
### Details:


