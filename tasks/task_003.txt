# Task ID: 3
# Title: LangGraph 워크플로우 상태 모델 구현
# Status: done
# Dependencies: 1
# Priority: high
# Description: LangGraph 기반 오케스트레이션을 위한 워크플로우 상태 모델을 정의하고 구현합니다.
# Details:
워크플로우 상태를 위한 TypedDict 또는 Pydantic 모델을 정의합니다: query (str), schema_info (str), sql (str), data (DataFrame), graph (Any), error (str), content (str). 워크플로우 관리를 위해 langgraph.graph.StateGraph를 사용합니다. 기본 상태 전환을 구현합니다.

# Test Strategy:
상태 모델 생성 및 업데이트를 테스트합니다. 간단한 워크플로우에서 상태 전환을 검증합니다.
