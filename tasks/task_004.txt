# Task ID: 4
# Title: Text-to-SQL 변환을 위한 OpenAI API 통합
# Status: pending
# Dependencies: 1, 2, 3
# Priority: high
# Description: 자연어를 SQL 쿼리로 변환하기 위해 OpenAI GPT-4o를 통합합니다.
# Details:
openai 라이브러리를 사용하여 OpenAI API 클라이언트를 구현합니다. 데이터베이스 스키마 컨텍스트를 포함한 Text-to-SQL 변환용 프롬프트 템플릿을 설계합니다. API 오류 및 속도 제한을 처리합니다. 효율성을 위해 스키마 정보를 캐시합니다.

# Test Strategy:
샘플 쿼리로 Text-to-SQL 변환을 테스트합니다. 오류 처리 및 스키마 컨텍스트 사용을 검증합니다.
