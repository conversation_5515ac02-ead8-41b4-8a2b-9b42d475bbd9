# Task ID: 1
# Title: 프로젝트 저장소 및 개발 환경 설정
# Status: done
# Dependencies: None
# Priority: high
# Description: 프로젝트 저장소를 초기화하고, Python 가상 환경을 설정하며, 핵심 의존성을 설치합니다.
# Details:
새로운 Git 저장소를 생성합니다. Python 3.9+ 가상 환경을 설정합니다. 필요한 라이브러리를 설치합니다: langchain-core (최신), langgraph (최신), openai (최신), streamlit (최신), pandas (최신), mysql-connector-python (최신), sqlalchemy (최신), matplot<PERSON>b (최신), seaborn (최신), plotly (최신). pip를 사용하여 설치합니다. README에 환경 설정을 문서화합니다.

# Test Strategy:
모든 패키지가 오류 없이 설치되는지 확인합니다. Python 버전이 3.9 이상인지 확인합니다. 기본 스크립트 실행을 테스트합니다.
