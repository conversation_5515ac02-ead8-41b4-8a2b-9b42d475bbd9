pipeline {
    agent any

    environment {
        // Docker Registry Configuration
        DOCKER_REGISTRY = 'harbor.logisteq.com'
        DOCKER_CREDENTIALS_ID = 'harbor-login'
        IMAGE_NAME = 'carta/ai-chatbot'

        // Build Configuration
        BUILD_TAG = new Date().format('yyyyMMdd-HHmmss')

        // Git Configuration
        GIT_CREDENTIALS_ID = 'gitlab-login'
        GIT_REPOSITORY = 'gitlab.logisteq.com/carta/fms/aichat.git'
        GIT_BRANCH = 'main'

        // Deployment Configuration
        DEPLOY_GIT_REPOSITORY = 'gitlab.logisteq.com/infra/dev-k8s-cfgstore.git'
        DEPLOY_GIT_BRANCH = 'main'
        DEPLOY_FILE_PATH = 'basic-helm-chart/ai-chatbot-values.yaml'

        // URL Configuration
        PREFIX_HTTPS = 'https://'
    }

    tools {
        jdk 'jdk-17'
    }

    stages {
        stage('Git Clone') {
            steps {
                script {
                    echo '🔄 Cloning source repository...'
                    git branch: "${GIT_BRANCH}",
                        credentialsId: "${GIT_CREDENTIALS_ID}",
                        url: "${PREFIX_HTTPS}${GIT_REPOSITORY}"
                    echo '✅ Source code cloned successfully'
                }
            }
        }

        stage('Build Docker Image') {
            steps {
                script {
                    try {
                        echo "🏗️ Building Docker image: ${IMAGE_NAME}"
                        def customImage = docker.build("${IMAGE_NAME}", '.')

                        // Tag the image with both latest and build timestamp
                        customImage.tag('latest')
                        customImage.tag("${BUILD_TAG}")

                        echo "✅ Docker image built successfully with tags: latest, ${BUILD_TAG}"
                    } catch (Exception e) {
                        error "❌ Failed to build Docker image: ${e.getMessage()}"
                    }
                }
            }
        }

        stage('Push to Registry') {
            steps {
                script {
                    try {
                        echo "📤 Pushing image to Harbor registry: ${DOCKER_REGISTRY}"

                        // Login to Harbor registry and push images
                        docker.withRegistry("${PREFIX_HTTPS}${DOCKER_REGISTRY}", "${DOCKER_CREDENTIALS_ID}") {
                            docker.image("${IMAGE_NAME}").push("${BUILD_TAG}")
                            docker.image("${IMAGE_NAME}").push('latest')
                        }

                        echo '✅ Images pushed successfully to registry'
                    } catch (Exception e) {
                        error "❌ Failed to push images to registry: ${e.getMessage()}"
                    }
                }
            }
        }

        stage('Update Deployment Configuration') {
            steps {
                script {
                    try {
                        echo '🔄 Updating deployment configuration...'

                        // Clone deployment repository
                        dir('deploy-repo') {
                            git branch: "${DEPLOY_GIT_BRANCH}",
                                credentialsId: "${GIT_CREDENTIALS_ID}",
                                url: "${PREFIX_HTTPS}${DEPLOY_GIT_REPOSITORY}"

                            // Configure Git and update image tag
                            withCredentials([usernamePassword(
                                credentialsId: "${GIT_CREDENTIALS_ID}",
                                usernameVariable: 'GIT_USERNAME',
                                passwordVariable: 'GIT_PASSWORD'
                            )]) {
                                configureGitAndUpdateTag()
                            }
                        }

                        echo '✅ Deployment configuration updated successfully'
                    } catch (Exception e) {
                        error "❌ Failed to update deployment configuration: ${e.getMessage()}"
                    }
                }
            }
        }

        stage('Pipeline Complete') {
            steps {
                script {
                    echo '🎉 CI/CD Pipeline completed successfully!'
                    echo '📋 Summary:'
                    echo "   - Image: ${IMAGE_NAME}:${BUILD_TAG}"
                    echo "   - Registry: ${DOCKER_REGISTRY}"
                    echo "   - Deployment file updated: ${DEPLOY_FILE_PATH}"
                }
            }
        }
    }

    post {
        always {
            script {
                echo '🧹 Cleaning up workspace...'
                // Clean up Docker images to save space
                sh """
                    docker rmi ${IMAGE_NAME}:${BUILD_TAG} || true
                    docker rmi ${IMAGE_NAME}:latest || true
                """
            }
        }

        success {
            echo '✅ Pipeline executed successfully!'
        }

        failure {
            echo '❌ Pipeline failed. Please check the logs for details.'
        }
    }
}

// Helper function to configure Git and update deployment tag
def configureGitAndUpdateTag() {
    sh """
        # Configure Git user
        git config user.email '<EMAIL>'
        git config user.name 'jenkins'
        git config pull.rebase false

        echo "📝 Updating image tag in ${DEPLOY_FILE_PATH}..."

        # Update image tag using Groovy script for better reliability
        groovy -e "
            def file = new File('${DEPLOY_FILE_PATH}')
            if (!file.exists()) {
                throw new FileNotFoundException('Deployment file not found: ${DEPLOY_FILE_PATH}')
            }

            def content = file.text
            def newContent = content.replaceAll(/tag: \\".*\\"/, 'tag: \\"${BUILD_TAG}\\"')

            if (content == newContent) {
                println 'Warning: No tag pattern found to update'
            } else {
                file.write(newContent)
                println 'Image tag updated successfully'
            }
        "

        # Commit and push changes
        git add .
        git commit -m "🚀 Update AI chatbot image tag to ${BUILD_TAG}

        - Image: ${IMAGE_NAME}:${BUILD_TAG}
        - Build time: \$(date)
        - Jenkins job: \${JOB_NAME} #\${BUILD_NUMBER}"

        echo "📤 Pushing changes to deployment repository..."
        git push ${PREFIX_HTTPS}\${GIT_USERNAME}:\${GIT_PASSWORD}@${DEPLOY_GIT_REPOSITORY} ${DEPLOY_GIT_BRANCH}
    """
}
