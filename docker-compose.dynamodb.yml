services:
  dynamodb-local:
    command: "-jar DynamoDBLocal.jar -sharedDb -dbPath ./data"
    image: "amazon/dynamodb-local:latest"
    container_name: dynamodb-local
    ports:
      - "8000:8000"
    volumes:
      - "./docker/dynamodb:/home/<USER>/data"
    working_dir: /home/<USER>
    environment:
      - JAVA_OPTS=-Xmx256m
    networks:
      - dynamodb-network

  dynamodb-admin:
    image: "aaronshaf/dynamodb-admin:latest"
    container_name: dynamodb-admin
    ports:
      - "8001:8001"
    environment:
      - DYNAMO_ENDPOINT=http://dynamodb-local:8000
      - AWS_REGION=ap-northeast-2
      - AWS_ACCESS_KEY_ID=test
      - AWS_SECRET_ACCESS_KEY=test
    depends_on:
      - dynamodb-local
    networks:
      - dynamodb-network

networks:
  dynamodb-network:
    driver: bridge

volumes:
  dynamodb-data: 