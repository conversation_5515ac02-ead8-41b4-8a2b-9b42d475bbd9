"""
설정 관리 모듈

이 모듈은 애플리케이션의 설정(API 키, 데이터베이스 연결 정보 등)을 관리합니다.
"""

import os
import streamlit as st
from typing import Dict, Any, Optional
import logging
from dotenv import load_dotenv

# .env 파일 로드 (파일이 존재하는 경우에만 성공)
load_dotenv()

# 로거 설정
logger = logging.getLogger(__name__)

class Config:
    """설정 관리 클래스"""
    
    # 유효한 OpenAI 모델 목록
    VALID_MODELS = [
        "gpt-4o", "gpt-4o-mini", "gpt-4", "gpt-4-turbo"
    ]

    @staticmethod
    def get_openai_api_key() -> str:
        """
        OpenAI API 키를 가져옵니다.

        Returns:
            OpenAI API 키
        """
        # 1. 환경 변수에서 API 키 가져오기
        api_key = os.getenv("OPENAI_API_KEY")
        if api_key:
            logger.info("환경 변수에서 OpenAI API 키를 가져왔습니다.")
            return api_key

        # 2. 세션 상태에서 API 키 가져오기
        if "openai_api_key" in st.session_state:
            logger.info("세션 상태에서 OpenAI API 키를 가져왔습니다.")
            return st.session_state.openai_api_key

        logger.warning("OpenAI API 키를 찾을 수 없습니다.")
        return ""

    @staticmethod
    def get_langsmith_api_key() -> str:
        """
        LangSmith API 키를 가져옵니다.

        Returns:
            LangSmith API 키
        """
        # 우선순위: 환경 변수 > 세션 상태
        api_key = os.getenv("LANGSMITH_API_KEY")
        if api_key:
            logger.info("환경 변수에서 LangSmith API 키를 가져왔습니다.")
            return api_key
        
        if "langsmith_api_key" in st.session_state:
            logger.info("세션 상태에서 LangSmith API 키를 가져왔습니다.")
            return st.session_state.langsmith_api_key
        
        logger.warning("LangSmith API 키를 찾을 수 없습니다.")
        return ""

    @staticmethod
    def get_db_config() -> Dict[str, str]:
        """
        데이터베이스 연결 설정을 가져옵니다.

        Returns:
            데이터베이스 연결 설정 딕셔너리
        """
        # 우선순위: 환경 변수 > 세션 상태 > 기본값
        config = {
            "host": "localhost",
            "port": 3306,
            "user": "root",
            "password": "",
            "database": "example_db"
        }

        # 환경 변수에서 가져오기 (.env 파일의 변수명과 일치시킴)
        mysql_host = os.getenv("MYSQL_HOST")
        if mysql_host:
            # 호스트와 포트 분리 처리
            if ":" in mysql_host:
                host_parts = mysql_host.split(":")
                config["host"] = host_parts[0]
                if len(host_parts) > 1 and host_parts[1].isdigit():
                    config["port"] = int(host_parts[1])
            else:
                config["host"] = mysql_host

        mysql_port = os.getenv("MYSQL_PORT")
        if mysql_port and mysql_port.isdigit():
            config["port"] = int(mysql_port)
            
        mysql_user = os.getenv("MYSQL_USER")
        if mysql_user:
            config["user"] = mysql_user
            
        mysql_password = os.getenv("MYSQL_PASSWORD")
        if mysql_password:
            config["password"] = mysql_password
            
        mysql_database = os.getenv("MYSQL_DATABASE")
        if mysql_database:
            config["database"] = mysql_database

        # DB_* 환경 변수도 계속 지원 (이전 코드 호환성)
        db_host = os.getenv("DB_HOST")
        if db_host:
            # 호스트와 포트 분리 처리
            if ":" in db_host:
                host_parts = db_host.split(":")
                config["host"] = host_parts[0]
                if len(host_parts) > 1 and host_parts[1].isdigit():
                    config["port"] = int(host_parts[1])
            else:
                config["host"] = db_host

        db_port = os.getenv("DB_PORT")
        if db_port and db_port.isdigit():
            config["port"] = int(db_port)
            
        db_user = os.getenv("DB_USER")
        if db_user:
            config["user"] = db_user
            
        db_password = os.getenv("DB_PASSWORD")
        if db_password:
            config["password"] = db_password
            
        db_name = os.getenv("DB_NAME")
        if db_name:
            config["database"] = db_name

        # 세션 상태에서 가져오기
        if "db_host" in st.session_state:
            # 호스트와 포트 분리 처리
            host_value = st.session_state.db_host
            if ":" in host_value:
                host_parts = host_value.split(":")
                config["host"] = host_parts[0]
                if len(host_parts) > 1:
                    try:
                        config["port"] = int(host_parts[1])
                    except ValueError:
                        pass
            else:
                config["host"] = host_value

        if "db_port" in st.session_state:
            config["port"] = st.session_state.db_port
        if "db_user" in st.session_state:
            config["user"] = st.session_state.db_user
        if "db_password" in st.session_state:
            config["password"] = st.session_state.db_password
        if "db_name" in st.session_state:
            config["database"] = st.session_state.db_name

        return config

    @staticmethod
    def get_dynamodb_config() -> Dict[str, Any]:
        """
        DynamoDB 연결 설정을 가져옵니다.

        Returns:
            DynamoDB 연결 설정 딕셔너리
        """
        # 로깅 설정
        logger = logging.getLogger(__name__)
        
        config = {
            "region_name": "ap-northeast-2",
            "endpoint_url": None,  # None이면 AWS DynamoDB 사용, 값이 있으면 로컬 DynamoDB 사용
            "aws_access_key_id": None,
            "aws_secret_access_key": None,
            "aws_session_token": None,
            "sessions_table": "text_to_sql_sessions",
            "messages_table": "text_to_sql_messages"
        }

        # 환경 변수에서 설정 가져오기
        aws_region = os.getenv("AWS_REGION")
        if aws_region:
            config["region_name"] = aws_region
            logger.info(f"환경 변수에서 AWS_REGION 설정: {aws_region}")

        endpoint_url = os.getenv("DYNAMODB_ENDPOINT_URL")
        if endpoint_url:
            config["endpoint_url"] = endpoint_url
            logger.info(f"환경 변수에서 DYNAMODB_ENDPOINT_URL 설정: {endpoint_url}")

        aws_access_key_id = os.getenv("AWS_ACCESS_KEY_ID")
        if aws_access_key_id:
            config["aws_access_key_id"] = aws_access_key_id
            logger.info("환경 변수에서 AWS_ACCESS_KEY_ID 설정됨")

        aws_secret_access_key = os.getenv("AWS_SECRET_ACCESS_KEY")
        if aws_secret_access_key:
            config["aws_secret_access_key"] = aws_secret_access_key
            logger.info("환경 변수에서 AWS_SECRET_ACCESS_KEY 설정됨")

        aws_session_token = os.getenv("AWS_SESSION_TOKEN")
        if aws_session_token:
            config["aws_session_token"] = aws_session_token
            logger.info("환경 변수에서 AWS_SESSION_TOKEN 설정됨")

        sessions_table = os.getenv("DYNAMODB_SESSIONS_TABLE")
        if sessions_table:
            config["sessions_table"] = sessions_table
            logger.info(f"환경 변수에서 DYNAMODB_SESSIONS_TABLE 설정: {sessions_table}")

        messages_table = os.getenv("DYNAMODB_MESSAGES_TABLE")
        if messages_table:
            config["messages_table"] = messages_table
            logger.info(f"환경 변수에서 DYNAMODB_MESSAGES_TABLE 설정: {messages_table}")

        # 세션 상태에서 설정 가져오기
        if "dynamodb_region" in st.session_state:
            config["region_name"] = st.session_state.dynamodb_region
            logger.info(f"세션 상태에서 DynamoDB 리전 설정: {st.session_state.dynamodb_region}")
        if "dynamodb_endpoint_url" in st.session_state:
            config["endpoint_url"] = st.session_state.dynamodb_endpoint_url
            logger.info(f"세션 상태에서 DynamoDB 엔드포인트 설정: {st.session_state.dynamodb_endpoint_url}")
        if "dynamodb_sessions_table" in st.session_state:
            config["sessions_table"] = st.session_state.dynamodb_sessions_table
            logger.info(f"세션 상태에서 DynamoDB 세션 테이블 설정: {st.session_state.dynamodb_sessions_table}")
        if "dynamodb_messages_table" in st.session_state:
            config["messages_table"] = st.session_state.dynamodb_messages_table
            logger.info(f"세션 상태에서 DynamoDB 메시지 테이블 설정: {st.session_state.dynamodb_messages_table}")

        # 최종 설정 로깅
        logger.info(f"DynamoDB 설정 완료 - 리전: {config['region_name']}, 엔드포인트: {config['endpoint_url']}, 세션 테이블: {config['sessions_table']}, 메시지 테이블: {config['messages_table']}")
        
        return config

    @staticmethod
    def get_server_config() -> Dict[str, Any]:
        """
        서버 식별 및 세션 관리 설정을 가져옵니다.

        Returns:
            서버 설정 딕셔너리
        """
        config = {
            "server_id": "auto",
            "server_domain": "localhost",
            "session_ttl_days": 30,
            "max_sessions_per_server": 100,
            "message_retention_days": 90,
            "environment": "development",
            "debug": False
        }

        # 환경 변수에서 설정 가져오기
        server_id = os.getenv("SERVER_ID")
        if server_id:
            config["server_id"] = server_id

        server_domain = os.getenv("SERVER_DOMAIN")
        if server_domain:
            config["server_domain"] = server_domain

        session_ttl_days = os.getenv("SESSION_TTL_DAYS")
        if session_ttl_days and session_ttl_days.isdigit():
            config["session_ttl_days"] = int(session_ttl_days)

        max_sessions = os.getenv("MAX_SESSIONS_PER_SERVER")
        if max_sessions and max_sessions.isdigit():
            config["max_sessions_per_server"] = int(max_sessions)

        message_retention = os.getenv("MESSAGE_RETENTION_DAYS")
        if message_retention and message_retention.isdigit():
            config["message_retention_days"] = int(message_retention)

        environment = os.getenv("ENVIRONMENT")
        if environment:
            config["environment"] = environment

        debug = os.getenv("DEBUG")
        if debug:
            config["debug"] = debug.lower() in ["true", "1", "yes"]

        # 세션 상태에서 설정 가져오기
        if "server_id" in st.session_state:
            config["server_id"] = st.session_state.server_id
        if "server_domain" in st.session_state:
            config["server_domain"] = st.session_state.server_domain
        if "session_ttl_days" in st.session_state:
            config["session_ttl_days"] = st.session_state.session_ttl_days

        return config

    @staticmethod
    def get_schema_cache_config() -> Dict[str, Any]:
        """
        스키마 캐시 설정을 가져옵니다.

        Returns:
            스키마 캐시 설정 딕셔너리
        """
        # 기본 캐시 설정
        default_config = {
            "enabled": True,
            "ttl": 3600  # 기본 TTL: 1시간 (초 단위)
        }

        # 환경 변수에서 캐시 설정 가져오기
        schema_cache_enabled = os.getenv("SCHEMA_CACHE_ENABLED")
        if schema_cache_enabled:
            default_config["enabled"] = schema_cache_enabled.lower() in ["true", "1", "yes"]

        schema_cache_ttl = os.getenv("SCHEMA_CACHE_TTL")
        if schema_cache_ttl:
            try:
                default_config["ttl"] = int(schema_cache_ttl)
            except ValueError:
                pass

        # 세션 상태에서 캐시 설정 가져오기
        if "schema_cache_enabled" in st.session_state:
            default_config["enabled"] = st.session_state.schema_cache_enabled

        if "schema_cache_ttl" in st.session_state:
            default_config["ttl"] = st.session_state.schema_cache_ttl

        return default_config

    @staticmethod
    def get_model_config() -> Dict[str, str]:
        """
        OpenAI 모델 설정을 가져옵니다.

        Returns:
            모델 설정 딕셔너리
        """
        # 로깅 설정
        logger = logging.getLogger(__name__)
        
        # 기본 모델 설정 - 유효한 OpenAI 모델명 사용
        default_models = {
            "sql_query": "gpt-4o-mini",
            "data_analysis": "gpt-4o-mini"
        }

        # 모델명 유효성 검증 함수
        def validate_model(model_name):
            # 정확히 일치하는 모델명인 경우
            if model_name in Config.VALID_MODELS:
                return model_name
            
            # 대소문자 구분없이 정확히 일치하는지 확인
            for valid_model in Config.VALID_MODELS:
                if model_name.lower() == valid_model.lower():
                    logger.warning(f"대소문자가 다른 모델명 '{model_name}'가 감지되었습니다. '{valid_model}'로 대체합니다.")
                    return valid_model
            
            # 부분 문자열 매칭 제거하고 정확한 모델명만 허용
            logger.error(f"지원되지 않는 모델명 '{model_name}'가 감지되었습니다. 기본 모델 'gpt-4o-mini'로 대체합니다.")
            return "gpt-4o-mini"

        try:
            # 1. 환경변수에서 모델 설정 가져오기 (가장 높은 우선순위)
            # 명시적으로 os.getenv를 사용하여 환경변수 로드
            
            # 디버깅: 모든 환경변수 출력
            all_env_vars = sorted([(k, v) for k, v in os.environ.items() if 'SQL' in k or 'GPT' in k or 'MODEL' in k])
            logger.info(f"관련 환경변수 목록: {all_env_vars}")
            
            env_sql_model = os.getenv("SQL_QUERY_MODEL")
            
            # 디버깅: 다른 방식으로도 환경변수 확인
            alt_env_value = os.environ.get("SQL_QUERY_MODEL")
            logger.info(f"환경변수 로드 방식 비교: os.getenv={env_sql_model}, os.environ.get={alt_env_value}")
            
            logger.info(f"환경변수 로드 ====>   env_sql_model : {env_sql_model}")

            if env_sql_model:
                logger.info(f"환경변수에서 SQL_QUERY_MODEL 설정 발견: {env_sql_model}")
                # 값 정리 (줄바꿈, 공백 등 처리)
                clean_model = env_sql_model.strip()
                
                # 중복 환경변수 확인 - 환경변수 값이 여러 줄로 설정된 경우 마지막 값 사용
                if '\n' in clean_model:
                    models = [m.strip() for m in clean_model.split('\n') if m.strip()]
                    if models:
                        last_model = models[-1]
                        logger.warning(f"SQL_QUERY_MODEL에 여러 값이 발견되었습니다. 마지막 값을 사용합니다: {last_model}")
                        default_models["sql_query"] = validate_model(last_model)
                else:
                    validated_model = validate_model(clean_model)
                    logger.info(f"SQL_QUERY_MODEL 검증 결과: {clean_model} -> {validated_model}")
                    default_models["sql_query"] = validated_model
                    
            env_data_model = os.getenv("DATA_ANALYSIS_MODEL")
            logger.info(f"환경변수 로드 ====>   env_data_model : {env_data_model}")
            
            if env_data_model:
                logger.info(f"환경변수에서 DATA_ANALYSIS_MODEL 설정 발견: {env_data_model}")
                # 값 정리 (줄바꿈, 공백 등 처리)
                clean_model = env_data_model.strip()
                
                # 중복 환경변수 확인 - 환경변수 값이 여러 줄로 설정된 경우 마지막 값 사용
                if '\n' in clean_model:
                    models = [m.strip() for m in clean_model.split('\n') if m.strip()]
                    if models:
                        last_model = models[-1]
                        logger.warning(f"DATA_ANALYSIS_MODEL에 여러 값이 발견되었습니다. 마지막 값을 사용합니다: {last_model}")
                        default_models["data_analysis"] = validate_model(last_model)
                else:
                    validated_model = validate_model(clean_model)
                    logger.info(f"DATA_ANALYSIS_MODEL 검증 결과: {clean_model} -> {validated_model}")
                    default_models["data_analysis"] = validated_model

            # 2. 세션 상태에서 모델 설정 가져오기 (환경변수보다 낮은 우선순위)
            if "sql_query_model" in st.session_state and not env_sql_model:
                raw_model = st.session_state.sql_query_model
                logger.info(f"세션 상태에서 SQL_QUERY_MODEL 설정 발견(검증 전): {raw_model}")
                validated_model = validate_model(raw_model)
                if validated_model != raw_model:
                    logger.warning(f"세션 상태의 SQL_QUERY_MODEL({raw_model})이 유효하지 않아 {validated_model}으로 교체됩니다.")
                default_models["sql_query"] = validated_model
                logger.info(f"세션 상태에서 SQL_QUERY_MODEL 설정 최종 적용: {default_models['sql_query']}")

            if "data_analysis_model" in st.session_state and not env_data_model:
                raw_model = st.session_state.data_analysis_model
                logger.info(f"세션 상태에서 DATA_ANALYSIS_MODEL 설정 발견(검증 전): {raw_model}")
                validated_model = validate_model(raw_model)
                if validated_model != raw_model:
                    logger.warning(f"세션 상태의 DATA_ANALYSIS_MODEL({raw_model})이 유효하지 않아 {validated_model}으로 교체됩니다.")
                default_models["data_analysis"] = validated_model
                logger.info(f"세션 상태에서 DATA_ANALYSIS_MODEL 설정 최종 적용: {default_models['data_analysis']}")
        except Exception as e:
            logger.error(f"환경변수 로드 중 오류 발생: {str(e)}")

        logger.info(f"최종 선택된 모델 설정 - SQL: {default_models['sql_query']}, 데이터 분석: {default_models['data_analysis']}")
        
        return default_models

    @staticmethod
    def save_session_config(
        openai_api_key: Optional[str] = None,
        langsmith_api_key: Optional[str] = None,
        langsmith_tracing: Optional[bool] = None,
        langsmith_project: Optional[str] = None,
        db_host: Optional[str] = None,
        db_user: Optional[str] = None,
        db_password: Optional[str] = None,
        db_name: Optional[str] = None,
        db_port: Optional[int] = None,
        sql_query_model: Optional[str] = None,
        data_analysis_model: Optional[str] = None,
        schema_cache_enabled: Optional[bool] = None,
        schema_cache_ttl: Optional[int] = None,
        # DynamoDB 설정 추가
        dynamodb_region: Optional[str] = None,
        dynamodb_endpoint_url: Optional[str] = None,
        dynamodb_sessions_table: Optional[str] = None,
        dynamodb_messages_table: Optional[str] = None
    ) -> None:
        """
        설정을 세션 상태에 저장합니다.

        Args:
            openai_api_key: OpenAI API 키
            langsmith_api_key: LangSmith API 키
            langsmith_tracing: LangSmith 추적 활성화 여부
            langsmith_project: LangSmith 프로젝트 이름
            db_host: 데이터베이스 호스트
            db_user: 데이터베이스 사용자
            db_password: 데이터베이스 비밀번호
            db_name: 데이터베이스 이름
            db_port: 데이터베이스 포트
            sql_query_model: SQL 쿼리 생성에 사용할 모델
            data_analysis_model: 데이터 분석에 사용할 모델
            schema_cache_enabled: 스키마 캐시 활성화 여부
            schema_cache_ttl: 스키마 캐시 TTL (초)
            dynamodb_region: DynamoDB 리전
            dynamodb_endpoint_url: DynamoDB 엔드포인트 URL (로컬 개발용)
            dynamodb_sessions_table: DynamoDB 세션 테이블명
            dynamodb_messages_table: DynamoDB 메시지 테이블명
        """
        if openai_api_key is not None:
            st.session_state.openai_api_key = openai_api_key
            os.environ["OPENAI_API_KEY"] = openai_api_key

        if langsmith_api_key is not None:
            st.session_state.langsmith_api_key = langsmith_api_key
            os.environ["LANGSMITH_API_KEY"] = langsmith_api_key

        if langsmith_tracing is not None:
            os.environ["LANGSMITH_TRACING"] = "true" if langsmith_tracing else "false"

        if langsmith_project is not None:
            os.environ["LANGSMITH_PROJECT"] = langsmith_project

        if db_host is not None:
            st.session_state.db_host = db_host

        if db_port is not None:
            st.session_state.db_port = db_port

        if db_user is not None:
            st.session_state.db_user = db_user

        if db_password is not None:
            st.session_state.db_password = db_password

        if db_name is not None:
            st.session_state.db_name = db_name

        if sql_query_model is not None:
            # 모델명 검증
            if sql_query_model not in Config.VALID_MODELS:
                logger.warning(f"잘못된 SQL 쿼리 모델명 '{sql_query_model}'이 감지되었습니다. 'gpt-4o-mini'로 대체합니다.")
                sql_query_model = "gpt-4o-mini"
            
            st.session_state.sql_query_model = sql_query_model
            logger.info(f"세션 상태에 SQL 쿼리 모델 저장: {sql_query_model}")

        if data_analysis_model is not None:
            # 모델명 검증
            if data_analysis_model not in Config.VALID_MODELS:
                logger.warning(f"잘못된 데이터 분석 모델명 '{data_analysis_model}'이 감지되었습니다. 'gpt-4o-mini'로 대체합니다.")
                data_analysis_model = "gpt-4o-mini"
                
            st.session_state.data_analysis_model = data_analysis_model
            logger.info(f"세션 상태에 데이터 분석 모델 저장: {data_analysis_model}")

        if schema_cache_enabled is not None:
            st.session_state.schema_cache_enabled = schema_cache_enabled

        if schema_cache_ttl is not None:
            st.session_state.schema_cache_ttl = schema_cache_ttl

        # DynamoDB 설정 저장
        if dynamodb_region is not None:
            st.session_state.dynamodb_region = dynamodb_region
            logger.info(f"세션 상태에 DynamoDB 리전 저장: {dynamodb_region}")

        if dynamodb_endpoint_url is not None:
            st.session_state.dynamodb_endpoint_url = dynamodb_endpoint_url
            logger.info(f"세션 상태에 DynamoDB 엔드포인트 저장: {dynamodb_endpoint_url}")

        if dynamodb_sessions_table is not None:
            st.session_state.dynamodb_sessions_table = dynamodb_sessions_table
            logger.info(f"세션 상태에 DynamoDB 세션 테이블 저장: {dynamodb_sessions_table}")

        if dynamodb_messages_table is not None:
            st.session_state.dynamodb_messages_table = dynamodb_messages_table
            logger.info(f"세션 상태에 DynamoDB 메시지 테이블 저장: {dynamodb_messages_table}")

    @staticmethod
    def get_default_values() -> Dict[str, str]:
        """
        UI에 표시할 기본 값을 가져옵니다.

        Returns:
            기본값 딕셔너리
        """
        try:
            db_config = Config.get_db_config()
            cache_config = Config.get_schema_cache_config()
            model_config = Config.get_model_config()
            dynamodb_config = Config.get_dynamodb_config()
            return {
                "openai_api_key": Config.get_openai_api_key(),
                "langsmith_api_key": Config.get_langsmith_api_key(),
                "langsmith_tracing": os.environ.get("LANGSMITH_TRACING", "false"),
                "langsmith_project": os.environ.get("LANGSMITH_PROJECT", "text_to_sql_app"),
                "db_host": db_config["host"],
                "db_port": str(db_config["port"]),
                "db_user": db_config["user"],
                "db_password": db_config["password"],
                "db_name": db_config["database"],
                "schema_cache_enabled": str(cache_config["enabled"]).lower(),
                "schema_cache_ttl": str(cache_config["ttl"]),
                "sql_query_model": model_config["sql_query"],
                "data_analysis_model": model_config["data_analysis"],
                # DynamoDB 설정 추가
                "dynamodb_region": dynamodb_config["region_name"],
                "dynamodb_endpoint_url": dynamodb_config["endpoint_url"] or "",
                "dynamodb_sessions_table": dynamodb_config["sessions_table"],
                "dynamodb_messages_table": dynamodb_config["messages_table"]
            }
        except Exception as e:
            print(f"기본값 로드 오류 (기본값 사용): {str(e)}")
            # 오류 발생 시 하드코딩된 기본값 반환
            return {
                "openai_api_key": "",
                "langsmith_api_key": "",
                "langsmith_tracing": "false",
                "langsmith_project": "text_to_sql_app",
                "db_host": "localhost",
                "db_port": "3306",
                "db_user": "root",
                "db_password": "",
                "db_name": "example_db",
                "schema_cache_enabled": "true",
                "schema_cache_ttl": "3600",
                "sql_query_model": "gpt-4o",
                "data_analysis_model": "gpt-4o",
                # DynamoDB 기본값 추가
                "dynamodb_region": "ap-northeast-2",
                "dynamodb_endpoint_url": "",
                "dynamodb_sessions_table": "text_to_sql_sessions",
                "dynamodb_messages_table": "text_to_sql_messages"
            }

    @staticmethod
    def get_langsmith_config() -> Dict[str, Any]:
        """
        LangSmith 설정을 가져옵니다.

        Returns:
            LangSmith 설정 딕셔너리
        """
        # 기본 설정
        config = {
            "tracing": False,
            "project": "text_to_sql_app",
            "api_key": Config.get_langsmith_api_key()
        }

        # 환경 변수에서 설정 가져오기
        langsmith_tracing = os.getenv("LANGSMITH_TRACING")
        if langsmith_tracing:
            config["tracing"] = langsmith_tracing.lower() in ["true", "1", "yes"]

        langsmith_project = os.getenv("LANGSMITH_PROJECT")
        if langsmith_project:
            config["project"] = langsmith_project

        # 세션 상태에서 설정 가져오기
        if "langsmith_tracing" in st.session_state:
            config["tracing"] = st.session_state.langsmith_tracing

        if "langsmith_project" in st.session_state:
            config["project"] = st.session_state.langsmith_project

        return config
