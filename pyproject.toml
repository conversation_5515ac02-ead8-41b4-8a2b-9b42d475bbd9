[project]
name = "text-to-sql-app"
version = "0.1.0"
description = "자연어 질문을 SQL 쿼리로 변환하여 MySQL 데이터베이스 조회 및 분석"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "streamlit>=1.44.0",
    "pandas>=2.2.3",
    "numpy>=2.2.0",
    "sqlalchemy>=2.0.28",
    "mysql-connector-python>=9.3.0",
    "pymysql>=1.1.0",
    "langchain>=0.3.22",
    "langchain-core>=0.2.0",
    "langchain-openai>=0.3.12",
    "langgraph>=0.3.0",
    "langchain-community>=0.3.20",
    "langsmith>=0.1.5",
    "openai>=0.3.12",
    "matplotlib>=3.9.0",
    "seaborn>=0.13.2",
    "python-dotenv>=1.0.0",
    "pydantic>=2.0.0",
    "typing-extensions>=4.10.0",
    "chromadb>=0.6.3",
    "markdown>=3.4.3",
    "bs4>=0.0.1",
    "beautifulsoup4>=4.12.2",
    "fastapi>=0.104.1",
    "uvicorn>=0.24.0",
    "python-multipart>=0.0.6",
    "typer>=0.9.0",
    "rich>=13.6.0",
    "langchain-chroma>=0.1.0",
    "jinja2>=3.1.2",
    "pymupdf4llm>=0.0.20",
    "psutil>=5.9.0",
    "plotly>=6.0.1",
    "boto3>=1.38.24",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["ui", "modules", "engine"]
